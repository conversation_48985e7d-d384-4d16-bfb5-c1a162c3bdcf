# Client-Side Real-Time Detection Rendering

This system renders AI detection results (bounding boxes, blur effects) entirely in the browser using the Canvas API. No need to download processed images from the backend - just fetch the lightweight JSON metadata and render in real-time!

## Overview

**Flow:**
1. User uploads image → Stored in Supabase `user-uploads` bucket
2. Backend processes image → Generates JSON detection metadata → Stores in `results` bucket
3. Frontend fetches JSON metadata from Supabase
4. Frontend renders original image with detections using Canvas API
5. User adjusts threshold/blur → Re-renders instantly in the browser

**Benefits:**
- ⚡ **Instant adjustments** - No backend round-trips needed
- 📉 **Reduced bandwidth** - Download 5KB JSON instead of 5MB processed image
- 🎨 **Flexible rendering** - Users can customize visualization on the fly
- 💰 **Cost savings** - Less storage and bandwidth usage

## File Structure

```
src/
├── services/
│   └── detectionMetadata.ts          # Fetch and process detection JSON
├── components/
│   └── detection/
│       ├── DetectionCanvas.tsx        # Canvas-based renderer
│       └── DetectionViewer.tsx        # Complete viewer with controls
```

## Components

### 1. DetectionCanvas

Low-level canvas component that renders detections on an image.

**Props:**
- `imageUrl` - URL to original image (from Supabase)
- `detections` - Array of processed detection objects
- `scoreThreshold` - Confidence threshold (0-1)
- `blurStrength` - Blur intensity in pixels (5-100)
- `showBoundingBoxes` - Toggle bounding box rendering
- `showLabels` - Toggle label rendering
- `blurDetections` - Toggle blur effect

**Features:**
- Renders original image
- Draws bounding boxes with distinct colors
- Displays labels with confidence scores
- Applies blur to detected regions
- Automatically handles CORS for Supabase images

### 2. DetectionViewer

High-level viewer with built-in controls and UI.

**Props:**
- `imageUrl` - URL to original image
- `metadataPath` - Path to JSON file in Supabase results bucket
- `initialThreshold` - Starting confidence threshold (default: 0.3)
- `initialBlurStrength` - Starting blur strength (default: 35px)

**Features:**
- Automatic metadata fetching from Supabase
- Interactive controls for threshold and blur
- Toggle buttons for boxes, labels, and blur
- Detection count and statistics
- Sortable detection list
- Loading and error states

## Usage Examples

### Basic Usage

```tsx
import { DetectionViewer } from '@/components/detection/DetectionViewer';

export default function ResultsPage() {
  const imageUrl = 'https://your-supabase-url/storage/v1/object/public/user-uploads/user_123/uploads/image.jpg';
  const metadataPath = 'user_123/detections/job_456_metadata.json';

  return (
    <DetectionViewer
      imageUrl={imageUrl}
      metadataPath={metadataPath}
      initialThreshold={0.3}
      initialBlurStrength={35}
    />
  );
}
```

### With Custom Settings

```tsx
<DetectionViewer
  imageUrl={imageUrl}
  metadataPath={metadataPath}
  initialThreshold={0.5}      // Higher threshold - fewer detections
  initialBlurStrength={50}    // Stronger blur effect
/>
```

### Low-Level Canvas Usage

```tsx
import { DetectionCanvas } from '@/components/detection/DetectionCanvas';
import { fetchAndProcessDetections } from '@/services/detectionMetadata';

function CustomViewer() {
  const [detections, setDetections] = useState([]);

  useEffect(() => {
    fetchAndProcessDetections(metadataPath, 0.1).then(({ detections }) => {
      setDetections(detections);
    });
  }, [metadataPath]);

  return (
    <DetectionCanvas
      imageUrl={imageUrl}
      detections={detections}
      scoreThreshold={0.3}
      blurStrength={25}
      showBoundingBoxes={true}
      showLabels={true}
      blurDetections={false}
    />
  );
}
```

## API Reference

### detectionMetadata.ts

#### `fetchDetectionMetadata(metadataPath: string): Promise<DetectionMetadata>`

Fetches raw JSON detection metadata from Supabase.

**Parameters:**
- `metadataPath` - Path to JSON file in results bucket (e.g., "user_123/detections/job_456_metadata.json")

**Returns:**
```typescript
{
  filename: string;
  detection_all: {
    box_ids: string[];
    labels: string[];
    boxes: number[][];   // [[x1, y1, x2, y2], ...]
    scores: number[];
  }
}
```

#### `processDetections(metadata: DetectionMetadata, scoreThreshold: number): ProcessedDetection[]`

Processes raw metadata into a more usable format with filtering.

**Returns:**
```typescript
[{
  box_id: string;
  label: string;
  x1: number;
  y1: number;
  x2: number;
  y2: number;
  width: number;
  height: number;
  score: number;
}, ...]
```

#### `fetchAndProcessDetections(metadataPath: string, scoreThreshold: number): Promise<{metadata, detections}>`

Convenience function that fetches and processes in one call.

## Detection JSON Format

The backend generates JSON files in this format:

```json
{
  "filename": "1760896755771_test",
  "detection_all": {
    "box_ids": ["8910e3ff", "045e58ab", "3aaf89e3", "2a6a14cd"],
    "labels": ["person", "person", "person", "person"],
    "boxes": [
      [133.32, 37.34, 980.58, 671.78],
      [611.12, 0.76, 995.31, 395.92],
      [1100.55, 529.23, 1124.85, 672.36],
      [616.70, 284.01, 836.69, 462.57]
    ],
    "scores": [0.61, 0.28, 0.14, 0.11]
  }
}
```

**Box format:** `[x1, y1, x2, y2]` where:
- `(x1, y1)` = Top-left corner
- `(x2, y2)` = Bottom-right corner

## Integration with Backend

### Backend Endpoint

Your FastAPI backend provides `/process-image-detection` endpoint:

```python
POST /process-image-detection
{
  "file_path": "user_320WoCx7oaj7coakpK117Liq8Rk/uploads/1760045705437_city.jpg",
  "text_prompt": "person",
  "score_threshold": 0.1
}
```

**Response:**
```json
{
  "job_id": "uuid",
  "status": "completed",
  "metadata": {...},
  "metadata_path": "user_320WoCx7oaj7coakpK117Liq8Rk/detections/job_uuid_metadata.json",
  "detection_count": 4,
  "processing_time": 1.23
}
```

### Frontend Integration Flow

```typescript
// 1. Submit image to backend for detection
const response = await fetch('https://your-backend.com/process-image-detection', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${clerkToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    file_path: uploadedFilePath,
    text_prompt: 'person',
    score_threshold: 0.1
  })
});

const result = await response.json();

// 2. Use DetectionViewer with returned metadata_path
<DetectionViewer
  imageUrl={originalImageUrl}
  metadataPath={result.metadata_path}
  initialThreshold={0.3}
/>
```

## Performance Considerations

### Image Loading

- Images are loaded with `crossOrigin='anonymous'` for CORS support
- Supabase signed URLs work out of the box
- Canvas automatically scales to fit container

### Memory Usage

- Canvas creates temporary canvases for blur effects
- Memory footprint: ~4 bytes per pixel
- Example: 1920x1080 image = ~8MB canvas memory
- Use `willReadFrequently` context option for heavy pixel manipulation

### Rendering Performance

- Bounding boxes: Very fast (<1ms for 50 boxes)
- Blur effects: Moderate (10-50ms depending on region size)
- Real-time threshold adjustment: Instant re-render

### Optimization Tips

```typescript
// For many detections, debounce threshold changes
const debouncedThreshold = useMemo(
  () => debounce((value) => setThreshold(value), 100),
  []
);

// For large images, consider downscaling
canvas.width = Math.min(image.width, 1920);
canvas.height = (canvas.width / image.width) * image.height;
```

## Styling and Customization

### Colors

Bounding boxes use HSL color space with golden angle for maximum distinction:

```typescript
const hue = (index * 137) % 360;  // Golden angle = 137.5°
const color = `hsl(${hue}, 70%, 50%)`;
```

### Custom Styling

```tsx
// Override canvas styling
<DetectionCanvas
  ...
  className="rounded-xl shadow-2xl"
/>

// Custom control colors
<div className="bg-custom-dark border-custom-light">
  <DetectionViewer ... />
</div>
```

## Troubleshooting

### CORS Errors

**Problem:** "Tainted canvas" or CORS errors

**Solution:** Ensure images have proper CORS headers:
```typescript
img.crossOrigin = 'anonymous';
```

For Supabase, use signed URLs or configure CORS in bucket settings.

### Blur Not Working

**Problem:** Blur effect not visible

**Solution:**
- Check `blurDetections` is enabled
- Increase `blurStrength` (try 50-100px)
- Verify detections overlap image regions

### Performance Issues

**Problem:** Slow rendering with many detections

**Solution:**
- Increase score threshold to reduce detection count
- Disable blur effect for real-time adjustments
- Use `debounce` for slider changes

### Metadata Not Loading

**Problem:** "Failed to fetch detection metadata"

**Solution:**
- Verify `metadataPath` is correct
- Check Supabase bucket permissions (should be readable)
- Ensure JSON file exists in results bucket
- Check browser console for detailed errors

## Examples

### Example 1: Privacy Mode (Blur Faces)

```tsx
<DetectionViewer
  imageUrl={imageUrl}
  metadataPath={metadataPath}
  initialThreshold={0.3}
  initialBlurStrength={50}
  // User can toggle blur on/off
/>
```

### Example 2: High-Precision Analysis

```tsx
<DetectionViewer
  imageUrl={imageUrl}
  metadataPath={metadataPath}
  initialThreshold={0.7}  // Only high-confidence detections
  initialBlurStrength={25}
/>
```

### Example 3: Custom Detection Display

```tsx
import { DetectionCanvas } from '@/components/detection/DetectionCanvas';

function CustomDisplay() {
  const [showBoxes, setShowBoxes] = useState(true);
  const [showLabels, setShowLabels] = useState(false);

  return (
    <div>
      <DetectionCanvas
        imageUrl={imageUrl}
        detections={detections}
        scoreThreshold={0.5}
        blurStrength={0}
        showBoundingBoxes={showBoxes}
        showLabels={showLabels}
        blurDetections={false}
      />
      <button onClick={() => setShowBoxes(!showBoxes)}>
        Toggle Boxes
      </button>
    </div>
  );
}
```

## Future Enhancements

Potential improvements:
- 🎨 Custom color schemes for bounding boxes
- 📊 Heatmap visualization
- 🎬 Video frame-by-frame playback
- 📥 Export processed image to file
- 🔍 Click-to-zoom on detections
- 🏷️ Edit/remove individual detections
- 📊 Statistics dashboard (class distribution, etc.)

## Support

For issues or questions:
- Check browser console for errors
- Verify Supabase bucket permissions
- Ensure backend is returning correct JSON format
- Test with sample JSON file first

---

**Happy Detecting! 🎯**
