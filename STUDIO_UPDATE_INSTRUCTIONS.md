# Studio Update Instructions - Switch to New Detection Endpoint

## The Problem

Your studio is currently using:
```typescript
// OLD CODE (in GuardiaVisionStudio.tsx)
import { useGuardiaVisionProcessing } from '@/hooks/useGuardiaVisionProcessing';

const { processMedia, isProcessing } = useGuardiaVisionProcessing();

// This calls the OLD /process endpoint → saves .jpg to Supabase
await processMedia(filePath, 'image', { text_prompt: 'person' });
```

## The Solution

Replace it with:
```typescript
// NEW CODE
import { useImageDetection } from '@/hooks/useImageDetection';
import { LiveDetectionViewer } from '@/components/detection/LiveDetectionViewer';

const { detect, isDetecting, result } = useImageDetection();

// This calls the NEW /process-image-detection endpoint → returns JSON
await detect(filePath, 'person', 0.1);
```

## Step-by-Step Changes

### 1. Open Your Studio File
```bash
src/components/studio/GuardiaVisionStudio.tsx
```

### 2. Replace the Import (Line 10)

**REMOVE:**
```typescript
import { useGuardiaVisionProcessing } from '@/hooks/useGuardiaVisionProcessing';
```

**ADD:**
```typescript
import { useImageDetection } from '@/hooks/useImageDetection';
import { LiveDetectionViewer } from '@/components/detection/LiveDetectionViewer';
```

### 3. Replace the Hook Usage (Around Line 63-71)

**REMOVE:**
```typescript
const {
  processMedia: runpodProcessMedia,
  isProcessing: runpodIsProcessing,
  currentJob: runpodCurrentJob,
  downloadUrl: runpodDownloadUrl,
  error: runpodError,
  reset: runpodReset,
} = useGuardiaVisionProcessing();
```

**ADD:**
```typescript
const {
  detect,
  isDetecting: isDetecting,
  result: detectionResult,
  error: detectionError,
  reset: resetDetection,
} = useImageDetection();
```

### 4. Find Your "Process" Button Handler

Look for where you call `processMedia()` or similar. It might look like:

**OLD CODE:**
```typescript
const handleProcess = async () => {
  await runpodProcessMedia(mediaFile.file_path, 'image', {
    text_prompt: 'person',
    score_threshold: 0.3,
    blur_detections: true,
    blur_strength: 35,
  });
};
```

**NEW CODE:**
```typescript
const handleDetect = async () => {
  await detect(
    mediaFile.file_path,  // e.g., "user_123/uploads/image.jpg"
    'person',             // text_prompt
    0.1                   // score_threshold (backend filter)
  );
};
```

### 5. Update Your Render/Display Logic

**OLD CODE (showing processed image):**
```typescript
{runpodDownloadUrl && (
  <img src={runpodDownloadUrl} alt="Processed" />
)}
```

**NEW CODE (rendering detections client-side):**
```typescript
{detectionResult && (
  <LiveDetectionViewer
    imageUrl={originalImageUrl}          // Original image from Supabase
    detectionMetadata={detectionResult.metadata}
    initialThreshold={0.3}               // User can adjust with slider
    initialBlurStrength={35}             // User can adjust with slider
  />
)}
```

### 6. Update Error Handling

**OLD:**
```typescript
{runpodError && <p className="text-red-400">{runpodError}</p>}
```

**NEW:**
```typescript
{detectionError && <p className="text-red-400">{detectionError}</p>}
```

### 7. Update Loading State

**OLD:**
```typescript
{runpodIsProcessing && <p>Processing...</p>}
```

**NEW:**
```typescript
{isDetecting && <p>Detecting objects...</p>}
```

## Complete Example Replacement

Here's a before/after for a typical processing section:

### BEFORE (Old Code):
```typescript
'use client';

import { useGuardiaVisionProcessing } from '@/hooks/useGuardiaVisionProcessing';

export default function GuardiaVisionStudio({ mediaFile, originalImageUrl }) {
  const { processMedia, isProcessing, downloadUrl, error } = useGuardiaVisionProcessing();

  const handleProcess = async () => {
    await processMedia(mediaFile.file_path, 'image', {
      text_prompt: 'person',
      blur_detections: true,
      blur_strength: 35,
    });
  };

  return (
    <div>
      <button onClick={handleProcess} disabled={isProcessing}>
        {isProcessing ? 'Processing...' : 'Process Image'}
      </button>

      {error && <p className="text-red-400">{error}</p>}

      {downloadUrl && (
        <img src={downloadUrl} alt="Processed" className="w-full" />
      )}
    </div>
  );
}
```

### AFTER (New Code):
```typescript
'use client';

import { useImageDetection } from '@/hooks/useImageDetection';
import { LiveDetectionViewer } from '@/components/detection/LiveDetectionViewer';

export default function GuardiaVisionStudio({ mediaFile, originalImageUrl }) {
  const { detect, isDetecting, result, error } = useImageDetection();

  const handleDetect = async () => {
    await detect(
      mediaFile.file_path,  // "user_123/uploads/image.jpg"
      'person',             // text_prompt
      0.1                   // score_threshold
    );
  };

  return (
    <div>
      <button onClick={handleDetect} disabled={isDetecting}>
        {isDetecting ? 'Detecting...' : 'Detect Objects'}
      </button>

      {error && <p className="text-red-400">{error}</p>}

      {result && (
        <div className="mt-6">
          <p className="text-green-400 mb-4">
            ✅ Found {result.detection_count} object(s) in {result.processing_time.toFixed(2)}s
          </p>

          <LiveDetectionViewer
            imageUrl={originalImageUrl}
            detectionMetadata={result.metadata}
            initialThreshold={0.3}
            initialBlurStrength={35}
          />
        </div>
      )}
    </div>
  );
}
```

## What You Get

After making these changes:

✅ **Instant results** - No polling, no waiting for job completion
✅ **JSON metadata only** - No `.jpg` files saved to Supabase results bucket
✅ **Client-side rendering** - Bounding boxes drawn in the browser
✅ **Real-time adjustments** - Users can change threshold/blur without re-processing
✅ **Better performance** - Download 5KB JSON instead of 5MB image

## Testing

1. Update your `.env` file:
```bash
NEXT_PUBLIC_RUNPOD_PROXY_URL=http://your-backend-url:8000
```

2. Restart your dev server:
```bash
npm run dev
```

3. Open your studio and click "Detect Objects"

4. Check browser console - you should see:
```
🔍 Starting object detection...
📁 File path: user_123/uploads/image.jpg
🎯 Prompt: person
📊 Threshold: 0.1
✅ Detection completed: { job_id: "...", detection_count: 1, processing_time: "0.88s" }
🎨 Rendering 1 detections (threshold: 0.3)
```

5. Check Supabase results bucket - you should see **only `.json` files**, no `.jpg` files

## Rollback Plan

If you need to rollback:

1. Keep both hooks imported:
```typescript
import { useGuardiaVisionProcessing } from '@/hooks/useGuardiaVisionProcessing';  // OLD
import { useImageDetection } from '@/hooks/useImageDetection';  // NEW
```

2. Use a feature flag:
```typescript
const USE_NEW_DETECTION = true;

const oldHook = useGuardiaVisionProcessing();
const newHook = useImageDetection();

const hook = USE_NEW_DETECTION ? newHook : oldHook;
```

## Support

- **Demo page:** Visit `/detection-example` in your app
- **Complete guide:** See `DETECTION_INTEGRATION_GUIDE.md`
- **API details:** See `CLIENT_SIDE_DETECTION_GUIDE.md`

---

**The frontend code is already built and working!** You just need to wire it up in your studio component. This should take about 15 minutes to update.
