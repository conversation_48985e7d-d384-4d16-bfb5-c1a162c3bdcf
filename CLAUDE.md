# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Guardiavision is a Next.js 14 SaaS boilerplate built with TypeScript, featuring Clerk authentication, Stripe billing, Supabase storage, and Drizzle ORM. The application supports internationalization and uses shadcn/ui components with Tailwind CSS.

## Development Commands

### Running the Application
```bash
npm run dev              # Start development server
npm run build            # Build for production
npm start                # Start production server
npm run clean            # Clean .next and out directories
```

### Database Operations
```bash
npm run db:generate      # Generate Drizzle migrations from schema changes
npm run db:migrate       # Run migrations (production environment)
npm run db:studio        # Open Drizzle Studio for database management
```

### Analysis
```bash
npm run build-stats      # Build with bundle analyzer
```

## Architecture

### Database Layer

The application uses **Drizzle ORM** with dual database support:
- **Production/Development with DATABASE_URL**: Uses PostgreSQL client with automatic migrations
- **Local/Build time without DATABASE_URL**: Falls back to PGlite (in-memory SQLite)

Database initialization happens in `src/libs/DB.ts`, which:
1. Checks for `DATABASE_URL` environment variable
2. Connects to PostgreSQL if available, otherwise uses PGlite
3. Automatically runs migrations from `/migrations` directory on startup

**Schema Definition**: All database tables are defined in `src/models/Schema.ts`. To modify the database:
1. Update `src/models/Schema.ts` with your changes
2. Run `npm run db:generate` to create migration files
3. Migrations are auto-applied on next server start (no manual migration required)

Current tables:
- `organization`: Stores organization data with Stripe subscription details
- `todo`: Example table with owner-based todos

### Authentication & Authorization

**Clerk** handles all authentication with the following configuration:

**Middleware** (`src/middleware.ts`):
- Combines Clerk auth + next-intl for internationalization
- Public routes: `/`, `/pricing`, `/terms-of-service`, `/privacy-policy`, webhooks
- Protected routes: `/dashboard/*`, `/studio/*` (require authentication)
- Redirects unauthenticated users to locale-aware sign-in pages

**Route Groups**:
- `src/app/[locale]/(auth)/*`: Protected routes (dashboard, studio, onboarding)
- `src/app/[locale]/(unauth)/*`: Public routes (landing, pricing)

**Webhooks**:
- Clerk webhook: `src/app/api/webhooks/clerk/route.ts`
- Stripe webhook: `src/app/api/webhooks/stripe/route.ts`

### Billing & Subscriptions

**Stripe Integration** with plan-based subscriptions defined in `src/utils/AppConfig.ts`:

Plans:
- **FREE**: `PLAN_ID.FREE`
- **PREMIUM**: `PLAN_ID.PREMIUM` (79/month)
- **ENTERPRISE**: `PLAN_ID.ENTERPRISE` (199/month)

Each plan has separate price IDs for test/dev/prod environments controlled by `BILLING_PLAN_ENV`.

The `organization` table tracks:
- `stripeCustomerId`
- `stripeSubscriptionId`
- `stripeSubscriptionPriceId`
- `stripeSubscriptionStatus`
- `stripeSubscriptionCurrentPeriodEnd`

### Internationalization (i18n)

Uses **next-intl** with configuration in `src/libs/i18n.ts`:
- Locale files: `src/locales/{locale}.json`
- Current locales: English (en) as default
- Locale prefix mode: `as-needed` (only adds locale prefix when non-default)
- Integration with Clerk middleware for locale-aware auth redirects

### Environment Variables

Type-safe environment validation using `@t3-oss/env-nextjs` in `src/libs/Env.ts`:

**Required Server Variables**:
- `CLERK_SECRET_KEY`
- `STRIPE_SECRET_KEY`
- `STRIPE_WEBHOOK_SECRET`
- `BILLING_PLAN_ENV`: 'dev' | 'test' | 'prod'

**Optional Server Variables**:
- `DATABASE_URL`: PostgreSQL connection string (falls back to PGlite if not set)
- `LOGTAIL_SOURCE_TOKEN`: For logging

**Required Client Variables**:
- `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY`
- `NEXT_PUBLIC_CLERK_SIGN_IN_URL`
- `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY`

**Optional Client Variables**:
- `NEXT_PUBLIC_APP_URL`

### File Storage

Supabase is configured for file storage with image upload capabilities:
- Configuration: `src/lib/supabase.ts`, `src/utils/supabase/*`
- Image optimization enabled via `next.config.js` for `*.supabase.co` and `*.supabase.com` domains
- Storage utilities: `src/utils/storage/*`

### Project Structure

```
src/
├── app/
│   ├── [locale]/
│   │   ├── (auth)/          # Protected routes (dashboard, studio, pricing, onboarding)
│   │   └── (unauth)/        # Public routes (landing, pricing pages)
│   └── api/                 # API routes including webhooks, studio operations, auth
├── components/              # React components (shadcn/ui + custom)
├── contexts/                # React contexts
├── features/                # Feature-based modules (auth, billing, dashboard, landing, sponsors)
├── hooks/                   # Custom React hooks
├── lib/                     # Library configurations (stripe, supabase, utils)
├── libs/                    # Core libraries (DB, Env, i18n, Logger, i18nNavigation)
├── locales/                 # i18n translation files
├── models/                  # Database schema (Drizzle)
├── services/                # Business logic services
├── styles/                  # Global styles
├── templates/               # Page templates
├── types/                   # TypeScript type definitions
└── utils/                   # Utility functions (AppConfig, storage, stripe, supabase)
```

### Key Configuration Files

- `src/libs/DB.ts`: Database initialization with Drizzle (dual PostgreSQL/PGlite support)
- `src/libs/Env.ts`: Type-safe environment variable validation
- `src/libs/i18n.ts`: Internationalization configuration
- `src/models/Schema.ts`: Database schema definitions
- `src/utils/AppConfig.ts`: Application configuration including pricing plans and locales
- `src/middleware.ts`: Clerk authentication + i18n middleware
- `next.config.js`: Next.js configuration with Clerk env vars and Supabase image optimization
- `tsconfig.json`: TypeScript configuration with path aliases (`@/*` → `src/*`)

## Development Workflow

### Adding a New Database Table
1. Add table definition to `src/models/Schema.ts` using Drizzle syntax
2. Run `npm run db:generate` to create migration file
3. Restart dev server (migrations auto-apply on startup)
4. Use `npm run db:studio` to verify changes

### Adding a New Pricing Plan
1. Add plan ID constant to `PLAN_ID` in `src/utils/AppConfig.ts`
2. Add plan configuration to `PricingPlanList` with test/dev/prod price IDs
3. Create corresponding Stripe prices (can use scripts or Stripe dashboard)
4. Update UI components to display the new plan

### Adding a New Locale
1. Add locale to `AppConfig.locales` in `src/utils/AppConfig.ts`
2. Create translation file in `src/locales/{locale}.json`
3. Crowdin integration will sync translations via GitHub Actions

### Working with Protected Routes
- All routes under `src/app/[locale]/(auth)/` are automatically protected
- Middleware redirects unauthenticated users to locale-specific sign-in
- Access user info via Clerk's `auth()` helper in Server Components

## Important Notes

- TypeScript and ESLint errors are ignored during builds (`ignoreBuildErrors: true`)
- Migrations run automatically on server startup - no manual migration command needed
- The app can run without a PostgreSQL database (uses PGlite fallback)
- Clerk webhooks handle user lifecycle events
- Stripe webhooks handle subscription lifecycle events
- Path alias `@/*` maps to `src/*` for imports
