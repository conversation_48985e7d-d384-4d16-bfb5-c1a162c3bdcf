# How to Change Prices - Complete Guide

This guide explains how to update pricing across your entire SaaS application. When you change a price, you need to update it in **3 places** to ensure everything works correctly.

---

## Overview: The 3 Places Where Prices Are Stored

1. **Stripe Dashboard** - Where you create the actual price IDs and manage billing
2. **Webhook Handler** - Maps price IDs to credits and plan names (`/src/app/api/webhooks/stripe/route.ts`)
3. **Frontend Pricing Page** - Displays prices to users (optional, if you show prices)

---

## Step-by-Step: How to Change a Price

### Step 1: Create New Price in Stripe Dashboard

1. Go to [Stripe Dashboard](https://dashboard.stripe.com)
2. Navigate to **Products**
3. Find your product (e.g., "Pro Plan")
4. Click **Add another price**
5. Configure the price:
   - **Amount**: e.g., €99 or €1 for testing
   - **Billing period**: Monthly or Yearly
   - **Currency**: EUR
6. Click **Save**
7. **Copy the Price ID** (looks like `price_1SKiHB8Ap4GWUvRfKvp8V5uz`)

### Step 2: Update Webhook Price Mappings

Open `/src/app/api/webhooks/stripe/route.ts` and update BOTH mappings:

```typescript
// Line ~10: Add the new price ID and its credit amount
const creditsPerPlan: { [key: string]: number } = {
    'price_1Rtczx8Ap4GWUvRfKowvGuGG': 700,      // Standard Monthly
    'price_1Rta8u8Ap4GWUvRfreOwBwhw': 3500,     // Pro Monthly
    'price_1RtaCC8Ap4GWUvRf30nF2vlN': 14000,    // Premium Monthly
    'price_1Rta9g8Ap4GWUvRfnjvHIO10': 8400,     // Standard Yearly
    'price_1RtaBl8Ap4GWUvRfDoGK2gdQ': 42000,    // Pro Yearly
    'price_1RtaCU8Ap4GWUvRfiTS7yl6i': 168000,   // Premium Yearly
    'price_YOUR_NEW_PRICE_ID': 50000,          // 👈 ADD YOUR NEW PRICE HERE
};

// Line ~19: Add the new price ID and its plan name
const planNamePerPlan: { [key: string]: string } = {
    'price_1Rtczx8Ap4GWUvRfKowvGuGG': 'Standard',
    'price_1Rta8u8Ap4GWUvRfreOwBwhw': 'Pro',
    'price_1RtaCC8Ap4GWUvRf30nF2vlN': 'Premium',
    'price_1Rta9g8Ap4GWUvRfnjvHIO10': 'Standard',
    'price_1RtaBl8Ap4GWUvRfDoGK2gdQ': 'Pro',
    'price_1RtaCU8Ap4GWUvRfiTS7yl6i': 'Premium',
    'price_YOUR_NEW_PRICE_ID': 'Pro',          // 👈 ADD YOUR NEW PRICE HERE
};
```

**Important Notes:**
- The credit amount should match the value you want users to receive
- The plan name should match one of: `'Standard'`, `'Pro'`, or `'Premium'`
- Keep both mappings in sync!

### Step 3: Update Frontend (Optional)

If you display prices on your frontend, update the pricing component:

```typescript
// Example: src/components/PricingTable.tsx or similar
const pricingPlans = [
  {
    name: 'Pro Plan',
    price: '$99',  // 👈 UPDATE THIS
    priceId: 'price_YOUR_NEW_PRICE_ID',  // 👈 UPDATE THIS
    credits: 50000,
  }
];
```

### Step 4: Deploy Changes

1. **Commit your code changes:**
   ```bash
   git add src/app/api/webhooks/stripe/route.ts
   git commit -m "Update Pro plan pricing to €99"
   git push
   ```

2. **Deploy to production** (Vercel auto-deploys from git push)

### Step 5: Test the New Price

1. Go to your pricing page
2. Subscribe to the plan with the new price
3. Complete the checkout
4. Verify in your database:
   - Credits were added correctly
   - Subscription status is "active"
   - `clerk_plan_id` matches your new price ID

---

## Common Scenarios

### Scenario 1: Adding a Test Price (€1) for Testing

```typescript
// In webhook route.ts
const creditsPerPlan = {
    // ... existing prices ...
    'price_1SKiHB8Ap4GWUvRfKvp8V5uz': 42000,  // Test: Pro Yearly €1
};

const planNamePerPlan = {
    // ... existing prices ...
    'price_1SKiHB8Ap4GWUvRfKvp8V5uz': 'Pro',
};
```

**Remember to remove test prices before going to production!**

### Scenario 2: Changing Price for Existing Plan

If you want to change the price of "Pro Monthly" from €79 to €99:

1. Create NEW price in Stripe (€99)
2. Get new price ID: `price_NEW123...`
3. Add new price to webhook mappings
4. **Keep old price ID** in mappings (for existing subscribers)
5. Update frontend to show new price and use new price ID for new subscriptions

```typescript
const creditsPerPlan = {
    'price_OLD123...': 42000,  // Old Pro Monthly (existing subscribers)
    'price_NEW123...': 42000,  // New Pro Monthly (new subscribers)
};
```

### Scenario 3: Removing an Old Price

**NEVER remove a price ID if anyone is currently subscribed to it!**

To safely remove a price:
1. Archive the price in Stripe Dashboard (prevents new subscriptions)
2. Wait until all subscribers have migrated or cancelled
3. Then remove from webhook mappings

---

## Current Price Structure

Based on your webhook configuration:

| Plan | Period | Price ID | Credits |
|------|--------|----------|---------|
| Standard | Monthly | `price_1Rtczx8Ap4GWUvRfKowvGuGG` | 700 |
| Pro | Monthly | `price_1Rta8u8Ap4GWUvRfreOwBwhw` | 3,500 |
| Premium | Monthly | `price_1RtaCC8Ap4GWUvRf30nF2vlN` | 14,000 |
| Standard | Yearly | `price_1Rta9g8Ap4GWUvRfnjvHIO10` | 8,400 |
| Pro | Yearly | `price_1RtaBl8Ap4GWUvRfDoGK2gdQ` | 42,000 |
| Premium | Yearly | `price_1RtaCU8Ap4GWUvRfiTS7yl6i` | 168,000 |

---

## Troubleshooting

### Error: "Price or Plan not found"

**Cause:** You created a new price in Stripe but forgot to add it to the webhook mappings.

**Fix:** Add the price ID to BOTH `creditsPerPlan` and `planNamePerPlan` in `/src/app/api/webhooks/stripe/route.ts`

### Error: Credits not added after subscription

**Cause:** Price ID mismatch or webhook failure.

**Fix:**
1. Check Stripe webhook logs for errors
2. Verify price ID is in both mappings
3. Check that credits amount is > 0

### Webhook shows 500 error

**Cause:** Usually a database function error or missing price mapping.

**Fix:**
1. Check webhook logs in Stripe Dashboard
2. Run `FIX-DUPLICATE-FUNCTIONS.sql` to clean up database functions
3. Verify price mappings are correct

---

## Best Practices

1. **Always test with €1 test prices first** before changing production prices
2. **Keep old price IDs** in mappings for existing subscribers
3. **Never remove active price IDs** - archive in Stripe instead
4. **Document your changes** - add comments next to price IDs
5. **Monitor webhooks** after deploying pricing changes
6. **Test the full flow**: subscribe → verify credits → cancel → verify expiration

---

## Quick Checklist

When adding a new price:
- [ ] Created price in Stripe Dashboard
- [ ] Copied price ID
- [ ] Added to `creditsPerPlan` mapping
- [ ] Added to `planNamePerPlan` mapping
- [ ] Updated frontend pricing display (if needed)
- [ ] Committed and pushed code
- [ ] Deployed to production
- [ ] Tested subscription flow
- [ ] Verified credits added correctly
- [ ] Checked webhook logs for success

---

## Files to Edit

1. **Webhook Handler** (REQUIRED):
   - File: `/src/app/api/webhooks/stripe/route.ts`
   - Lines: ~10 (creditsPerPlan) and ~19 (planNamePerPlan)

2. **Frontend Pricing** (Optional):
   - File: `/src/app/[locale]/(unauth)/pricing/page.tsx` or similar
   - Update displayed prices and price IDs

3. **AppConfig** (Optional, if you store plans there):
   - File: `/src/utils/AppConfig.ts`
   - Update plan configurations

---

## Need Help?

- Check Stripe webhook logs: Dashboard → Developers → Webhooks
- Check server logs for webhook errors
- Run `FIX-DUPLICATE-FUNCTIONS.sql` if you see database function errors
- Test with €1 test prices before changing production prices
