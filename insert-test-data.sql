-- ============================================================================
-- GUARDIAVISION TEST DATA INSERTION
-- Run this script AFTER the main fix script to insert test data
-- This ensures the studio works immediately after setup
-- ============================================================================

-- Insert test user file (replace 'your-user-id' with actual Clerk user ID)
-- You can get your user ID from the browser console when logged in

-- Example user file entry (modify the user_id to match your actual Clerk user ID)
INSERT INTO user_files (id, user_id, filename, file_path, file_size, mime_type, created_at, updated_at)
VALUES (
    'test-file-1',
    'user_2example123456789', -- REPLACE THIS WITH YOUR ACTUAL CLERK USER ID
    'test-image.jpg',
    '/uploads/test-image.jpg',
    1024000,
    'image/jpeg',
    NOW(),
    NOW()
) ON CONFLICT (id) DO NOTHING;

-- Insert another test file
INSERT INTO user_files (id, user_id, filename, file_path, file_size, mime_type, created_at, updated_at)
VALUES (
    'test-file-2',
    'user_2example123456789', -- REPLACE THIS WITH YOUR ACTUAL CLERK USER ID
    'sample-photo.png',
    '/uploads/sample-photo.png',
    2048000,
    'image/png',
    NOW(),
    NOW()
) ON CONFLICT (id) DO NOTHING;

-- ============================================================================
-- HOW TO GET YOUR ACTUAL USER ID:
-- ============================================================================
-- 1. Open GuardiaVision in browser
-- 2. Log in to your account
-- 3. Open browser console (F12)
-- 4. Type: console.log(window.Clerk?.user?.id)
-- 5. Copy the user ID that appears
-- 6. Replace 'user_2example123456789' above with your actual user ID
-- 7. Run this script again with the correct user ID

-- ============================================================================
-- VERIFICATION QUERY
-- ============================================================================
-- Check if test data was inserted
SELECT 
    'user_files' as table_name,
    COUNT(*) as record_count
FROM user_files
UNION ALL
SELECT 
    'processed_files' as table_name,
    COUNT(*) as record_count
FROM processed_files
UNION ALL
SELECT 
    'studio_states' as table_name,
    COUNT(*) as record_count
FROM studio_states;

-- Show user files for debugging
SELECT 
    id,
    user_id,
    filename,
    created_at
FROM user_files
ORDER BY created_at DESC
LIMIT 10;

-- ============================================================================
-- SUCCESS MESSAGE
-- ============================================================================
DO $$
BEGIN
    RAISE NOTICE '✅ Test data insertion completed!';
    RAISE NOTICE '⚠️  Remember to replace user_2example123456789 with your actual Clerk user ID';
    RAISE NOTICE '📝 Get your user ID from browser console: window.Clerk?.user?.id';
END $$;
