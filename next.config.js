const withNextIntl = require('next-intl/plugin')('./src/libs/i18n.ts');

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  env: {
    // Ensure Clerk environment variables are available
    CLERK_SECRET_KEY: process.env.CLERK_SECRET_KEY || process.env.NEXT_PUBLIC_CLERK_SECRET_KEY,
    CLERK_WEBHOOK_SECRET: process.env.CLERK_WEBHOOK_SECRET,
    CLERK_ENCRYPTION_KEY: process.env.CLERK_ENCRYPTION_KEY,
  },
  // Configure images for Supabase
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '*.supabase.co',
        port: '',
        pathname: '/storage/v1/object/public/**',
      },
      {
        protocol: 'https',
        hostname: '*.supabase.com',
        port: '',
        pathname: '/storage/v1/object/public/**',
      },
    ],
    unoptimized: false, // Enable optimization for better performance
  },
  // Disable TypeScript and ESLint checking during build
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Add webpack configuration to ignore critical dependency warnings
  webpack: (config, { isServer }) => {
    // Ignore the @opentelemetry critical dependency warning
    config.ignoreWarnings = [
      { module: /@opentelemetry/ },
      { module: /@prisma/ },
    ];

    return config;
  },
  // Add any other Next.js configuration options here
};

// Log environment variables during build
console.log('Next.js config - Environment variables check:', {
  NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY ? 'present' : 'missing',
  CLERK_SECRET_KEY: process.env.CLERK_SECRET_KEY ? 'present' : 'missing',
  NEXT_PUBLIC_CLERK_SECRET_KEY: process.env.NEXT_PUBLIC_CLERK_SECRET_KEY ? 'present' : 'missing',
  CLERK_WEBHOOK_SECRET: process.env.CLERK_WEBHOOK_SECRET ? 'present' : 'missing',
  CLERK_ENCRYPTION_KEY: process.env.CLERK_ENCRYPTION_KEY ? 'present' : 'missing',
  NODE_ENV: process.env.NODE_ENV,
});

module.exports = withNextIntl(nextConfig);
