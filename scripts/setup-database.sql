-- GuardiaVision Database Setup Script
-- Run this script to ensure all required tables exist with proper permissions
-- This fixes the "Access Denied" and "File Not Found" errors on studio refresh

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create user_files table (for file metadata)
CREATE TABLE IF NOT EXISTS user_files (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    filename TEXT NOT NULL,
    file_path TEXT,
    file_size BIGINT,
    mime_type TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create processed_files table (for processing history)
CREATE TABLE IF NOT EXISTS processed_files (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id TEXT NOT NULL,
    original_file_id TEXT NOT NULL,
    processed_image_url TEXT,
    processing_settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create studio_states table (for session state)
CREATE TABLE IF NOT EXISTS studio_states (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id TEXT NOT NULL,
    file_id TEXT NOT NULL,
    state_data JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, file_id)
);

-- Create credit_transactions table (for credit tracking)
CREATE TABLE IF NOT EXISTS credit_transactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id TEXT NOT NULL,
    amount INTEGER NOT NULL,
    transaction_type TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_files_user_id ON user_files(user_id);
CREATE INDEX IF NOT EXISTS idx_processed_files_user_id ON processed_files(user_id);
CREATE INDEX IF NOT EXISTS idx_processed_files_original_file_id ON processed_files(original_file_id);
CREATE INDEX IF NOT EXISTS idx_studio_states_user_id ON studio_states(user_id);
CREATE INDEX IF NOT EXISTS idx_studio_states_file_id ON studio_states(file_id);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_user_id ON credit_transactions(user_id);

-- Enable Row Level Security on all tables
ALTER TABLE user_files ENABLE ROW LEVEL SECURITY;
ALTER TABLE processed_files ENABLE ROW LEVEL SECURITY;
ALTER TABLE studio_states ENABLE ROW LEVEL SECURITY;
ALTER TABLE credit_transactions ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Users can view their own files" ON user_files;
DROP POLICY IF EXISTS "Users can insert their own files" ON user_files;
DROP POLICY IF EXISTS "Users can update their own files" ON user_files;
DROP POLICY IF EXISTS "Users can delete their own files" ON user_files;

DROP POLICY IF EXISTS "Users can view their own processed files" ON processed_files;
DROP POLICY IF EXISTS "Users can insert their own processed files" ON processed_files;
DROP POLICY IF EXISTS "Users can update their own processed files" ON processed_files;
DROP POLICY IF EXISTS "Users can delete their own processed files" ON processed_files;

DROP POLICY IF EXISTS "Users can view their own studio states" ON studio_states;
DROP POLICY IF EXISTS "Users can insert their own studio states" ON studio_states;
DROP POLICY IF EXISTS "Users can update their own studio states" ON studio_states;
DROP POLICY IF EXISTS "Users can delete their own studio states" ON studio_states;

DROP POLICY IF EXISTS "Users can view their own credit transactions" ON credit_transactions;
DROP POLICY IF EXISTS "Users can insert their own credit transactions" ON credit_transactions;

-- Create RLS policies for user_files
CREATE POLICY "Users can view their own files" ON user_files
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own files" ON user_files
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own files" ON user_files
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own files" ON user_files
    FOR DELETE USING (auth.uid()::text = user_id);

-- Create RLS policies for processed_files
CREATE POLICY "Users can view their own processed files" ON processed_files
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own processed files" ON processed_files
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own processed files" ON processed_files
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own processed files" ON processed_files
    FOR DELETE USING (auth.uid()::text = user_id);

-- Create RLS policies for studio_states
CREATE POLICY "Users can view their own studio states" ON studio_states
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own studio states" ON studio_states
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own studio states" ON studio_states
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own studio states" ON studio_states
    FOR DELETE USING (auth.uid()::text = user_id);

-- Create RLS policies for credit_transactions
CREATE POLICY "Users can view their own credit transactions" ON credit_transactions
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own credit transactions" ON credit_transactions
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

-- Grant necessary permissions
GRANT ALL ON user_files TO authenticated;
GRANT ALL ON user_files TO service_role;
GRANT ALL ON processed_files TO authenticated;
GRANT ALL ON processed_files TO service_role;
GRANT ALL ON studio_states TO authenticated;
GRANT ALL ON studio_states TO service_role;
GRANT ALL ON credit_transactions TO authenticated;
GRANT ALL ON credit_transactions TO service_role;

-- Create helper function for debugging RLS issues
CREATE OR REPLACE FUNCTION debug_user_access()
RETURNS TABLE(
    current_user_id TEXT,
    auth_uid TEXT,
    user_files_count BIGINT,
    processed_files_count BIGINT,
    studio_states_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        current_setting('request.jwt.claims', true)::json->>'sub' as current_user_id,
        auth.uid()::text as auth_uid,
        (SELECT COUNT(*) FROM user_files WHERE user_id = auth.uid()::text) as user_files_count,
        (SELECT COUNT(*) FROM processed_files WHERE user_id = auth.uid()::text) as processed_files_count,
        (SELECT COUNT(*) FROM studio_states WHERE user_id = auth.uid()::text) as studio_states_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

GRANT EXECUTE ON FUNCTION debug_user_access() TO authenticated;
GRANT EXECUTE ON FUNCTION debug_user_access() TO service_role;
