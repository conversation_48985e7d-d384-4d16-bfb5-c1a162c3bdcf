#!/bin/bash

# Test script to verify GuardiaVision backend connectivity
# Usage: bash scripts/test-backend.sh

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Backend configuration
# Priority 1: Use RunPod proxy URL from environment
# Priority 2: Fall back to direct connection (usually doesn't work from outside)

if [ -f .env.local ]; then
    PROXY_URL=$(grep NEXT_PUBLIC_RUNPOD_PROXY_URL .env.local | cut -d '=' -f2)
fi

if [ -n "$PROXY_URL" ] && [ "$PROXY_URL" != "https://YOUR-POD-ID-8000.proxy.runpod.net" ]; then
    BASE_URL="$PROXY_URL"
    echo -e "${CYAN}Using RunPod Proxy URL: ${BASE_URL}${NC}\n"
else
    BACKEND_HOST="***************"
    BACKEND_PORT="8000"
    BASE_URL="http://${BACKEND_HOST}:${BACKEND_PORT}"
    echo -e "${YELLOW}⚠️  No proxy URL found in .env.local${NC}"
    echo -e "${YELLOW}   Using direct connection (may not work from outside)${NC}"
    echo -e "${CYAN}   See RUNPOD_PROXY_URL_SETUP.md for setup instructions${NC}\n"
fi

echo -e "${CYAN}🧪 GuardiaVision Backend Connection Test${NC}\n"
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"

passed=0
total=0

# Test 1: Health Check
((total++))
echo -e "\n${YELLOW}📋 Test 1: Health Check${NC}"
echo -e "${CYAN}   Testing: ${BASE_URL}/health${NC}"

response=$(curl -s -w "\n%{http_code}" "${BASE_URL}/health" 2>/dev/null)
http_code=$(echo "$response" | tail -n1)
body=$(echo "$response" | sed '$d')

if [ "$http_code" = "200" ]; then
    echo -e "${GREEN}✅ Health check passed (HTTP $http_code)${NC}"
    echo -e "${CYAN}   Response: $body${NC}"
    ((passed++))
else
    echo -e "${RED}❌ Health check failed (HTTP $http_code)${NC}"
    echo -e "${YELLOW}   Backend may not be accessible${NC}"
fi

# Test 2: Readiness Check
((total++))
echo -e "\n${YELLOW}📋 Test 2: Readiness Check${NC}"
echo -e "${CYAN}   Testing: ${BASE_URL}/ready${NC}"

response=$(curl -s -w "\n%{http_code}" "${BASE_URL}/ready" 2>/dev/null)
http_code=$(echo "$response" | tail -n1)
body=$(echo "$response" | sed '$d')

if [ "$http_code" = "200" ]; then
    echo -e "${GREEN}✅ Models are ready (HTTP $http_code)${NC}"
    echo -e "${CYAN}   Response: $body${NC}"
    ((passed++))
elif [ "$http_code" = "503" ]; then
    echo -e "${YELLOW}⏳ Models still loading (HTTP $http_code)${NC}"
    echo -e "${YELLOW}   Wait a few minutes and try again${NC}"
else
    echo -e "${RED}❌ Readiness check failed (HTTP $http_code)${NC}"
fi

# Test 3: Queue Statistics
((total++))
echo -e "\n${YELLOW}📋 Test 3: Queue Statistics${NC}"
echo -e "${CYAN}   Testing: ${BASE_URL}/stats${NC}"

response=$(curl -s -w "\n%{http_code}" "${BASE_URL}/stats" 2>/dev/null)
http_code=$(echo "$response" | tail -n1)
body=$(echo "$response" | sed '$d')

if [ "$http_code" = "200" ]; then
    echo -e "${GREEN}✅ Stats retrieved (HTTP $http_code)${NC}"
    echo -e "${CYAN}   Response: $body${NC}"
    ((passed++))
else
    echo -e "${RED}❌ Stats check failed (HTTP $http_code)${NC}"
fi

# Summary
echo -e "\n${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo -e "\n${CYAN}📊 Test Results: $passed/$total passed${NC}\n"

if [ $passed -eq $total ]; then
    echo -e "${GREEN}🎉 All tests passed! Backend is fully operational.${NC}"
    echo -e "\n${YELLOW}Next steps:${NC}"
    echo -e "${CYAN}1. Test image processing from your frontend${NC}"
    echo -e "${CYAN}2. Test video processing from your frontend${NC}"
    echo -e "${CYAN}3. Monitor job processing times${NC}"
elif [ $passed -gt 0 ]; then
    echo -e "${YELLOW}⚠️  Some tests failed. Backend is partially accessible.${NC}"
    echo -e "\n${YELLOW}Troubleshooting:${NC}"
    echo -e "${CYAN}1. Wait a few minutes for models to load${NC}"
    echo -e "${CYAN}2. Check RunPod container logs${NC}"
    echo -e "${CYAN}3. Verify network connectivity${NC}"
else
    echo -e "${RED}❌ All tests failed. Backend is not accessible.${NC}"
    echo -e "\n${YELLOW}Troubleshooting:${NC}"
    echo -e "${CYAN}1. Verify RunPod container is running${NC}"
    echo -e "${CYAN}2. Check firewall/network settings${NC}"
    echo -e "${CYAN}3. Confirm correct proxy URL in .env.local${NC}"
    echo -e "${CYAN}4. Try: curl ${BASE_URL}/health${NC}"
fi

echo -e "\n${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo ""

exit 0
