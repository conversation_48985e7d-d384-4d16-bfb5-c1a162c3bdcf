// File: app/api/billing/manage/route.ts

import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { stripe } from '@/lib/stripe';
import { supabase } from '@/lib/supabase';

export async function POST() {
  try {
    const { userId } = await auth();

    if (!userId) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // ✨ FIX: Changed 'clerkId' to 'clerk_id' to match your Supabase schema
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('stripeCustomerId')
      .eq('clerk_id', userId)
      .single();

    if (userError) {
      console.error('Supabase error fetching user:', userError);
      throw new Error('Could not fetch user data from the database.');
    }

    if (!user || !user.stripeCustomerId) {
      console.error('Stripe Customer ID not found for user:', userId);
      throw new Error('Stripe Customer ID not found for this user.');
    }
    
    const stripeCustomerId = user.stripeCustomerId;

    if (!stripeCustomerId.startsWith('cus_')) {
        console.error(`Invalid Stripe Customer ID format: ${stripeCustomerId}`);
        throw new Error('The stored Stripe Customer ID has an invalid format.');
    }

    const returnUrl = `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/user-profile/billing`;

    const portalSession = await stripe.billingPortal.sessions.create({
      customer: stripeCustomerId,
      return_url: returnUrl,
    });

    return NextResponse.json({ url: portalSession.url });

  } catch (error: any) {
    console.error('[MANAGE_SUBSCRIPTION_ERROR]', error);
    return new NextResponse(
        JSON.stringify({ error: error.message || 'An unexpected error occurred.' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}
