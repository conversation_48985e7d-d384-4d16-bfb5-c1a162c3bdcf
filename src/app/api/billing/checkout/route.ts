// File: app/api/billing/checkout/route.ts

import { NextResponse } from 'next/server';
import { auth, currentUser } from '@clerk/nextjs/server';
import Stripe from 'stripe';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

export async function POST(request: Request) {
  try {
    const { userId } = await auth();
    const user = await currentUser();
    if (!userId || !user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }
    const customerEmail = user.emailAddresses?.[0]?.emailAddress;
    if (!customerEmail) {
      return new NextResponse("User email not found.", { status: 400 });
    }
    const { priceId } = await request.json();
    if (!priceId) {
      return new NextResponse("Price ID is required", { status: 400 });
    }
    const supabase = createServiceRoleSupabaseClient();
    const { data: userData } = await supabase.from('users').select('stripe_customer_id').eq('id', userId).single();
    
    let customerId = userData?.stripe_customer_id;

    // --- ✨ ROBUST VALIDATION LOGIC ---
    // This block now checks if the customerId is not only present but also valid.
    if (customerId && customerId.startsWith('cus_')) {
      try {
        const customer = await stripe.customers.retrieve(customerId);
        // If the customer was deleted in Stripe, we treat it as if they don't have an ID.
        if (customer.deleted) {
            customerId = null;
        }
      } catch (error) {
        // If the ID is invalid and Stripe throws an error, we reset it.
        customerId = null;
      }
    } else {
        // If the ID is missing or doesn't start with 'cus_', it's invalid.
        customerId = null;
    }
    // --- END OF VALIDATION ---

    // If after all checks the customerId is still null, create a new one.
    if (!customerId) {
      const customer = await stripe.customers.create({
        email: customerEmail,
        name: `${user.firstName || ''} ${user.lastName || ''}`.trim(),
        metadata: { clerk_user_id: userId },
      });
      customerId = customer.id;
      // Update the user record in Supabase with the new, valid ID.
      await supabase.from('users').update({ stripe_customer_id: customerId }).eq('id', userId);
    }

    const origin = request.headers.get("origin") || "http://localhost:3000";

    const session = await stripe.checkout.sessions.create({
      ui_mode: 'embedded',
      customer: customerId,
      line_items: [{ price: priceId, quantity: 1 }],
      mode: 'subscription',
      return_url: `${origin}/dashboard/billing?session_id={CHECKOUT_SESSION_ID}`,
      metadata: {
        userId: userId,
      },
      client_reference_id: userId,
    });

    return NextResponse.json({ clientSecret: session.client_secret });

  } catch (error: any) {
    console.error("[BILLING_POST_ERROR]", error);
    return new NextResponse(
        JSON.stringify({ error: error.message || "Internal Server Error" }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}
