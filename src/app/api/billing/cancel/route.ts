// File: app/api/billing/cancel/route.ts

import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { stripe } from '@/lib/stripe';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function POST() {
  try {
    const { userId } = await auth();
    if (!userId) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const supabase = createServiceRoleSupabaseClient();
    const { data: user } = await supabase
      .from('users')
      .select('stripe_subscription_id')
      .eq('id', userId)
      .single();

    if (!user || !user.stripe_subscription_id) {
      throw new Error('Active subscription not found for this user.');
    }

    // Get user's current credits BEFORE cancelling
    const { data: userData } = await supabase
      .from('users')
      .select('id, credits')
      .eq('id', userId)
      .single();

    // This cancels the subscription immediately.
    // <PERSON><PERSON> will automatically send a `customer.subscription.deleted` webhook event.
    try {
      await stripe.subscriptions.cancel(user.stripe_subscription_id);

      // IMMEDIATELY set credit expiration here as well (redundant with webhook for safety)
      if (userData && userData.credits > 0) {
        console.log(`🔄 Setting credit expiration immediately for user ${userId} with ${userData.credits} credits`);
        const { data: rpcData, error: rpcError } = await supabase.rpc('handle_subscription_cancellation', {
          p_user_id: userId
        });

        if (rpcError) {
          console.error('❌ Error calling handle_subscription_cancellation RPC:', rpcError);
          // Don't throw - try direct update as fallback
          const { error: directUpdateError } = await supabase
            .from('users')
            .update({
              credits_expire_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
            })
            .eq('id', userId);

          if (directUpdateError) {
            console.error('❌ Error in direct update fallback:', directUpdateError);
          } else {
            console.log('✅ Set expiration via direct update fallback');
          }
        } else {
          console.log('✅ Successfully called RPC to set expiration');
        }
      } else {
        console.log(`ℹ️ User ${userId} has ${userData?.credits || 0} credits, skipping expiration`);
      }

    } catch (stripeError: any) {
      // If subscription doesn't exist in Stripe, clean up database
      if (stripeError.code === 'resource_missing') {
        console.log(`⚠️ Subscription ${user.stripe_subscription_id} not found in Stripe, cleaning up database...`);

        // Update database to reflect the subscription is gone
        await supabase
          .from('users')
          .update({
            subscription_type: 'Free',
            subscription_status: 'cancelled',
            clerk_plan_id: null,
            stripe_subscription_id: null,
            subscription_period_end: null,
            updated_at: new Date().toISOString(),
          })
          .eq('id', userId);

        // Set credit expiration to 30 days from now
        if (userData && userData.credits > 0) {
          const { error: rpcError } = await supabase.rpc('handle_subscription_cancellation', {
            p_user_id: userId
          });

          if (rpcError) {
            console.error('❌ RPC error, using direct update:', rpcError);
            await supabase
              .from('users')
              .update({
                credits_expire_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
              })
              .eq('id', userId);
          }
        }

        return NextResponse.json({
          success: true,
          message: 'Subscription was already cancelled. Your credits will expire in 30 days.'
        });
      }
      // Re-throw other Stripe errors
      throw stripeError;
    }

    return NextResponse.json({ success: true, message: 'Subscription cancelled successfully.' });

  } catch (error: any) {
    console.error('[CANCEL_SUBSCRIPTION_ERROR]', error);
    return new NextResponse(
        JSON.stringify({ error: error.message || 'An unexpected error occurred.' }),
        { status: 500 }
    );
  }
}