// File: app/api/billing/subscription-status/route.ts

import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function GET() {
  try {
    const { userId } = await auth();

    if (!userId) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const supabase = createServiceRoleSupabaseClient();

    // Fetches the user data from your Supabase table
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError && userError.code !== 'PGRST116') { // PGRST116 = no rows found
      console.error('Supabase error fetching user:', userError);
      throw userError;
    }

    if (!user) {
      return NextResponse.json({
        planName: null,
        priceId: null, // ✨ FIX: Include priceId for all responses
        status: 'free',
        credits: 0,
        renewsOn: null,
      });
    }

    // If the subscription_type is missing or set to 'Free', show the free plan view.
    if (!user.subscription_type || user.subscription_type === 'Free') {
      return NextResponse.json({
        planName: 'Free',
        priceId: null, // ✨ FIX: Include priceId for all responses
        status: user.subscription_status || 'active',
        credits: user.credits,
        renewsOn: null,
      });
    }

    // Format the renewal date directly from the database
    const renewsOn = user.subscription_period_end 
      ? new Date(user.subscription_period_end).toLocaleDateString() 
      : null;

    // Return the correct data directly from your database
    return NextResponse.json({
      planName: user.subscription_type,
      priceId: user.clerk_plan_id, // ✨ FIX: Send the user's current priceId
      status: user.subscription_status,
      credits: user.credits,
      renewsOn,
    });

  } catch (error) {
    console.error('[SUBSCRIPTION_STATUS_ERROR]', error);
    return new NextResponse(
        JSON.stringify({ error: 'Internal Server Error' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}