import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createClient } from '@supabase/supabase-js';

// Server-side Supabase client with service role
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log('📊 Fetching user data for:', userId);

    // Get user data from database
    const { data: userData, error } = await supabase
      .from('users')
      .select('id, credits, subscription_type, email, username, first_name, last_name')
      .eq('id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('❌ Database error:', error);
      return NextResponse.json(
        { error: 'Database error', details: error.message },
        { status: 500 }
      );
    }

    // If user doesn't exist, create them
    if (!userData) {
      console.log('👤 Creating new user record');
      
      const newUserData = {
        id: userId,
        email: '', // Will be filled by Clerk webhook
        credits: 5,
        subscription_type: 'Free',
        username: '',
        first_name: '',
        last_name: ''
      };

      const { data: createdUser, error: createError } = await supabase
        .from('users')
        .insert(newUserData)
        .select()
        .single();

      if (createError) {
        console.error('❌ Failed to create user:', createError);
        return NextResponse.json(
          { error: 'Failed to create user', details: createError.message },
          { status: 500 }
        );
      }

      console.log('✅ User created successfully');
      
      return NextResponse.json({
        success: true,
        user: {
          credits: createdUser.credits,
          subscriptionType: createdUser.subscription_type,
          email: createdUser.email,
          username: createdUser.username,
          firstName: createdUser.first_name,
          lastName: createdUser.last_name
        }
      });
    }

    console.log('✅ User data found');

    return NextResponse.json({
      success: true,
      user: {
        credits: userData.credits,
        subscriptionType: userData.subscription_type,
        email: userData.email,
        username: userData.username,
        firstName: userData.first_name,
        lastName: userData.last_name
      }
    });

  } catch (error: any) {
    console.error('❌ User data API error:', error);
    return NextResponse.json(
      { error: 'Server error', details: error.message },
      { status: 500 }
    );
  }
}

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';
