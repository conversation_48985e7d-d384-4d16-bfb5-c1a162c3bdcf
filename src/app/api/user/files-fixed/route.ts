import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

export async function GET(request: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const fileType = searchParams.get('type') as 'image' | 'video' | undefined;
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    console.log(`🔍 Fetching files for user ${userId}, type: ${fileType || 'all'}, limit: ${limit}`);

    // Use service role client directly (same as working secure-upload)
    const supabase = createServiceRoleSupabaseClient();

    // Build query exactly like the working secure-upload verification step
    let query = supabase
      .from('user_files')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Filter by file type if specified
    if (fileType === 'image') {
      query = query.eq('file_type', 'image');
    } else if (fileType === 'video') {
      query = query.eq('file_type', 'video');
    }

    const { data: files, error: filesError } = await query;

    if (filesError) {
      console.error('❌ Query error:', filesError);
      return NextResponse.json({ 
        error: `Database query failed: ${filesError.message}`,
        details: filesError
      }, { status: 500 });
    }

    console.log(`✅ Found ${files?.length || 0} files for user ${userId}`);

    // Generate signed URLs for each file (same logic as /api/user/files)
    const filesWithUrls = await Promise.all((files || []).map(async (file) => {
      try {
        let storagePath = file.file_path;

        // Normalize path - remove leading slash if present
        if (storagePath && storagePath.startsWith('/')) {
          storagePath = storagePath.substring(1);
        }

        // Generate SIGNED URL (secure, expires in 7 days) instead of public URL
        const { data: signedUrlData, error: signedError } = await supabase
          .storage
          .from('user-uploads')
          .createSignedUrl(storagePath, 604800); // 7 days = 604800 seconds

        if (signedError) {
          console.error(`❌ Failed to create signed URL for ${storagePath}:`, signedError);
          return {
            ...file,
            metadata: {
              ...file.metadata,
              originalUrl: '' // Clear invalid public URL
            }
          };
        }

        return {
          ...file,
          url: signedUrlData.signedUrl, // Add secure signed URL
          metadata: {
            ...file.metadata,
            originalUrl: signedUrlData.signedUrl // Update metadata with signed URL
          },
          type: file.file_type || (file.mime_type?.startsWith('image/') ? 'image' : 'video'),
          filename: file.original_filename || file.filename,
          size: file.file_size || 0,
        };
      } catch (error) {
        console.error(`❌ Error processing file ${file.id}:`, error);
        return file;
      }
    }));

    return NextResponse.json({
      success: true,
      files: filesWithUrls,
      count: filesWithUrls.length,
      userId
    });

  } catch (error: any) {
    console.error('❌ Get files error:', error);
    return NextResponse.json({ 
      success: false,
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    }, { status: 500 });
  }
}