import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: NextRequest) {
  try {
    // Get user authentication
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const { fileId, lastAccessedDate } = await request.json();

    if (!fileId || !lastAccessedDate) {
      return NextResponse.json(
        { error: 'Missing required fields: fileId and lastAccessedDate' },
        { status: 400 }
      );
    }

    // Update the last accessed date for the file
    const { data, error } = await supabase
      .from('user_files')
      .update({
        last_accessed_date: lastAccessedDate,
        updated_at: new Date().toISOString()
      })
      .eq('id', fileId)
      .eq('user_id', userId) // Ensure user can only update their own files
      .select('id, original_filename, last_accessed_date');

    if (error) {
      console.error('Database error updating last accessed date:', error);
      return NextResponse.json(
        { error: 'Failed to update file access date' },
        { status: 500 }
      );
    }

    if (!data || data.length === 0) {
      return NextResponse.json(
        { error: 'File not found or access denied' },
        { status: 404 }
      );
    }

    console.log(`✅ Updated last accessed date for file: ${data[0].original_filename}`);

    return NextResponse.json({
      success: true,
      message: 'File access date updated successfully',
      data: data[0]
    });

  } catch (error) {
    console.error('Error updating file access date:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET endpoint to check files approaching auto-deletion
export async function GET(request: NextRequest) {
  try {
    // Get user authentication
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get files that will be deleted in 7 days (warning period)
    const { data: warningFiles, error: warningError } = await supabase
      .from('user_files')
      .select('id, original_filename, file_type, created_at, last_accessed_date')
      .eq('user_id', userId)
      .lt('last_accessed_date', new Date(Date.now() - 23 * 24 * 60 * 60 * 1000).toISOString()) // 23 days ago
      .order('last_accessed_date', { ascending: true });

    if (warningError) {
      console.error('Database error fetching warning files:', warningError);
      return NextResponse.json(
        { error: 'Failed to fetch files approaching deletion' },
        { status: 500 }
      );
    }

    // Calculate days until deletion for each file
    const filesWithWarnings = (warningFiles || []).map(file => {
      const lastActivity = new Date(file.last_accessed_date || file.created_at);
      const daysSinceActivity = Math.floor((Date.now() - lastActivity.getTime()) / (1000 * 60 * 60 * 24));
      const daysUntilDeletion = Math.max(0, 30 - daysSinceActivity);

      return {
        ...file,
        daysSinceActivity,
        daysUntilDeletion,
        lastActivity: lastActivity.toISOString()
      };
    });

    return NextResponse.json({
      success: true,
      warningFiles: filesWithWarnings,
      totalWarningFiles: filesWithWarnings.length
    });

  } catch (error) {
    console.error('Error fetching files approaching deletion:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
