import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

/**
 * POST /api/user/files/cleanup-orphaned
 * Manually cleanup orphaned database records (files that don't exist in storage)
 * This endpoint can be called periodically or manually to clean up orphaned records
 */
export async function POST(request: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log(`🧹 Starting orphaned records cleanup for user ${userId}...`);

    const supabase = createServiceRoleSupabaseClient();

    // Get all files for this user
    const { data: files, error: filesError } = await supabase
      .from('user_files')
      .select('*')
      .eq('user_id', userId);

    if (filesError) {
      console.error('❌ Error fetching files:', filesError);
      return NextResponse.json({
        error: 'Failed to fetch files',
        details: filesError.message
      }, { status: 500 });
    }

    let orphanedCount = 0;
    const orphanedFiles: string[] = [];

    // Check each file in storage
    for (const file of files || []) {
      try {
        let storagePath = file.file_path;

        // If file_path doesn't include userId, prepend it
        if (storagePath && !storagePath.startsWith(userId)) {
          storagePath = `${userId}/${storagePath}`;
        }

        // Try to download the file to verify it exists
        const { data: existsData, error: existsError } = await supabase
          .storage
          .from('user-uploads')
          .download(storagePath);

        // If file doesn't exist in storage
        if (existsError || !existsData) {
          console.log(`⚠️ Orphaned record found: ${file.id} (${storagePath})`);

          // Delete from database
          const { error: deleteError } = await supabase
            .from('user_files')
            .delete()
            .eq('id', file.id);

          if (!deleteError) {
            orphanedCount++;
            orphanedFiles.push(file.filename);
            console.log(`🗑️ Deleted orphaned record: ${file.id}`);
          } else {
            console.error(`❌ Failed to delete orphaned record ${file.id}:`, deleteError);
          }
        }
      } catch (error) {
        console.error(`❌ Error checking file ${file.id}:`, error);
      }
    }

    console.log(`✅ Cleanup complete. Removed ${orphanedCount} orphaned records`);

    return NextResponse.json({
      success: true,
      message: `Cleaned up ${orphanedCount} orphaned records`,
      orphanedCount,
      orphanedFiles,
      totalFilesChecked: files?.length || 0
    });

  } catch (error: any) {
    console.error('❌ Cleanup error:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    }, { status: 500 });
  }
}

/**
 * GET /api/user/files/cleanup-orphaned
 * Check how many orphaned records exist without deleting them
 */
export async function GET(request: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log(`🔍 Checking for orphaned records for user ${userId}...`);

    const supabase = createServiceRoleSupabaseClient();

    // Get all files for this user
    const { data: files, error: filesError } = await supabase
      .from('user_files')
      .select('*')
      .eq('user_id', userId);

    if (filesError) {
      console.error('❌ Error fetching files:', filesError);
      return NextResponse.json({
        error: 'Failed to fetch files',
        details: filesError.message
      }, { status: 500 });
    }

    let orphanedCount = 0;
    const orphanedFiles: any[] = [];

    // Check each file in storage
    for (const file of files || []) {
      try {
        let storagePath = file.file_path;

        // If file_path doesn't include userId, prepend it
        if (storagePath && !storagePath.startsWith(userId)) {
          storagePath = `${userId}/${storagePath}`;
        }

        // Try to download the file to verify it exists
        const { data: existsData, error: existsError } = await supabase
          .storage
          .from('user-uploads')
          .download(storagePath);

        // If file doesn't exist in storage
        if (existsError || !existsData) {
          orphanedCount++;
          orphanedFiles.push({
            id: file.id,
            filename: file.filename,
            path: storagePath,
            created_at: file.created_at
          });
        }
      } catch (error) {
        console.error(`❌ Error checking file ${file.id}:`, error);
      }
    }

    console.log(`✅ Found ${orphanedCount} orphaned records`);

    return NextResponse.json({
      success: true,
      orphanedCount,
      orphanedFiles,
      totalFiles: files?.length || 0,
      message: orphanedCount > 0
        ? `Found ${orphanedCount} orphaned records. Use POST to clean them up.`
        : 'No orphaned records found'
    });

  } catch (error: any) {
    console.error('❌ Check error:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    }, { status: 500 });
  }
}
