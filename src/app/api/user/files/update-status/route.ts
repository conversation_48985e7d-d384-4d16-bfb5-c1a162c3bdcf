import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

/**
 * POST /api/user/files/update-status
 * Updates the workflow status of a file (draft/completed)
 */
export async function POST(request: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { fileId, status } = body;

    if (!fileId) {
      return NextResponse.json({ error: 'fileId is required' }, { status: 400 });
    }

    if (status && !['draft', 'completed', null].includes(status)) {
      return NextResponse.json({
        error: 'Invalid status. Must be "draft", "completed", or null'
      }, { status: 400 });
    }

    console.log(`📝 Updating file ${fileId} status to: ${status || 'null'} for user ${userId}`);

    const supabase = createServiceRoleSupabaseClient();

    // Verify file belongs to user
    const { data: file, error: fetchError } = await supabase
      .from('user_files')
      .select('id, user_id')
      .eq('id', fileId)
      .single();

    if (fetchError || !file) {
      console.error('❌ File not found:', fetchError);
      return NextResponse.json({ error: 'File not found' }, { status: 404 });
    }

    if (file.user_id !== userId) {
      console.error('❌ Unauthorized: file does not belong to user');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Update workflow status
    const updateData: any = {
      workflow_status: status,
      updated_at: new Date().toISOString(),
    };

    // Update last_accessed_at when setting to draft
    if (status === 'draft') {
      updateData.last_accessed_at = new Date().toISOString();
    }

    const { error: updateError } = await supabase
      .from('user_files')
      .update(updateData)
      .eq('id', fileId)
      .eq('user_id', userId);

    if (updateError) {
      console.error('❌ Error updating file status:', updateError);
      return NextResponse.json({
        error: 'Failed to update file status',
        details: updateError.message
      }, { status: 500 });
    }

    console.log(`✅ Successfully updated file ${fileId} to status: ${status || 'null'}`);

    return NextResponse.json({
      success: true,
      fileId,
      status,
      message: `File status updated to ${status || 'uploaded'}`
    });

  } catch (error: any) {
    console.error('❌ Update file status error:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    }, { status: 500 });
  }
}

/**
 * GET /api/user/files/update-status
 * Get information about this endpoint
 */
export async function GET() {
  return NextResponse.json({
    endpoint: '/api/user/files/update-status',
    method: 'POST',
    description: 'Update the workflow status of a user file',
    parameters: {
      fileId: 'string (required) - The ID of the file to update',
      status: 'string (required) - The new status: "draft", "completed", or null'
    },
    workflow: {
      null: 'File is uploaded but not yet opened in studio',
      draft: 'File has been opened in studio (in progress)',
      completed: 'File has been processed and saved from studio'
    }
  });
}
