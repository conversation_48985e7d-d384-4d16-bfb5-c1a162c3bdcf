import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { fileId: string } }
) {
  try {
    // Authenticate user
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { fileId } = params;
    if (!fileId) {
      return NextResponse.json({ error: 'File ID is required' }, { status: 400 });
    }

    // Create Supabase client with service role (same as working endpoints)
    const supabase = createServiceRoleSupabaseClient();

    // Fetch the specific file
    const { data: file, error } = await supabase
      .from('user_files')
      .select('*')
      .eq('id', fileId)
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('❌ Error fetching file:', error);
      return NextResponse.json({ 
        error: 'File not found or access denied',
        details: error.message 
      }, { status: 404 });
    }

    if (!file) {
      return NextResponse.json({ 
        error: 'File not found' 
      }, { status: 404 });
    }

    console.log('✅ File fetched successfully:', file.original_filename);

    return NextResponse.json({
      success: true,
      file
    });

  } catch (error: any) {
    console.error('❌ Error in GET /api/user/files/[fileId]:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error.message
    }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { fileId: string } }
) {
  try {
    // Authenticate user
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { fileId } = params;
    if (!fileId) {
      return NextResponse.json({ error: 'File ID is required' }, { status: 400 });
    }

    // Create Supabase client
    const supabase = createServiceRoleSupabaseClient();

    // First, get the file to check ownership and get file paths
    const { data: file, error: fetchError } = await supabase
      .from('user_files')
      .select('*')
      .eq('id', fileId)
      .eq('user_id', userId)
      .single();

    if (fetchError || !file) {
      console.error('❌ Error fetching file for deletion:', fetchError);
      return NextResponse.json({ 
        error: 'File not found or access denied' 
      }, { status: 404 });
    }

    // Delete the file record from database
    const { error: deleteError } = await supabase
      .from('user_files')
      .delete()
      .eq('id', fileId)
      .eq('user_id', userId);

    if (deleteError) {
      console.error('❌ Error deleting file record:', deleteError);
      return NextResponse.json({ 
        error: 'Failed to delete file record',
        details: deleteError.message 
      }, { status: 500 });
    }

    // Try to delete the actual file from storage
    try {
      if (file.file_path) {
        const { error: storageError } = await supabase.storage
          .from('user-uploads')
          .remove([file.file_path]);

        if (storageError) {
          console.warn('⚠️ Warning: Failed to delete file from storage:', storageError);
          // Don't fail the entire operation if storage deletion fails
        }
      }

      // Delete processed file if it exists
      if (file.processed_path) {
        const { error: processedStorageError } = await supabase.storage
          .from('user-uploads')
          .remove([file.processed_path]);

        if (processedStorageError) {
          console.warn('⚠️ Warning: Failed to delete processed file from storage:', processedStorageError);
        }
      }

      // Delete thumbnail if it exists
      if (file.thumbnail_path) {
        const { error: thumbnailStorageError } = await supabase.storage
          .from('user-uploads')
          .remove([file.thumbnail_path]);

        if (thumbnailStorageError) {
          console.warn('⚠️ Warning: Failed to delete thumbnail from storage:', thumbnailStorageError);
        }
      }
    } catch (storageError) {
      console.warn('⚠️ Warning: Storage cleanup failed:', storageError);
      // Continue anyway - the database record is deleted
    }

    console.log('✅ File deleted successfully:', file.original_filename);

    return NextResponse.json({
      success: true,
      message: 'File deleted successfully'
    });

  } catch (error: any) {
    console.error('❌ Error in DELETE /api/user/files/[fileId]:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error.message
    }, { status: 500 });
  }
}