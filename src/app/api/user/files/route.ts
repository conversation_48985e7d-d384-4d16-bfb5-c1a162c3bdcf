import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

export async function GET(request: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const fileType = searchParams.get('type') as 'image' | 'video' | undefined;
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    console.log(`🔍 Fetching files for user ${userId}, type: ${fileType || 'all'}, limit: ${limit}`);

    const supabase = createServiceRoleSupabaseClient();

    // Use EXACT same query as working debug endpoint
    const { data: files, error: filesError } = await supabase
      .from('user_files')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (filesError) {
      console.error('❌ Query error:', filesError);
      return NextResponse.json({ 
        error: `Database query failed: ${filesError.message}`,
        details: filesError
      }, { status: 500 });
    }

    // Filter by file type if specified (client-side filtering like debug endpoint)
    let filteredFiles = files || [];
    if (fileType === 'image') {
      filteredFiles = filteredFiles.filter(f => f.file_type === 'image');
    } else if (fileType === 'video') {
      filteredFiles = filteredFiles.filter(f => f.file_type === 'video');
    }

    // Get all files in storage for this user (fast batch operation)
    // NOTE: Files are stored in userId/uploads/ subdirectory, so we need to list that path
    const { data: storageFiles, error: storageError } = await supabase
      .storage
      .from('user-uploads')
      .list(`${userId}/uploads`, {
        limit: 1000,
        sortBy: { column: 'created_at', order: 'desc' }
      });

    console.log(`📁 Found ${storageFiles?.length || 0} files in storage for user ${userId}`);

    // Create a Set of existing filenames for O(1) lookup
    // Storage path format: userId/uploads/timestamp_filename.ext
    const existingFiles = new Set(
      (storageFiles || []).map(f => `${userId}/uploads/${f.name}`)
    );

    // Process files and check against storage Set (much faster than individual downloads)
    const orphanedIds: string[] = [];
    const filesWithUrls = await Promise.all(filteredFiles.map(async (file) => {
      try {
        // file_path should already contain the full path: userId/uploads/timestamp_filename.ext
        let storagePath = file.file_path;

        // Normalize path - remove leading slash if present
        if (storagePath && storagePath.startsWith('/')) {
          storagePath = storagePath.substring(1);
        }

        // Fast check: does file exist in storage?
        if (!existingFiles.has(storagePath)) {
          console.warn(`⚠️ Orphaned record: ${file.id} (${storagePath})`);
          console.warn(`⚠️ Available files:`, Array.from(existingFiles).slice(0, 5));
          orphanedIds.push(file.id);
          return null; // Will be filtered out
        }

        // Generate SIGNED URL (secure, expires in 7 days) instead of public URL
        const { data: signedUrlData, error: signedError } = await supabase
          .storage
          .from('user-uploads')
          .createSignedUrl(storagePath, 604800); // 7 days = 604800 seconds

        if (signedError) {
          console.error(`❌ Failed to create signed URL for ${storagePath}:`, signedError);
          return null;
        }

        return {
          ...file,
          url: signedUrlData.signedUrl, // Secure signed URL
          type: file.file_type || (file.mime_type?.startsWith('image/') ? 'image' : 'video'),
          filename: file.original_filename || file.filename,
          size: file.file_size || 0,
        };
      } catch (error) {
        console.error(`❌ Error processing file ${file.id}:`, error);
        return null;
      }
    }));

    // Batch delete orphaned records (much faster than individual deletes)
    if (orphanedIds.length > 0) {
      const { error: deleteError } = await supabase
        .from('user_files')
        .delete()
        .in('id', orphanedIds);

      if (!deleteError) {
        console.log(`🗑️ Deleted ${orphanedIds.length} orphaned records`);
      } else {
        console.error(`❌ Failed to delete orphaned records:`, deleteError);
      }
    }

    // Filter out null entries (files that don't exist in storage or had errors)
    const validFiles = filesWithUrls.filter(file => file !== null);

    console.log(`✅ Found ${validFiles.length} valid files for user ${userId} (filtered out ${filesWithUrls.length - validFiles.length} orphaned records)`);

    return NextResponse.json({
      success: true,
      files: validFiles,
      count: validFiles.length,
      userId,
      orphanedRemoved: filesWithUrls.length - validFiles.length
    });

  } catch (error: any) {
    console.error('❌ Get files error:', error);
    return NextResponse.json({ 
      success: false,
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    }, { status: 500 });
  }
}