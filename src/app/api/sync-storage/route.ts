import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function POST() {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('🔄 SYNCING STORAGE FOR USER:', userId);
    const supabase = createServiceRoleSupabaseClient();

    // Step 1: Get all files from storage for this user
    console.log('📁 Fetching storage files...');
    const { data: storageFiles, error: storageError } = await supabase.storage
      .from('user-uploads')
      .list(`${userId}/uploads`, {
        limit: 1000,
        sortBy: { column: 'created_at', order: 'desc' }
      });

    if (storageError) {
      console.error('❌ Storage error:', storageError);
      return NextResponse.json({ 
        success: false,
        error: `Storage error: ${storageError.message}` 
      }, { status: 500 });
    }

    console.log('📁 Found', storageFiles?.length || 0, 'files in storage');

    // Step 2: Get all database records for this user
    console.log('🗄️ Fetching database records...');
    const { data: dbFiles, error: dbError } = await supabase
      .from('user_files')
      .select('*')
      .eq('user_id', userId);

    if (dbError) {
      console.error('❌ Database error:', dbError);
      return NextResponse.json({ 
        success: false,
        error: `Database error: ${dbError.message}` 
      }, { status: 500 });
    }

    console.log('🗄️ Found', dbFiles?.length || 0, 'files in database');

    // Step 3: Find files that exist in storage but not in database
    const dbFilePaths = new Set(dbFiles?.map(f => f.file_path) || []);
    const missingInDb = storageFiles?.filter(file => {
      const filePath = `${userId}/uploads/${file.name}`;
      return !dbFilePaths.has(filePath) && file.name !== '.emptyFolderPlaceholder';
    }) || [];

    // Step 4: Find files that exist in database but not in storage
    const storageFileNames = new Set(storageFiles?.map(f => f.name) || []);
    const missingInStorage = dbFiles?.filter(record => {
      const fileName = record.file_path.split('/').pop();
      return !storageFileNames.has(fileName || '');
    }) || [];

    console.log('🔍 Files missing in database:', missingInDb.length);
    console.log('🔍 Files missing in storage:', missingInStorage.length);

    // Step 5: Sync metadata for existing files (update URLs, etc.)
    const syncedFiles = [];
    const syncErrors = [];

    for (const dbFile of dbFiles || []) {
      try {
        // Check if file exists in storage
        const fileName = dbFile.file_path.split('/').pop();
        const storageFile = storageFiles?.find(f => f.name === fileName);
        
        if (storageFile) {
          // Update metadata if needed
          const needsUpdate = !dbFile.metadata?.originalUrl || 
                             !dbFile.metadata?.publicUrl ||
                             !dbFile.metadata?.lastSynced;

          if (needsUpdate) {
            console.log('🔄 Syncing metadata for:', dbFile.original_filename);

            // Get fresh URLs
            const { data: urlData } = supabase.storage
              .from('user-uploads')
              .getPublicUrl(dbFile.file_path);

            let finalUrl = urlData.publicUrl;
            try {
              const { data: signedUrlData, error: signedUrlError } = await supabase.storage
                .from('user-uploads')
                .createSignedUrl(dbFile.file_path, 3600 * 24 * 7); // 7 days

              if (!signedUrlError && signedUrlData) {
                finalUrl = signedUrlData.signedUrl;
              }
            } catch (err) {
              // Use public URL as fallback
            }

            // Update the record
            const { error: updateError } = await supabase
              .from('user_files')
              .update({
                file_size: storageFile.metadata?.size || dbFile.file_size,
                metadata: {
                  ...dbFile.metadata,
                  originalUrl: finalUrl,
                  publicUrl: urlData.publicUrl,
                  lastSynced: new Date().toISOString(),
                  storageMetadata: storageFile.metadata,
                  urlType: finalUrl === urlData.publicUrl ? 'public' : 'signed'
                }
              })
              .eq('id', dbFile.id)
              .eq('user_id', userId);

            if (updateError) {
              console.error('❌ Failed to sync', dbFile.original_filename, ':', updateError);
              syncErrors.push(`${dbFile.original_filename}: ${updateError.message}`);
            } else {
              console.log('✅ Synced:', dbFile.original_filename);
              syncedFiles.push({
                id: dbFile.id,
                filename: dbFile.original_filename,
                url: finalUrl
              });
            }
          }
        }
      } catch (err: any) {
        console.error('❌ Error syncing', dbFile.original_filename, ':', err);
        syncErrors.push(`${dbFile.original_filename}: ${err.message}`);
      }
    }

    console.log('🎉 STORAGE SYNC COMPLETED');

    return NextResponse.json({
      success: true,
      message: 'Storage sync completed successfully',
      summary: {
        storageFiles: storageFiles?.length || 0,
        databaseRecords: dbFiles?.length || 0,
        missingInDatabase: missingInDb.length,
        missingInStorage: missingInStorage.length,
        filesSynced: syncedFiles.length
      },
      details: {
        syncedFiles,
        missingInDb: missingInDb.map(f => f.name),
        missingInStorage: missingInStorage.map(f => f.original_filename),
        errors: syncErrors.length > 0 ? syncErrors : undefined
      }
    });

  } catch (error: any) {
    console.error('❌ STORAGE SYNC ERROR:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    }, { status: 500 });
  }
}