import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function POST() {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('🔧 FIXING VIDEOS FOR USER:', userId);
    const supabase = createServiceRoleSupabaseClient();

    // Step 1: Get all files from storage for this user
    console.log('📁 Checking storage files...');
    const { data: storageFiles, error: storageError } = await supabase.storage
      .from('user-uploads')
      .list(`${userId}/uploads`, {
        limit: 1000,
        sortBy: { column: 'created_at', order: 'desc' }
      });

    if (storageError) {
      console.error('❌ Storage error:', storageError);
      return NextResponse.json({ 
        error: `Storage error: ${storageError.message}` 
      }, { status: 500 });
    }

    console.log('📁 Found', storageFiles?.length || 0, 'files in storage');

    // Step 2: Get all database records for this user
    console.log('🗄️ Checking database records...');
    const { data: dbFiles, error: dbError } = await supabase
      .from('user_files')
      .select('*')
      .eq('user_id', userId);

    if (dbError) {
      console.error('❌ Database error:', dbError);
      return NextResponse.json({ 
        error: `Database error: ${dbError.message}` 
      }, { status: 500 });
    }

    console.log('🗄️ Found', dbFiles?.length || 0, 'files in database');

    // Step 3: Find orphaned storage files (files in storage but not in database)
    const dbFilePaths = new Set(dbFiles?.map(f => f.file_path) || []);
    const orphanedFiles = storageFiles?.filter(file => {
      const filePath = `${userId}/uploads/${file.name}`;
      return !dbFilePaths.has(filePath) && file.name !== '.emptyFolderPlaceholder';
    }) || [];

    console.log('🔍 Found', orphanedFiles.length, 'orphaned files in storage');

    // Step 4: Find orphaned database records (records in database but not in storage)
    const storageFileNames = new Set(storageFiles?.map(f => f.name) || []);
    const orphanedRecords = dbFiles?.filter(record => {
      const fileName = record.file_path.split('/').pop();
      return !storageFileNames.has(fileName || '');
    }) || [];

    console.log('🔍 Found', orphanedRecords.length, 'orphaned database records');

    // Step 5: Create database records for orphaned storage files
    const createdRecords = [];
    const creationErrors = [];

    for (const file of orphanedFiles) {
      try {
        const filePath = `${userId}/uploads/${file.name}`;
        
        // Extract original filename (remove timestamp prefix if present)
        const originalFilename = file.name.replace(/^\d+_/, '');
        
        // Determine file type from name and metadata
        const isImage = /\.(jpg|jpeg|png|gif|webp|bmp|tiff)$/i.test(originalFilename);
        const isVideo = /\.(mp4|mov|avi|webm|mkv|flv|wmv|3gp|m4v)$/i.test(originalFilename);
        
        if (!isImage && !isVideo) {
          console.log('⚠️ Skipping unknown file type:', originalFilename);
          continue;
        }
        
        const fileType = isVideo ? 'video' : 'image';
        
        // Determine mime type
        const ext = originalFilename.split('.').pop()?.toLowerCase();
        let mimeType = 'application/octet-stream';
        if (ext) {
          const mimeMap: Record<string, string> = {
            // Images
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'png': 'image/png',
            'gif': 'image/gif',
            'webp': 'image/webp',
            'bmp': 'image/bmp',
            'tiff': 'image/tiff',
            // Videos
            'mp4': 'video/mp4',
            'mov': 'video/quicktime',
            'avi': 'video/x-msvideo',
            'webm': 'video/webm',
            'mkv': 'video/x-matroska',
            'flv': 'video/x-flv',
            'wmv': 'video/x-ms-wmv',
            '3gp': 'video/3gpp',
            'm4v': 'video/x-m4v'
          };
          mimeType = mimeMap[ext] || mimeType;
        }

        // Get public URL
        const { data: urlData } = supabase.storage
          .from('user-uploads')
          .getPublicUrl(filePath);

        // Try to get signed URL as fallback
        let finalUrl = urlData.publicUrl;
        try {
          const { data: signedUrlData, error: signedUrlError } = await supabase.storage
            .from('user-uploads')
            .createSignedUrl(filePath, 3600 * 24 * 7); // 7 days

          if (!signedUrlError && signedUrlData) {
            finalUrl = signedUrlData.signedUrl;
          }
        } catch (err) {
          console.log('⚠️ Could not create signed URL for', filePath);
        }

        console.log('📝 Creating database record for:', originalFilename, 'Type:', fileType);

        // Insert into database
        const { data: insertData, error: insertError } = await supabase
          .from('user_files')
          .insert({
            user_id: userId,
            original_filename: originalFilename,
            file_path: filePath,
            file_type: fileType,
            file_size: file.metadata?.size || 0,
            mime_type: mimeType,
            processing_status: 'completed',
            processing_progress: 100,
            metadata: {
              originalUrl: finalUrl,
              publicUrl: urlData.publicUrl,
              fixedAt: new Date().toISOString(),
              storageMetadata: file.metadata,
              urlType: finalUrl === urlData.publicUrl ? 'public' : 'signed'
            }
          })
          .select('*')
          .single();

        if (insertError) {
          console.error('❌ Failed to create record for', originalFilename, ':', insertError);
          creationErrors.push(`${originalFilename}: ${insertError.message}`);
        } else {
          console.log('✅ Created record for', originalFilename, 'ID:', insertData.id);
          createdRecords.push({
            id: insertData.id,
            filename: originalFilename,
            fileType: fileType,
            filePath: filePath,
            url: finalUrl
          });
        }
      } catch (err: any) {
        console.error('❌ Error processing', file.name, ':', err);
        creationErrors.push(`${file.name}: ${err.message}`);
      }
    }

    // Step 6: Clean up orphaned database records
    const deletedRecords = [];
    const deletionErrors = [];

    for (const record of orphanedRecords) {
      try {
        console.log('🗑️ Deleting orphaned database record:', record.original_filename);
        
        const { error: deleteError } = await supabase
          .from('user_files')
          .delete()
          .eq('id', record.id)
          .eq('user_id', userId);

        if (deleteError) {
          console.error('❌ Failed to delete record', record.id, ':', deleteError);
          deletionErrors.push(`${record.original_filename}: ${deleteError.message}`);
        } else {
          console.log('✅ Deleted orphaned record:', record.original_filename);
          deletedRecords.push({
            id: record.id,
            filename: record.original_filename
          });
        }
      } catch (err: any) {
        console.error('❌ Error deleting record', record.id, ':', err);
        deletionErrors.push(`${record.original_filename}: ${err.message}`);
      }
    }

    // Step 7: Update URLs for existing records that might have broken URLs
    console.log('🔗 Updating URLs for existing records...');
    const updatedRecords = [];
    const updateErrors = [];

    for (const record of dbFiles || []) {
      try {
        // Check if the record has a proper URL
        if (!record.metadata?.originalUrl) {
          console.log('🔗 Updating URL for:', record.original_filename);
          
          // Get fresh URLs
          const { data: urlData } = supabase.storage
            .from('user-uploads')
            .getPublicUrl(record.file_path);

          let finalUrl = urlData.publicUrl;
          try {
            const { data: signedUrlData, error: signedUrlError } = await supabase.storage
              .from('user-uploads')
              .createSignedUrl(record.file_path, 3600 * 24 * 7);

            if (!signedUrlError && signedUrlData) {
              finalUrl = signedUrlData.signedUrl;
            }
          } catch (err) {
            // Use public URL as fallback
          }

          // Update the record
          const { error: updateError } = await supabase
            .from('user_files')
            .update({
              metadata: {
                ...record.metadata,
                originalUrl: finalUrl,
                publicUrl: urlData.publicUrl,
                urlUpdatedAt: new Date().toISOString(),
                urlType: finalUrl === urlData.publicUrl ? 'public' : 'signed'
              }
            })
            .eq('id', record.id)
            .eq('user_id', userId);

          if (updateError) {
            console.error('❌ Failed to update URL for', record.original_filename, ':', updateError);
            updateErrors.push(`${record.original_filename}: ${updateError.message}`);
          } else {
            console.log('✅ Updated URL for:', record.original_filename);
            updatedRecords.push({
              id: record.id,
              filename: record.original_filename,
              url: finalUrl
            });
          }
        }
      } catch (err: any) {
        console.error('❌ Error updating URL for', record.original_filename, ':', err);
        updateErrors.push(`${record.original_filename}: ${err.message}`);
      }
    }

    console.log('🎉 VIDEO FIX COMPLETED');

    return NextResponse.json({
      success: true,
      message: 'Video fix completed successfully',
      summary: {
        storageFiles: storageFiles?.length || 0,
        databaseRecords: dbFiles?.length || 0,
        orphanedStorageFiles: orphanedFiles.length,
        orphanedDatabaseRecords: orphanedRecords.length,
        recordsCreated: createdRecords.length,
        recordsDeleted: deletedRecords.length,
        urlsUpdated: updatedRecords.length
      },
      details: {
        createdRecords,
        deletedRecords,
        updatedRecords,
        errors: {
          creation: creationErrors.length > 0 ? creationErrors : undefined,
          deletion: deletionErrors.length > 0 ? deletionErrors : undefined,
          updates: updateErrors.length > 0 ? updateErrors : undefined
        }
      }
    });

  } catch (error: any) {
    console.error('❌ VIDEO FIX ERROR:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    }, { status: 500 });
  }
}