import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export const dynamic = 'force-dynamic';
export const revalidate = 0;

/**
 * GET /api/pricing/plans
 *
 * Fetches all active pricing plans from the database.
 * This replaces hardcoded pricing from AppConfig.ts and components.
 *
 * Response format:
 * {
 *   success: true,
 *   plans: [
 *     {
 *       id: 'free',
 *       name: 'Free',
 *       description: 'Free trial user',
 *       price: { monthly: 0, yearly: 0 },
 *       credits: 5,
 *       creditsExpireDays: 60,
 *       features: [{ text: '...', included: true }],
 *       isPopular: false,
 *       buttonText: 'Current Plan',
 *       buttonVariant: 'blue',
 *       displayOrder: 0
 *     },
 *     ...
 *   ]
 * }
 */
export async function GET(request: NextRequest) {
  try {
    console.log('📋 Fetching pricing plans from database...');

    // Query pricing_plans table directly (no database function needed)
    const { data, error } = await supabase
      .from('pricing_plans')
      .select('*')
      .eq('is_active', true)
      .order('display_order', { ascending: true });

    if (error) {
      console.error('❌ Error fetching pricing plans:', error);
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to fetch pricing plans',
          details: error.message,
        },
        { status: 500 }
      );
    }

    // Transform data to match the expected format for components
    // Note: Your pricing_plans table has one row per plan with both monthly/yearly pricing
    const plans = data.map((plan: any) => ({
      planId: plan.id,
      name: plan.name,
      description: plan.description,
      price: {
        monthly: parseFloat(plan.price_monthly || 0),
        yearly: parseFloat(plan.price_yearly || 0),
      },
      stripePriceId: {
        monthly: plan.stripe_price_id_monthly_prod,
        yearly: plan.stripe_price_id_yearly_prod,
      },
      credits: plan.credits_included || 0,
      features: plan.features || [],
      isPopular: plan.is_popular || false,
      buttonText: plan.button_text || 'Get Started',
      buttonVariant: plan.button_variant || 'blue',
      displayOrder: plan.display_order || 0,
    }));

    console.log(`✅ Fetched ${plans.length} pricing plan types`);

    return NextResponse.json(
      {
        success: true,
        plans,
        cachedAt: new Date().toISOString(),
      },
      {
        status: 200,
        headers: {
          // Temporarily disable cache to see updates immediately
          // Change back to 'public, s-maxage=300, stale-while-revalidate=600' when done testing
          'Cache-Control': 'no-cache, no-store, must-revalidate',
        },
      }
    );
  } catch (err: any) {
    console.error('❌ Unexpected error in pricing API:', err);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: err.message,
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/pricing/plans/:id
 *
 * Updates a specific pricing plan (admin only).
 * This allows dynamic pricing updates without code changes.
 *
 * Request body:
 * {
 *   priceMonthly?: number,
 *   priceYearly?: number,
 *   creditsIncluded?: number,
 *   features?: Array<{ text: string, included: boolean }>,
 *   isActive?: boolean,
 *   ...
 * }
 */
export async function POST(request: NextRequest) {
  try {
    // TODO: Add authentication check for admin users
    // For now, this endpoint should be protected at the API Gateway level
    // or add Clerk authentication here

    const body = await request.json();
    const { planId, ...updates } = body;

    if (!planId) {
      return NextResponse.json(
        { success: false, error: 'Plan ID is required' },
        { status: 400 }
      );
    }

    console.log(`🔄 Updating pricing plan: ${planId}`);

    // Build update object
    const updateData: any = {
      updated_at: new Date().toISOString(),
    };

    if (updates.priceMonthly !== undefined) {
      updateData.price_monthly = updates.priceMonthly;
    }
    if (updates.priceYearly !== undefined) {
      updateData.price_yearly = updates.priceYearly;
    }
    if (updates.creditsIncluded !== undefined) {
      updateData.credits_included = updates.creditsIncluded;
    }
    if (updates.creditsExpireDays !== undefined) {
      updateData.credits_expire_days = updates.creditsExpireDays;
    }
    if (updates.features !== undefined) {
      updateData.features = updates.features;
    }
    if (updates.isActive !== undefined) {
      updateData.is_active = updates.isActive;
    }
    if (updates.isPopular !== undefined) {
      updateData.is_popular = updates.isPopular;
    }

    const { data, error } = await supabase
      .from('pricing_plans')
      .update(updateData)
      .eq('id', planId)
      .select()
      .single();

    if (error) {
      console.error('❌ Error updating pricing plan:', error);
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to update pricing plan',
          details: error.message,
        },
        { status: 500 }
      );
    }

    console.log(`✅ Updated pricing plan: ${planId}`);

    return NextResponse.json({
      success: true,
      plan: data,
      message: `Pricing plan '${planId}' updated successfully`,
    });
  } catch (err: any) {
    console.error('❌ Unexpected error updating pricing:', err);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: err.message,
      },
      { status: 500 }
    );
  }
}
