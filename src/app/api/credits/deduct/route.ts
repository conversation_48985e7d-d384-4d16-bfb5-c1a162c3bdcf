import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/utils/supabase/server';
import { auth } from '@clerk/nextjs/server';

export async function POST(request: NextRequest) {
  try {
    const { userId } = auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { amount, operation } = await request.json();

    if (!amount || amount <= 0) {
      return NextResponse.json({ error: 'Invalid amount' }, { status: 400 });
    }

    console.log(`API: Deducting ${amount} credits for user ${userId} (${operation})`);

    const supabase = createServerSupabaseClient();

    // Get current user credits
    const { data: user, error: fetchError } = await supabase
      .from('users')
      .select('id, credits, subscription_type')
      .eq('id', userId)
      .single();

    if (fetchError || !user) {
      console.error('API: User not found', fetchError);
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    console.log(`API: Current credits: ${user.credits}, attempting to deduct: ${amount}`);

    // Check if user has enough credits
    if (user.credits < amount) {
      console.log(`API: Insufficient credits. Has: ${user.credits}, needs: ${amount}`);
      return NextResponse.json({ 
        error: 'Insufficient credits',
        currentCredits: user.credits,
        requiredCredits: amount,
        needsUpgrade: true
      }, { status: 402 }); // Payment Required
    }

    // Deduct credits
    const newCredits = user.credits - amount;
    const { data: updatedUser, error: updateError } = await supabase
      .from('users')
      .update({ 
        credits: newCredits,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)
      .select('credits, subscription_type')
      .single();

    if (updateError) {
      console.error('API: Failed to deduct credits', updateError);
      return NextResponse.json({
        error: 'Failed to deduct credits',
        details: updateError.message,
        code: updateError.code
      }, { status: 500 });
    }

    // Log the transaction (optional - don't fail if table doesn't exist)
    try {
      const { error: logError } = await supabase
        .from('credit_transactions')
        .insert([
          {
            user_id: userId,
            amount: -amount, // Negative for deduction
            operation: operation || 'detection',
            credits_before: user.credits,
            credits_after: newCredits,
            created_at: new Date().toISOString()
          }
        ]);

      if (logError) {
        console.error('API: Failed to log transaction (non-critical):', logError);
        // Don't fail the request if logging fails
      } else {
        console.log('API: Transaction logged successfully');
      }
    } catch (logError) {
      console.error('API: Transaction logging failed (non-critical):', logError);
      // Continue without failing
    }

    console.log(`API: Successfully deducted ${amount} credits. New balance: ${newCredits}`);

    return NextResponse.json({
      success: true,
      creditsDeducted: amount,
      remainingCredits: newCredits,
      subscriptionType: updatedUser.subscription_type,
      operation: operation || 'detection'
    });

  } catch (error) {
    console.error('API: Error in credit deduction', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET endpoint to check if user has enough credits
export async function GET(request: NextRequest) {
  try {
    const { userId } = auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const url = new URL(request.url);
    const requiredCredits = parseInt(url.searchParams.get('amount') || '3');

    const supabase = createServerSupabaseClient();

    const { data: user, error } = await supabase
      .from('users')
      .select('credits, subscription_type')
      .eq('id', userId)
      .single();

    if (error || !user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const hasEnoughCredits = user.credits >= requiredCredits;

    return NextResponse.json({
      hasEnoughCredits,
      currentCredits: user.credits,
      requiredCredits,
      subscriptionType: user.subscription_type,
      needsUpgrade: !hasEnoughCredits
    });

  } catch (error) {
    console.error('API: Error checking credits', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
