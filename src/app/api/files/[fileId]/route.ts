import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function GET(
  request: Request,
  { params }: { params: { fileId: string } }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { fileId } = params;
    const supabase = createServiceRoleSupabaseClient();

    // Get file record and verify ownership
    const { data: file, error: fetchError } = await supabase
      .from('user_files')
      .select('*')
      .eq('id', fileId)
      .eq('user_id', userId) // Ensure user owns the file
      .single();

    if (fetchError || !file) {
      return NextResponse.json({ 
        error: 'File not found or access denied' 
      }, { status: 404 });
    }

    // Get signed URL for secure access (expires in 1 hour)
    const { data: signedUrlData, error: urlError } = await supabase.storage
      .from('user-uploads')
      .createSignedUrl(file.file_path, 3600); // 1 hour expiry

    if (urlError || !signedUrlData) {
      return NextResponse.json({ 
        error: 'Failed to generate secure URL' 
      }, { status: 500 });
    }

    // Return the file data with secure URL
    return NextResponse.json({
      success: true,
      file: {
        id: file.id,
        filename: file.original_filename,
        fileType: file.file_type,
        fileSize: file.file_size,
        mimeType: file.mime_type,
        processingStatus: file.processing_status,
        createdAt: file.created_at,
        secureUrl: signedUrlData.signedUrl // This URL expires in 1 hour
      }
    });

  } catch (error: any) {
    console.error('❌ Secure file access error:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}