import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createClient } from '@supabase/supabase-js';

// Server-side Supabase client with service role
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      console.log('❌ No userId found in auth');
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log('✅ Authenticated user:', userId);

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') as 'image' | 'video' | undefined;
    const limit = parseInt(searchParams.get('limit') || '50');

    console.log(`📁 Fetching files for user ${userId}, type: ${type || 'all'}`);

    // Build query
    let query = supabase
      .from('user_files')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    // Add type filter if specified
    if (type) {
      query = query.eq('file_type', type);
    }

    const { data: files, error } = await query;

    if (error) {
      console.error('❌ Database query failed:', error);
      return NextResponse.json(
        { error: 'Failed to fetch files', details: error.message },
        { status: 500 }
      );
    }

    // Transform files for frontend
    const transformedFiles = files?.map(file => ({
      id: file.id,
      filename: file.original_filename,
      type: file.file_type,
      size: file.file_size,
      url: file.metadata?.originalUrl || '',
      created_at: file.created_at,
      processing_status: file.processing_status
    })) || [];

    console.log(`✅ Found ${transformedFiles.length} files for user ${userId}`);

    // Set cache headers for better performance
    const response = NextResponse.json({
      success: true,
      files: transformedFiles,
      count: transformedFiles.length,
      userId
    });

    // Cache for 30 seconds
    response.headers.set('Cache-Control', 'public, max-age=30, stale-while-revalidate=60');

    return response;

  } catch (error: any) {
    console.error('❌ Files API error:', error);
    return NextResponse.json(
      { error: 'Server error', details: error.message },
      { status: 500 }
    );
  }
}

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';
