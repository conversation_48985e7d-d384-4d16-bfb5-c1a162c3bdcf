import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { auth } from '@clerk/nextjs/server';

// Create a direct Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

const supabase = createClient(supabaseUrl, supabaseKey);

export async function GET(request: NextRequest) {
  try {
    // Get the user ID from Clerk
    const { userId } = auth();

    if (!userId) {
      console.log('API: No userId found in auth');
      return NextResponse.json({
        credits: 5,
        subscriptionType: 'Free',
        note: 'Default values used - not authenticated'
      });
    }

    console.log('API: Getting credits for user', userId);

    try {
      // Check if user exists in the database
      const { data, error } = await supabase
        .from('users')
        .select('credits, subscription_type')
        .eq('id', userId)
        .single();

      console.log('API: User data result', { data, error });

      if (error) {
        console.log('API: User not found, returning default values');
        return NextResponse.json({
          credits: 5,
          subscriptionType: 'Free',
          note: 'Default values used - user not found'
        });
      }

      return NextResponse.json({
        credits: data.credits,
        subscriptionType: data.subscription_type
      });
    } catch (dbError) {
      console.error('API: Database error', dbError);
      return NextResponse.json({
        credits: 5,
        subscriptionType: 'Free',
        note: 'Default values used - database error'
      });
    }
  } catch (error) {
    console.error('API: Error getting user credits', error);
    return NextResponse.json({
      credits: 5,
      subscriptionType: 'Free',
      note: 'Default values used - internal error'
    });
  }
}
