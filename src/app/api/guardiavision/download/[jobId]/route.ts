import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { GuardiaVisionAPI } from '@/lib/guardiavision-api';

/**
 * GET /api/guardiavision/download/[jobId]
 *
 * Get the download URL for a completed processing job
 *
 * Response:
 * {
 *   job_id: string;
 *   download_url: string;  // Presigned URL from Supabase (1 hour expiry)
 *   expires_at: string;
 * }
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { jobId: string } }
) {
  try {
    // Authenticate user and get token
    const { userId, getToken } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized - please sign in' },
        { status: 401 }
      );
    }

    const { jobId } = params;

    if (!jobId) {
      return NextResponse.json(
        { error: 'Missing job ID' },
        { status: 400 }
      );
    }

    // Get Clerk JWT token for backend authentication
    const clerkToken = await getToken();

    if (!clerkToken) {
      return NextResponse.json(
        { error: 'Failed to get authentication token' },
        { status: 401 }
      );
    }

    console.log('📥 Getting download URL for job:', jobId);

    // Create API client with Clerk JWT token
    const api = new GuardiaVisionAPI(clerkToken);

    // Get download URL from backend
    const downloadData = await api.getDownloadUrl(jobId);

    console.log('✅ Download URL retrieved, expires at:', downloadData.expires_at);

    return NextResponse.json(downloadData);

  } catch (error) {
    console.error('❌ Error getting download URL:', error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
