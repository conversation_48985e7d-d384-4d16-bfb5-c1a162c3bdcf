import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { GuardiaVisionAPI } from '@/lib/guardiavision-api';

/**
 * POST /api/guardiavision/detect
 *
 * Detect objects in an image and return JSON metadata for client-side rendering
 * This endpoint returns immediately with detection results (no processed image)
 *
 * Request body:
 * {
 *   file_path: string;           // Format: {userId}/uploads/{filename}
 *   text_prompt?: string;        // Default: 'person'
 *   score_threshold?: number;    // Default: 0.1
 * }
 *
 * Response:
 * {
 *   job_id: string;
 *   status: 'completed';
 *   metadata: {
 *     filename: string;
 *     detection_all: {
 *       box_ids: string[];
 *       labels: string[];
 *       boxes: number[][];     // [[x1, y1, x2, y2], ...]
 *       scores: number[];
 *     }
 *   };
 *   metadata_path: string;
 *   detection_count: number;
 *   processing_time: number;
 * }
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🎯 API Route /api/guardiavision/detect called');

    // Authenticate user and get token
    const { userId, getToken } = await auth();

    if (!userId) {
      console.log('❌ No userId found in auth');
      return NextResponse.json(
        { error: 'Unauthorized - please sign in' },
        { status: 401 }
      );
    }

    console.log('✅ User authenticated:', userId);

    // Parse request body
    const body = await request.json();
    const {
      file_path,
      text_prompt,
      score_threshold,
    } = body;

    console.log('📦 Request body:', { file_path, text_prompt, score_threshold });

    // Validate required fields
    if (!file_path) {
      console.log('❌ Missing file_path');
      return NextResponse.json(
        { error: 'Missing required field: file_path' },
        { status: 400 }
      );
    }

    // Get Clerk JWT token for backend authentication
    const clerkToken = await getToken();

    if (!clerkToken) {
      console.log('❌ Failed to get Clerk token');
      return NextResponse.json(
        { error: 'Failed to get authentication token' },
        { status: 401 }
      );
    }

    console.log('🔑 Clerk token obtained');

    console.log('🔍 Detecting objects in image:', {
      file_path,
      text_prompt: text_prompt || 'person',
      score_threshold: score_threshold || 0.1,
      userId,
      backend_url: process.env.NEXT_PUBLIC_RUNPOD_PROXY_URL,
    });

    // Create API client with Clerk JWT token
    const api = new GuardiaVisionAPI(clerkToken);

    console.log('📡 Calling backend...');

    // Detect objects (returns immediately with JSON metadata)
    const result = await api.detectImage(file_path, {
      text_prompt,
      score_threshold,
    });

    console.log('✅ Detection completed:', {
      job_id: result.job_id,
      detection_count: result.detection_count,
      processing_time: result.processing_time,
    });

    return NextResponse.json(result);

  } catch (error) {
    console.error('❌ Error detecting objects:', error);
    console.error('❌ Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
