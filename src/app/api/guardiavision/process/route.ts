import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { GuardiaVisionAPI } from '@/lib/guardiavision-api';

/**
 * POST /api/guardiavision/process
 *
 * Submit an image or video processing job to the RunPod backend
 *
 * Request body:
 * {
 *   file_path: string;           // Format: {userId}/uploads/{filename}
 *   media_type: 'image' | 'video';
 *   text_prompt?: string;        // Default: 'person'
 *   score_threshold?: number;    // Default: 0.3
 *   visualize?: boolean;         // Default: false
 *   blur_detections?: boolean;   // Default: false
 *   blur_strength?: number;      // Default: 35
 *   priority?: 'high' | 'normal' | 'low';
 * }
 *
 * Response:
 * {
 *   job_id: string;
 *   status: 'submitted' | 'processing';
 *   message?: string;
 * }
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user and get token
    const { userId, getToken } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized - please sign in' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const {
      file_path,
      media_type,
      text_prompt,
      score_threshold,
      visualize,
      blur_detections,
      blur_strength,
      priority,
    } = body;

    // Validate required fields
    if (!file_path) {
      return NextResponse.json(
        { error: 'Missing required field: file_path' },
        { status: 400 }
      );
    }

    if (!media_type || !['image', 'video'].includes(media_type)) {
      return NextResponse.json(
        { error: 'Invalid media_type - must be "image" or "video"' },
        { status: 400 }
      );
    }

    // Get Clerk JWT token for backend authentication
    const clerkToken = await getToken();

    if (!clerkToken) {
      return NextResponse.json(
        { error: 'Failed to get authentication token' },
        { status: 401 }
      );
    }

    console.log('📤 Submitting job to RunPod backend:', {
      file_path,
      media_type,
      text_prompt,
      userId,
    });

    // Create API client with Clerk JWT token
    const api = new GuardiaVisionAPI(clerkToken);

    // Submit job based on media type
    let result;
    if (media_type === 'video') {
      result = await api.processVideo(file_path, {
        text_prompt,
        score_threshold,
        blur_detections,
        blur_strength,
        priority,
      });
    } else {
      result = await api.processImage(file_path, {
        text_prompt,
        score_threshold,
        visualize,
        blur_detections,
        blur_strength,
        priority,
      });
    }

    console.log('✅ Job submitted successfully:', result.job_id);

    return NextResponse.json(result);

  } catch (error) {
    console.error('❌ Error submitting job:', error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
