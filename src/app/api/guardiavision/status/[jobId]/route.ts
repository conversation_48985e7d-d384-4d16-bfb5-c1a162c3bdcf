import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { GuardiaVisionAPI } from '@/lib/guardiavision-api';

/**
 * GET /api/guardiavision/status/[jobId]
 *
 * Get the status of a processing job
 *
 * Response:
 * {
 *   job_id: string;
 *   status: 'submitted' | 'processing' | 'completed' | 'failed';
 *   created_at?: string;
 *   completed_at?: string;
 *   error_message?: string;
 * }
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { jobId: string } }
) {
  try {
    // Authenticate user and get token
    const { userId, getToken } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized - please sign in' },
        { status: 401 }
      );
    }

    const { jobId } = params;

    if (!jobId) {
      return NextResponse.json(
        { error: 'Missing job ID' },
        { status: 400 }
      );
    }

    // Get Clerk JWT token for backend authentication
    const clerkToken = await getToken();

    if (!clerkToken) {
      return NextResponse.json(
        { error: 'Failed to get authentication token' },
        { status: 401 }
      );
    }

    console.log('📊 Getting job status for:', jobId);

    // Create API client with Clerk JWT token
    const api = new GuardiaVisionAPI(clerkToken);

    // Get job status from backend
    const status = await api.getJobStatus(jobId);

    console.log('✅ Job status retrieved:', status.status);

    return NextResponse.json(status);

  } catch (error) {
    console.error('❌ Error getting job status:', error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
