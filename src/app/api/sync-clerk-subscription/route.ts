import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function POST() {
  try {
    // Get the current user and session claims
    const { userId, sessionClaims } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    console.log(`🔄 Syncing Clerk subscription for user ${userId}`);
    console.log(`📋 Session claims:`, JSON.stringify(sessionClaims, null, 2));
    console.log(`📋 Raw plan from session: ${clerkPlan}`);

    const supabase = createServiceRoleSupabaseClient();

    // Extract plan from Clerk's session claims
    const clerkPlan = sessionClaims?.pla as string; // e.g., "u:standard", "u:free_user", "u:pro"

    if (!clerkPlan) {
      return NextResponse.json({
        success: false,
        error: 'No plan information found in session claims'
      }, { status: 400 });
    }

    console.log(`📋 Clerk plan from session claims: ${clerkPlan}`);

    // Parse the plan (remove "u:" prefix)
    const planName = clerkPlan.replace('u:', '').replace('_user', '');

    // Map Clerk plan names to our plan IDs
    const planMapping: Record<string, string> = {
      'free': 'free',
      'standard': 'standard',
      'pro': 'pro',
      'premium': 'premium',
    };

    const planId = planMapping[planName] || 'free';

    console.log(`🔄 Mapping Clerk plan "${planName}" to our plan "${planId}"`);

    // Get current user data from our database
    const { data: currentUser, error: currentUserError } = await supabase
      .from('users')
      .select('subscription_type, credits, last_payment_date')
      .eq('id', userId)
      .single();

    if (currentUserError) {
      console.error('❌ Error fetching current user:', currentUserError);
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch current user data'
      }, { status: 500 });
    }

    // Check if user already has this plan
    const currentPlanName = currentUser?.subscription_type?.toLowerCase() || 'free';
    const targetPlanName = planId === 'free' ? 'Free' : planId.charAt(0).toUpperCase() + planId.slice(1);

    console.log(`📋 Plan comparison: Current="${currentPlanName}", Target="${planId}"`);

    // More flexible plan comparison - normalize both sides
    const normalizedCurrentPlan = currentPlanName.toLowerCase().trim();
    const normalizedTargetPlan = planId.toLowerCase().trim();

    console.log(`📋 Normalized comparison: "${normalizedCurrentPlan}" vs "${normalizedTargetPlan}"`);

    // Check if plans match (allowing for case variations)
    const plansMatch = normalizedCurrentPlan === normalizedTargetPlan;

    // Also check if user already has credits for this plan (indicates they've been on this plan before)
    const { data: existingPlanCredits } = await supabase
      .from('credit_transactions')
      .select('id, amount, created_at')
      .eq('user_id', userId)
      .eq('plan_id', planId)
      .eq('transaction_type', 'subscription')
      .limit(1);

    const hasExistingPlanCredits = existingPlanCredits && existingPlanCredits.length > 0;

    console.log(`📋 Plans match: ${plansMatch}, Has existing plan credits: ${hasExistingPlanCredits}`);

    // Only skip sync if plans match AND user already has credits for this plan
    if (plansMatch && hasExistingPlanCredits) {
      console.log(`✅ User already has ${planId} plan with credits, no sync needed`);
      return NextResponse.json({
        success: true,
        message: `User already has ${targetPlanName} plan`,
        currentPlan: targetPlanName,
        noChangeNeeded: true,
      });
    }

    // If plans match but no credits, or if plans don't match, proceed with sync
    if (!plansMatch) {
      console.log(`🔄 Plan change detected: ${normalizedCurrentPlan} → ${normalizedTargetPlan}`);
    } else {
      console.log(`💳 Plan matches but missing credits for ${planId}, adding credits`);
    }

    console.log(`🔄 Upgrading user from ${currentPlanName} to ${planId}`);

    // Get plan details from our database
    const { data: plan, error: planError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('id', planId)
      .single();

    if (planError || !plan) {
      console.error('❌ Plan not found:', planId);
      return NextResponse.json({
        success: false,
        error: `Plan not found: ${planId}`
      }, { status: 400 });
    }

    // Update user subscription details
    const { error: userUpdateError } = await supabase
      .from('users')
      .update({
        subscription_type: plan.name,
        subscription_status: 'active',
        last_payment_date: planId !== 'free' ? new Date().toISOString() : currentUser.last_payment_date,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId);

    if (userUpdateError) {
      console.error('❌ Error updating user subscription:', userUpdateError);
      return NextResponse.json({
        success: false,
        error: 'Failed to update user subscription'
      }, { status: 500 });
    }

    // Add credits if it's a paid plan and user doesn't already have them
    let creditsAdded = 0;
    if (plan.credits_included > 0 && planId !== 'free') {
      // For plan upgrades, check if user already has credits for this specific plan
      const { data: existingPlanCredits } = await supabase
        .from('credit_transactions')
        .select('id')
        .eq('user_id', userId)
        .eq('plan_id', planId)
        .eq('transaction_type', 'subscription')
        .limit(1);

      if (!existingPlanCredits || existingPlanCredits.length === 0) {
        const expiresAt = plan.credits_expire_days > 0
          ? new Date(Date.now() + plan.credits_expire_days * 24 * 60 * 60 * 1000).toISOString()
          : null;

        // Insert credit transaction
        const { error: creditError } = await supabase
          .from('credit_transactions')
          .insert({
            user_id: userId,
            amount: plan.credits_included,
            description: `Clerk subscription sync: ${plan.name} plan`,
            transaction_type: 'subscription',
            expires_at: expiresAt,
            plan_id: planId,
          });

        if (creditError) {
          console.error('❌ Error setting credits:', creditError);
        } else {
          // For plan changes: SET credits to new plan amount (expire old credits first)
          console.log(`🔄 Plan change detected: Setting credits to ${plan.credits_included}`);

          // Expire all existing credits for this user
          await supabase
            .from('credit_transactions')
            .update({ is_expired: true })
            .eq('user_id', userId)
            .eq('is_expired', false);

          // Set user's total credits to the new plan amount
          await supabase
            .from('users')
            .update({
              credits: plan.credits_included,
              credits_expire_at: expiresAt,
            })
            .eq('id', userId);

          creditsAdded = plan.credits_included;
          console.log(`✅ Set credits to ${plan.credits_included} for user ${userId} (plan change)`);
        }
      } else {
        console.log(`⚠️ User already has credits for ${planId} plan, skipping credit addition`);

        // Still update the user's total credits in case they changed
        const { data: totalCredits, error: totalError } = await supabase
          .from('credit_transactions')
          .select('amount')
          .eq('user_id', userId)
          .eq('is_expired', false);

        if (!totalError) {
          const newTotal = totalCredits?.reduce((sum: number, transaction: any) => sum + (transaction.amount || 0), 0) || 0;

          await supabase
            .from('users')
            .update({
              credits: newTotal,
            })
            .eq('id', userId);
        }
      }
    }

    // Record subscription history
    await supabase
      .from('subscription_history')
      .insert({
        user_id: userId,
        plan_id: planId,
        action: 'clerk_sync',
        effective_date: new Date().toISOString(),
        metadata: {
          clerk_plan: clerkPlan,
          sync_method: 'session_claims',
          credits_added: creditsAdded,
        },
      });

    // Get updated user data
    const { data: updatedUser, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    console.log(`✅ Clerk subscription synced successfully for user ${userId}: ${planId} plan`);

    return NextResponse.json({
      success: true,
      message: `Successfully synced to ${plan.name} plan`,
      results: {
        clerkPlan: clerkPlan,
        mappedPlan: planId,
        planName: plan.name,
        creditsAdded: creditsAdded,
        user: updatedUser,
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('❌ Error syncing Clerk subscription:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Clerk subscription sync endpoint',
    description: 'Syncs user subscription from Clerk session claims to database',
    usage: 'POST to sync current user subscription',
    note: 'Reads from sessionClaims.pla field to determine subscription status',
  });
}
