import { NextResponse } from 'next/server';
import { auth, clerkClient } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

// Plan configuration
const SUBSCRIPTION_PLANS = {
  standard: {
    name: 'Standard',
    credits: 700,
    monthlyPrice: 7,
    yearlyPrice: 60,
  },
  pro: {
    name: 'Pro',
    credits: 3500,
    monthlyPrice: 26,
    yearlyPrice: 192,
  },
  premium: {
    name: 'Premium',
    credits: 14500,
    monthlyPrice: 78,
    yearlyPrice: 561,
  },
};

export async function POST(request: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const { priceId, planType, isYearly } = await request.json();

    console.log(`💳 Creating subscription checkout session for user ${userId}: ${planType} (${isYearly ? 'yearly' : 'monthly'})`);

    const supabase = createServiceRoleSupabaseClient();

    // Get or create Stripe customer
    let customerId: string;
    let userEmail: string;

    // Check if user already has a Stripe customer ID
    const { data: existingUser, error: userError } = await supabase
      .from('users')
      .select('stripe_customer_id, email, first_name, last_name')
      .eq('id', userId)
      .single();

    if (userError || !existingUser) {
      // User doesn't exist in Supabase yet - fetch from Clerk
      console.log(`⚠️ User ${userId} not found in Supabase, fetching from Clerk...`);

      try {
        const client = await clerkClient();
        const clerkUser = await client.users.getUser(userId);

        if (!clerkUser) {
          return NextResponse.json({
            error: 'User not found in Clerk'
          }, { status: 404 });
        }

        userEmail = clerkUser.emailAddresses?.[0]?.emailAddress || '';

        // Create user in Supabase
        const { error: createError } = await supabase
          .from('users')
          .insert({
            id: userId,
            email: userEmail,
            username: clerkUser.username || null,
            first_name: clerkUser.firstName || null,
            last_name: clerkUser.lastName || null,
            avatar_url: clerkUser.imageUrl || null,
            credits: 5,
            subscription_type: 'Free',
          });

        if (createError) {
          console.error('❌ Error creating user in Supabase:', createError);
          return NextResponse.json({
            error: 'Failed to create user in database'
          }, { status: 500 });
        }

        // Also create initial credit transaction
        await supabase.from('credit_transactions').insert({
          user_id: userId,
          amount: 5,
          description: 'Initial signup credits',
          transaction_type: 'bonus',
        });

        console.log(`✅ Created user ${userId} in Supabase via checkout flow`);
      } catch (clerkError: any) {
        console.error('❌ Error fetching user from Clerk:', clerkError);
        return NextResponse.json({
          error: 'Failed to fetch user information'
        }, { status: 500 });
      }
    } else {
      userEmail = existingUser.email;
    }

    // Check if user has Stripe customer ID
    if (existingUser?.stripe_customer_id) {
      customerId = existingUser.stripe_customer_id;
    } else {
      // Create new Stripe customer
      const customer = await stripe.customers.create({
        email: userEmail,
        name: existingUser ? `${existingUser.first_name || ''} ${existingUser.last_name || ''}`.trim() : '',
        metadata: {
          clerk_user_id: userId,
        },
      });

      customerId = customer.id;

      // Update user with Stripe customer ID
      await supabase
        .from('users')
        .update({ stripe_customer_id: customerId })
        .eq('id', userId);

      console.log(`✅ Created Stripe customer ${customerId} for user ${userId}`);
    }

    // Validate that priceId is provided
    if (!priceId) {
      return NextResponse.json({
        error: 'Price ID is required'
      }, { status: 400 });
    }

    console.log(`💳 Using Stripe Price ID: ${priceId}`);

    // Create Stripe checkout session using the Price ID from database
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId, // Use the actual Stripe Price ID from database
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://guardiavision.com'}/dashboard?subscription=success&plan=${planType}`,
      cancel_url: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://guardiavision.com'}/dashboard?subscription=cancelled`,
      metadata: {
        userId: userId,
        planType: planType,
        priceId: priceId,
        type: 'subscription',
        billingInterval: isYearly ? 'year' : 'month',
      },
      // Configure billing descriptor for subscriptions
      subscription_data: {
        description: `GuardiaVision ${planType} Plan`,
        metadata: {
          company: 'GuardiaVision',
          product: 'Subscription',
          plan: planType,
          user_id: userId,
          price_id: priceId,
        },
      },
      // Additional checkout configuration
      billing_address_collection: 'auto',
      customer_update: {
        address: 'auto',
        name: 'auto',
      },
      allow_promotion_codes: true,
    });

    console.log(`✅ Created Stripe subscription checkout session: ${session.id} with price ${priceId}`);

    return NextResponse.json({
      success: true,
      sessionId: session.id,
      checkoutUrl: session.url,
      planType: planType,
      priceId: priceId,
      message: `Checkout session created for ${planType} plan`,
    });

  } catch (error: any) {
    console.error('❌ Error creating subscription checkout session:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Stripe subscription checkout endpoint',
    description: 'Creates Stripe checkout sessions for subscription plans',
    usage: 'POST with { "priceId": "price_id", "planType": "standard|pro|premium", "isYearly": boolean }',
    plans: SUBSCRIPTION_PLANS,
    flow: [
      '1. User selects subscription plan',
      '2. Stripe checkout session created with proper billing descriptor',
      '3. User completes payment',
      '4. Webhook processes subscription and adds credits',
      '5. User redirected back to dashboard with new subscription',
    ],
  });
}
