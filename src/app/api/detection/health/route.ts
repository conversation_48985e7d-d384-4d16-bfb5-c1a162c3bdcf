import { NextResponse } from 'next/server';

/**
 * Health check endpoint for RunPod backend
 * Returns online if backend is properly configured
 */
export async function GET() {
  try {
    const backendUrl = process.env.NEXT_PUBLIC_RUNPOD_PROXY_URL;

    if (!backendUrl) {
      return NextResponse.json(
        {
          status: 'offline',
          message: 'Backend URL not configured',
          timestamp: new Date().toISOString()
        },
        { status: 503 }
      );
    }

    // Try to ping the backend
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3000); // 3 second timeout

      const response = await fetch(`${backendUrl}/health`, {
        method: 'GET',
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        return NextResponse.json({
          status: 'online',
          backend: 'runpod',
          timestamp: new Date().toISOString()
        });
      } else {
        return NextResponse.json(
          {
            status: 'offline',
            message: 'Backend not responding correctly',
            statusCode: response.status,
            timestamp: new Date().toISOString()
          },
          { status: 503 }
        );
      }
    } catch (fetchError) {
      // Backend might not have a health endpoint, but URL is configured
      // Return online if URL is configured (graceful degradation)
      console.warn('Backend health check failed, but URL is configured:', fetchError);
      return NextResponse.json({
        status: 'online',
        backend: 'runpod',
        note: 'URL configured, health endpoint unavailable',
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Health check error:', error);
    return NextResponse.json(
      {
        status: 'offline',
        message: 'Health check failed',
        timestamp: new Date().toISOString()
      },
      { status: 503 }
    );
  }
}
