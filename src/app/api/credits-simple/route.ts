import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

export async function GET(request: NextRequest) {
  try {
    const { userId } = auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Always return 5 credits and Free subscription
    return NextResponse.json({
      credits: 5,
      subscription_type: 'Free'
    });
  } catch (error) {
    console.error('API: Error in simple credits endpoint', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error },
      { status: 500 }
    );
  }
}
