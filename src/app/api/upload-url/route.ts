// src/app/api/upload-url/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const requestBody = await request.json();
    console.log('📥 Received request body:', requestBody);

    const { fileName, fileType, fileSize } = requestBody;
    console.log('📋 Extracted values:', { fileName, fileType, fileSize });

    // Validate required fields
    if (!fileName || !fileType || !fileSize) {
      console.error('❌ Missing required fields:', { fileName, fileType, fileSize });
      return NextResponse.json(
        {
          error: 'Missing required fields',
          details: {
            fileName: fileName ? 'present' : 'missing',
            fileType: fileType ? 'present' : 'missing',
            fileSize: fileSize ? 'present' : 'missing'
          }
        },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedTypes = [
      'image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif',
      'video/mp4', 'video/quicktime', 'video/webm', 'video/avi', 'video/x-msvideo'
    ];

    const normalizedFileType = fileType.toLowerCase();
    if (!allowedTypes.includes(normalizedFileType)) {
      console.error('❌ File type not allowed:', fileType);
      return NextResponse.json(
        {
          error: `File type "${fileType}" not allowed`,
          allowedTypes: allowedTypes
        },
        { status: 400 }
      );
    }

    // Validate file size
    const isVideo = normalizedFileType.startsWith('video/');
    const maxSize = isVideo ? 50 * 1024 * 1024 : 10 * 1024 * 1024;

    if (fileSize > maxSize) {
      const maxSizeMB = maxSize / (1024 * 1024);
      return NextResponse.json(
        { error: `File too large. Max size: ${maxSizeMB}MB` },
        { status: 400 }
      );
    }

    // Create unique file path
    const timestamp = Date.now();
    const sanitizedName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_');
    const filePath = `${userId}/uploads/${timestamp}_${sanitizedName}`;

    // Generate signed upload URL (expires in 10 minutes)
    const { data, error } = await supabase.storage
      .from('user-uploads')
      .createSignedUploadUrl(filePath, {
        upsert: false,
      });

    if (error) {
      console.error('❌ Failed to create signed URL:', error);
      return NextResponse.json(
        { error: `Failed to create upload URL: ${error.message}` },
        { status: 500 }
      );
    }

    console.log('✅ Generated signed URL for:', filePath);

    return NextResponse.json({
      success: true,
      uploadUrl: data.signedUrl,
      filePath: data.path,
      fileName: sanitizedName,
      fileType: isVideo ? 'video' : 'image'
    });

  } catch (error: any) {
    console.error('❌ Upload URL generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate upload URL', details: error.message },
      { status: 500 }
    );
  }
}
