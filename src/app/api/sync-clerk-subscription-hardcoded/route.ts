import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

// HARDCODED CORRECT VALUES - Override any external configuration
const HARDCODED_PLAN_DATA = {
  free: {
    id: 'free',
    name: 'Free',
    credits_included: 50,
    credits_expire_days: 30,
  },
  standard: {
    id: 'standard',
    name: 'Standard',
    credits_included: 700,
    credits_expire_days: 30,
  },
  pro: {
    id: 'pro',
    name: 'Pro',
    credits_included: 3500,  // NOT 1000749
    credits_expire_days: 0,  // No expiration
  },
  premium: {
    id: 'premium',
    name: 'Premium',
    credits_included: 14500, // NOT 2000749
    credits_expire_days: 0,  // No expiration
  },
};

export async function POST() {
  try {
    // Get the current user and session claims
    const { userId, sessionClaims } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    console.log(`🔄 Syncing with HARDCODED values for user ${userId}`);
    console.log(`📋 Session claims:`, JSON.stringify(sessionClaims, null, 2));

    const supabase = createServiceRoleSupabaseClient();

    // Extract plan from Clerk's session claims
    const clerkPlan = sessionClaims?.pla as string;

    if (!clerkPlan) {
      return NextResponse.json({
        success: false,
        error: 'No plan information found in session claims'
      }, { status: 400 });
    }

    console.log(`📋 Raw plan from session: ${clerkPlan}`);

    // Parse the plan
    const planName = clerkPlan.replace('u:', '').replace('_user', '');
    const planId = planName === 'free' ? 'free' : planName;

    console.log(`🔄 Mapping Clerk plan "${planName}" to our plan "${planId}"`);

    // Get HARDCODED plan data (ignore database/external config)
    const plan = HARDCODED_PLAN_DATA[planId as keyof typeof HARDCODED_PLAN_DATA];

    if (!plan) {
      return NextResponse.json({
        success: false,
        error: `Unknown plan: ${planId}`,
        availablePlans: Object.keys(HARDCODED_PLAN_DATA)
      }, { status: 400 });
    }

    console.log(`📋 Using HARDCODED plan data:`, plan);

    // Get current user data
    const { data: currentUser, error: currentUserError } = await supabase
      .from('users')
      .select('subscription_type, credits, last_payment_date')
      .eq('id', userId)
      .single();

    if (currentUserError) {
      console.error('❌ Error fetching current user:', currentUserError);
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch current user data'
      }, { status: 500 });
    }

    const currentPlanId = currentUser?.subscription_type?.toLowerCase() || 'free';
    console.log(`📋 Current plan: ${currentPlanId}, Target plan: ${planId}`);

    // Check if this is a plan change or same plan
    const isPlanChange = currentPlanId !== planId;
    const isNewSubscription = currentPlanId === 'free' && planId !== 'free';

    if (!isPlanChange && !isNewSubscription) {
      console.log(`✅ User already has ${planId} plan, no sync needed`);
      return NextResponse.json({
        success: true,
        message: `User already has ${plan.name} plan`,
        currentPlan: plan.name,
        noChangeNeeded: true,
      });
    }

    console.log(`🔄 ${isPlanChange ? 'Plan change' : 'New subscription'}: ${currentPlanId} → ${planId}`);

    // Update user subscription details
    const { error: userUpdateError } = await supabase
      .from('users')
      .update({
        subscription_type: plan.name,
        subscription_status: 'active',
        last_payment_date: planId !== 'free' ? new Date().toISOString() : currentUser.last_payment_date,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId);

    if (userUpdateError) {
      console.error('❌ Error updating user subscription:', userUpdateError);
      return NextResponse.json({
        success: false,
        error: 'Failed to update user subscription'
      }, { status: 500 });
    }

    // Handle credits for plan changes and new subscriptions
    let creditsAdded = 0;
    if (plan.credits_included > 0 && planId !== 'free') {
      const expiresAt = plan.credits_expire_days > 0
        ? new Date(Date.now() + plan.credits_expire_days * 24 * 60 * 60 * 1000).toISOString()
        : null;

      if (isPlanChange || isNewSubscription) {
        // For plan changes or new subscriptions: SET credits to new plan amount
        console.log(`🔄 ${isPlanChange ? 'Plan change' : 'New subscription'}: Setting credits to ${plan.credits_included}`);

        // Expire all existing credits for this user
        await supabase
          .from('credit_transactions')
          .update({ is_expired: true })
          .eq('user_id', userId)
          .eq('is_expired', false);

        // Insert new credit transaction for the new plan
        const { error: creditError } = await supabase
          .from('credit_transactions')
          .insert({
            user_id: userId,
            amount: plan.credits_included,
            description: `${isPlanChange ? 'Plan change' : 'New subscription'}: ${plan.name} plan (${plan.credits_included} credits)`,
            transaction_type: 'subscription',
            expires_at: expiresAt,
            plan_id: planId,
          });

        if (creditError) {
          console.error('❌ Error setting credits:', creditError);
        } else {
          creditsAdded = plan.credits_included;
          console.log(`✅ Set credits to ${creditsAdded} for ${planId} plan (${isPlanChange ? 'plan change' : 'new subscription'})`);
        }
      }
    }

    // Set user's total credits to the new plan amount
    if (isPlanChange || isNewSubscription) {
      await supabase
        .from('users')
        .update({
          credits: plan.credits_included,
          credits_expire_at: plan.credits_expire_days > 0 ?
            new Date(Date.now() + plan.credits_expire_days * 24 * 60 * 60 * 1000).toISOString() : null,
        })
        .eq('id', userId);

      console.log(`✅ Set total credits to ${plan.credits_included} (${isPlanChange ? 'plan change' : 'new subscription'})`);
    }

    // Record subscription history
    await supabase
      .from('subscription_history')
      .insert({
        user_id: userId,
        plan_id: planId,
        action: 'hardcoded_sync',
        effective_date: new Date().toISOString(),
        metadata: {
          clerk_plan: clerkPlan,
          sync_method: 'hardcoded_values',
          credits_added: creditsAdded,
          hardcoded_credits: plan.credits_included,
        },
      });

    // Get updated user data
    const { data: updatedUser, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    console.log(`✅ HARDCODED sync completed for user ${userId}: ${planId} plan`);

    return NextResponse.json({
      success: true,
      message: `Successfully synced to ${plan.name} plan using HARDCODED values`,
      results: {
        clerkPlan: clerkPlan,
        mappedPlan: planId,
        planName: plan.name,
        creditsAdded: creditsAdded,
        totalCredits: updatedUser?.credits || 0,
        hardcodedCredits: plan.credits_included,
        user: updatedUser,
      },
      note: 'Used hardcoded credit values to override any external configuration',
      timestamp: new Date().toISOString(),
    });

  } catch (error: any) {
    console.error('❌ Error in hardcoded sync:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Hardcoded Clerk subscription sync endpoint',
    description: 'Uses hardcoded correct credit values instead of database/external config',
    hardcodedValues: HARDCODED_PLAN_DATA,
    usage: 'POST to sync with guaranteed correct credit amounts',
    note: 'This bypasses any incorrect Clerk/Stripe/Database configuration',
  });
}
