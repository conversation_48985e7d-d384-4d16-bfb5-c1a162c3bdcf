import { type NextRequest, NextResponse } from 'next/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';
import crypto from 'crypto';

export async function POST(request: NextRequest) {
  try {
    const { fingerprint } = await request.json();

    // Get IP address from request headers
    const ip = request.headers.get('x-forwarded-for')?.split(',')[0] ||
               request.headers.get('x-real-ip') ||
               'unknown';

    // Create unique identifier by hashing IP + fingerprint
    const identifier = crypto
      .createHash('sha256')
      .update(`${ip}-${fingerprint || 'no-fingerprint'}`)
      .digest('hex');

    // Initialize Supabase client
    const supabase = createServiceRoleSupabaseClient();

    // Check submission count for this identifier
    const { data: existingSubmissions, error: countError } = await supabase
      .from('feedback')
      .select('id, created_at')
      .eq('identifier', identifier);

    if (countError) {
      console.error('❌ Error checking submission count:', countError);
      return NextResponse.json(
        { error: 'Failed to check rate limit' },
        { status: 500 },
      );
    }

    const submissionCount = existingSubmissions?.length || 0;
    const remainingSubmissions = Math.max(0, 2 - submissionCount);
    const isBlocked = submissionCount >= 2;

    return NextResponse.json({
      success: true,
      submissionCount,
      remainingSubmissions,
      maxSubmissions: 2,
      isBlocked,
      identifier: identifier.substring(0, 8) + '...', // Only return partial identifier for privacy
    });
  } catch (error) {
    console.error('Error checking rate limit:', error);
    return NextResponse.json(
      { error: 'Failed to check rate limit' },
      { status: 500 },
    );
  }
}
