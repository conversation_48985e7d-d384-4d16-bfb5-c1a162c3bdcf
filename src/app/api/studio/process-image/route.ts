import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

// Your Google Cloud AI model endpoint
const API_BASE_URL = 'https://guardiavision-prod-448517754159.europe-west1.run.app';

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse form data
    const formData = await request.formData();
    const imageFile = formData.get('image') as File;
    const textPrompt = formData.get('text_prompt') as string;
    const scoreThreshold = formData.get('score_threshold') as string;
    const visualize = formData.get('visualize') as string;
    const blurDetections = formData.get('blur_detections') as string;
    const blurStrength = formData.get('blur_strength') as string;
    const blurType = formData.get('blur_type') as string;

    if (!imageFile) {
      return NextResponse.json({ error: 'No image file provided' }, { status: 400 });
    }

    console.log('🤖 Studio API - Processing image with settings:', {
      textPrompt: textPrompt || 'person',
      scoreThreshold: scoreThreshold || '0.5',
      visualize: visualize || 'false',
      blurDetections: blurDetections || 'false',
      blurStrength: blurStrength || '25',
      blurType: blurType || 'blur',
      fileName: imageFile.name,
      fileSize: imageFile.size
    });

    // Create form data for AI model - exactly like blur-me-exact.tsx
    const aiFormData = new FormData();
    aiFormData.append('image', imageFile);
    aiFormData.append('text_prompt', textPrompt || 'person');
    aiFormData.append('score_threshold', scoreThreshold || '0.5');
    aiFormData.append('visualize', visualize || 'false');
    aiFormData.append('blur_detections', blurDetections || 'false');
    aiFormData.append('blur_strength', blurStrength || '25');
    aiFormData.append('blur_type', blurType || 'blur');

    console.log('🚀 Sending request to Google Cloud AI model:', `${API_BASE_URL}/process_image`);

    // Send request to your Google Cloud AI model
    const aiResponse = await fetch(`${API_BASE_URL}/process_image`, {
      method: 'POST',
      body: aiFormData,
      headers: {
        'Accept': 'application/json',
      },
    });

    if (!aiResponse.ok) {
      const errorText = await aiResponse.text();
      console.error('❌ Google Cloud AI model error:', {
        status: aiResponse.status,
        statusText: aiResponse.statusText,
        error: errorText
      });
      
      return NextResponse.json({ 
        error: `AI processing failed: ${aiResponse.status} ${aiResponse.statusText}`,
        details: errorText
      }, { status: aiResponse.status });
    }

    // Check if response is an image or JSON
    const contentType = aiResponse.headers.get('content-type');
    
    if (contentType?.startsWith('image/')) {
      // Return processed image directly
      const imageBuffer = await aiResponse.arrayBuffer();
      const fileName = aiResponse.headers.get('content-disposition')?.match(/filename\*?=([^;]+)/)?.[1] || 'processed_image.jpg';
      
      console.log('✅ AI processing successful - returning processed image');
      
      return new NextResponse(imageBuffer, {
        status: 200,
        headers: {
          'Content-Type': contentType,
          'Content-Disposition': `attachment; filename="${fileName.replace(/['"]/g, '')}"`,
          'Content-Length': imageBuffer.byteLength.toString(),
        },
      });
    } else {
      // Return JSON response with full result
      const result = await aiResponse.json();
      console.log('✅ AI processing successful - returning JSON result:', {
        hasDetections: !!result.detections,
        detectionsCount: result.detections?.length || 0,
        hasBboxes: !!result.bboxes,
        bboxesCount: result.bboxes?.length || 0,
        hasProcessedImageUrl: !!result.processed_image_url,
        hasDownloadUrl: !!result.download_url
      });
      
      return NextResponse.json({
        success: true,
        ...result // Return the full result from your AI model
      });
    }

  } catch (error: any) {
    console.error('❌ Studio API error:', error);
    
    return NextResponse.json({
      error: 'Internal server error',
      details: error.message
    }, { status: 500 });
  }
}

// Custom blur areas endpoint - for precise bounding box blur
export async function PUT(request: NextRequest) {
  try {
    // Authenticate user
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse form data
    const formData = await request.formData();
    const imageFile = formData.get('image') as File;
    const blurAreas = formData.get('blur_areas') as string;
    const blurStrength = formData.get('blur_strength') as string;
    const blurType = formData.get('blur_type') as string;

    if (!imageFile) {
      return NextResponse.json({ error: 'No image file provided' }, { status: 400 });
    }

    if (!blurAreas) {
      return NextResponse.json({ error: 'No blur areas provided' }, { status: 400 });
    }

    console.log('🎯 Studio API - Custom blur areas request:', {
      blurAreas: JSON.parse(blurAreas),
      blurStrength: blurStrength || '25',
      blurType: blurType || 'blur',
      fileName: imageFile.name
    });

    // Create form data for AI model custom blur endpoint
    const aiFormData = new FormData();
    aiFormData.append('image', imageFile);
    aiFormData.append('blur_areas', blurAreas);
    aiFormData.append('blur_strength', blurStrength || '25');
    aiFormData.append('blur_type', blurType || 'blur');

    console.log('🚀 Sending custom blur request to:', `${API_BASE_URL}/custom_blur_areas`);

    // Send request to your Google Cloud AI model custom blur endpoint
    const aiResponse = await fetch(`${API_BASE_URL}/custom_blur_areas`, {
      method: 'POST',
      body: aiFormData,
      headers: {
        'Accept': 'application/json',
      },
    });

    if (!aiResponse.ok) {
      const errorText = await aiResponse.text();
      console.error('❌ Custom blur error:', {
        status: aiResponse.status,
        statusText: aiResponse.statusText,
        error: errorText
      });
      
      return NextResponse.json({ 
        error: `Custom blur failed: ${aiResponse.status} ${aiResponse.statusText}`,
        details: errorText
      }, { status: aiResponse.status });
    }

    // Return JSON response
    const result = await aiResponse.json();
    console.log('✅ Custom blur successful');
    
    return NextResponse.json({
      success: true,
      ...result
    });

  } catch (error: any) {
    console.error('❌ Custom blur API error:', error);
    
    return NextResponse.json({
      error: 'Internal server error',
      details: error.message
    }, { status: 500 });
  }
}
