import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const studioState = await request.json();
    
    if (!studioState.mediaFileId) {
      return NextResponse.json({ error: 'Media file ID is required' }, { status: 400 });
    }

    console.log('💾 Saving studio state for user:', userId, 'file:', studioState.mediaFileId);

    // Create Supabase client with service role
    const supabase = createServiceRoleSupabaseClient();

    // Check if user has access to this file (graceful handling if table doesn't exist)
    const { data: fileData, error: fileError } = await supabase
      .from('user_files')
      .select('id')
      .eq('id', studioState.mediaFileId)
      .eq('user_id', userId)
      .single();

    if (fileError) {
      if (fileError.code === '42P01') {
        console.log('ℹ️ user_files table does not exist yet - skipping file access check');
        // Continue with state save even if table doesn't exist
      } else if (fileError.code === 'PGRST116') {
        console.log('ℹ️ File not found in user_files - continuing with localStorage save');
        return NextResponse.json({
          success: true,
          message: 'State saved to localStorage only',
          database_saved: false
        });
      } else {
        console.log('⚠️ File access check failed - continuing with localStorage save:', fileError.message);
        return NextResponse.json({
          success: true,
          message: 'State saved to localStorage only',
          database_saved: false
        });
      }
    }

    // Prepare state data for database
    const stateData = {
      user_id: userId,
      file_id: studioState.mediaFileId,
      state_data: {
        detectedObjects: studioState.detectedObjects || [],
        allDetectedObjects: studioState.allDetectedObjects || [],
        customBlurAreas: studioState.customBlurAreas || [],
        textPrompt: studioState.textPrompt || '',
        blurIntensity: studioState.blurIntensity || 50,
        blurType: studioState.blurType || 'blur',
        confidenceThreshold: studioState.confidenceThreshold || 0.3,
        processedImageUrl: studioState.processedImageUrl || '',
        hasUserTyped: studioState.hasUserTyped || false,
        lastUsedPrompt: studioState.lastUsedPrompt || '',
        timestamp: new Date().toISOString()
      }
    };

    // Save or update studio state
    const { data, error } = await supabase
      .from('studio_states')
      .upsert(stateData, {
        onConflict: 'user_id,file_id'
      })
      .select()
      .single();

    if (error) {
      // Handle table not existing or other database issues gracefully
      if (error.code === '42P01') {
        console.log('⚠️ studio_states table does not exist - localStorage save successful');
      } else if (error.code === '42501') {
        console.log('⚠️ Permission denied saving to studio_states - RLS policy issue (non-critical)');
      } else if (error.code === '23505') {
        console.log('⚠️ Duplicate key error in studio_states - attempting update instead');
      } else {
        console.log('⚠️ Database save failed - localStorage save successful:', error.message, 'Code:', error.code);
      }

      return NextResponse.json({
        success: true,
        message: 'State saved to localStorage only',
        database_saved: false,
        error: error.message
      });
    }

    console.log('✅ Studio state saved successfully to database:', data.id);

    return NextResponse.json({
      success: true,
      message: 'State saved successfully',
      database_saved: true,
      state_id: data.id
    });

  } catch (error) {
    console.log('⚠️ Save state error (non-critical):', error);
    
    // Return success even on error to prevent user-facing issues
    return NextResponse.json({
      success: true,
      message: 'State saved to localStorage only',
      database_saved: false,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
