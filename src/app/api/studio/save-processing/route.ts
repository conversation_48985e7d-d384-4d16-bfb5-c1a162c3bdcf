import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      original_file_id,
      processed_image_url,
      processing_settings,
      file_name,
      created_at
    } = body;

    console.log('💾 Saving processing state for user:', userId);
    console.log('📁 Original file ID:', original_file_id);
    console.log('🎨 Processing settings:', processing_settings);

    // Create Supabase client with service role
    const supabase = createServiceRoleSupabaseClient();

    // Save the processing state to database
    const { data, error } = await supabase
      .from('processed_files')
      .insert({
        user_id: userId,
        original_file_id,
        processed_image_url,
        processing_settings,
        file_name,
        created_at: created_at || new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('❌ Database error:', error);
      return NextResponse.json(
        { error: 'Failed to save processing state', details: error.message },
        { status: 500 }
      );
    }

    console.log('✅ Processing state saved successfully:', data.id);

    return NextResponse.json({
      success: true,
      processing_id: data.id,
      message: 'Processing state saved successfully'
    });

  } catch (error) {
    console.error('❌ Error saving processing state:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
