import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get file ID from query parameters
    const { searchParams } = new URL(request.url);
    const fileId = searchParams.get('fileId');

    if (!fileId) {
      return NextResponse.json({ error: 'File ID is required' }, { status: 400 });
    }

    console.log('🔍 Loading processing state for user:', userId, 'file:', fileId);

    // Create Supabase client with service role
    const supabase = createServiceRoleSupabaseClient();

    // Get the most recent processing state for this file
    const { data, error } = await supabase
      .from('processed_files')
      .select('*')
      .eq('user_id', userId)
      .eq('original_file_id', fileId)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No processing state found - this is normal
        console.log('ℹ️ No processing state found for file:', fileId);
        return NextResponse.json({
          found: false,
          message: 'No processing state found for this file'
        });
      }

      if (error.code === '42P01') {
        // Table doesn't exist - handle gracefully
        console.log('ℹ️ processed_files table does not exist yet - this is normal for new installations');
        return NextResponse.json({
          found: false,
          message: 'No processing state found for this file'
        });
      }

      if (error.code === '42501') {
        // Permission denied - handle gracefully
        console.log('⚠️ Permission denied accessing processed_files - RLS policy issue (non-critical)');
        return NextResponse.json({
          found: false,
          message: 'No processing state available'
        });
      }

      // CRITICAL FIX: Handle all database errors gracefully
      console.log('⚠️ Database error (non-critical):', error.message, 'Code:', error.code);
      return NextResponse.json({
        found: false,
        message: 'No processing state available',
        error: 'Database temporarily unavailable'
      });
    }

    console.log('✅ Processing state loaded successfully:', data.id);

    return NextResponse.json({
      found: true,
      processing_state: {
        id: data.id,
        processed_image_url: data.processed_image_url,
        processing_settings: data.processing_settings,
        file_name: data.file_name,
        created_at: data.created_at
      }
    });

  } catch (error) {
    // CRITICAL FIX: Handle all errors gracefully to prevent 500 errors
    console.log('⚠️ Error loading processing state (non-critical):', error);
    return NextResponse.json({
      found: false,
      message: 'No processing state available',
      error: 'Service temporarily unavailable'
    });
  }
}
