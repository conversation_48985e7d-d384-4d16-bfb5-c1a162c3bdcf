import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get file ID from query parameters
    const { searchParams } = new URL(request.url);
    const fileId = searchParams.get('fileId');

    if (!fileId) {
      return NextResponse.json({ error: 'File ID is required' }, { status: 400 });
    }

    console.log('🔍 Loading studio state for user:', userId, 'file:', fileId);

    // Create Supabase client with service role
    const supabase = createServiceRoleSupabaseClient();

    // Get the studio state for this file
    const { data, error } = await supabase
      .from('studio_states')
      .select('*')
      .eq('user_id', userId)
      .eq('file_id', fileId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No studio state found - this is normal
        console.log('ℹ️ No studio state found for file:', fileId);
        return NextResponse.json({
          found: false,
          message: 'No studio state found for this file'
        });
      }

      if (error.code === '42P01') {
        // Table doesn't exist yet - this is normal for new installations
        console.log('ℹ️ studio_states table does not exist yet');
        return NextResponse.json({
          found: false,
          message: 'No studio state found for this file'
        });
      }

      // Handle other database errors gracefully
      console.log('⚠️ Database error (non-critical):', error.message);
      return NextResponse.json({
        found: false,
        message: 'No studio state available',
        error: 'Database temporarily unavailable'
      });
    }

    console.log('✅ Studio state loaded successfully:', data.id);

    return NextResponse.json({
      found: true,
      studio_state: {
        id: data.id,
        state_data: data.state_data,
        created_at: data.created_at,
        updated_at: data.updated_at
      }
    });

  } catch (error) {
    // Handle all errors gracefully to prevent 500 errors
    console.log('⚠️ Error loading studio state (non-critical):', error);
    return NextResponse.json({
      found: false,
      message: 'No studio state available',
      error: 'Service temporarily unavailable'
    });
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
