// src/app/api/upload-complete/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Extract ALL the fields being sent from frontend
    const { 
      filePath, 
      originalFileName, 
      fileType, 
      fileSize, 
      mimeType, 
      thumbnail,        // ← Now handling thumbnail
      videoMetadata     // ← Now handling videoMetadata
    } = await request.json();

    console.log('📝 Processing upload completion for:', originalFileName, {
      fileType,
      hasThumnail: !!thumbnail,
      hasVideoMetadata: !!videoMetadata
    });

    // Get public URL
    const { data: urlData } = supabase.storage
      .from('user-uploads')
      .getPublicUrl(filePath);

    // Create enhanced metadata object
    const metadata = {
      originalUrl: urlData.publicUrl,
      uploadedAt: new Date().toISOString(),
      directUpload: true,
      // Add video metadata if present
      ...(videoMetadata && {
        duration: videoMetadata.duration,
        width: videoMetadata.width,
        height: videoMetadata.height,
        aspectRatio: videoMetadata.width / videoMetadata.height
      }),
      // Add thumbnail info if present (but don't store the actual data URL)
      ...(thumbnail && {
        hasThumbnail: true,
        thumbnailType: thumbnail.startsWith('data:') ? 'dataURL' : 'objectURL'
      })
    };

    // Create database record
    const fileRecord = {
      user_id: userId,
      original_filename: originalFileName,
      file_path: filePath,
      file_type: fileType,
      file_size: fileSize,
      mime_type: mimeType,
      processing_status: 'completed',
      processing_progress: 100,
      metadata: metadata
    };

    console.log('💾 Saving to database:', {
      filename: originalFileName,
      type: fileType,
      size: fileSize
    });

    const { data: dbData, error: dbError } = await supabase
      .from('user_files')
      .insert(fileRecord)
      .select('*')
      .single();

    if (dbError) {
      console.error('❌ Database save failed:', dbError);
      // Clean up uploaded file
      await supabase.storage.from('user-uploads').remove([filePath]);
      return NextResponse.json(
        { error: `Database error: ${dbError.message}` },
        { status: 500 }
      );
    }

    console.log('✅ Upload completed successfully for:', originalFileName);

    return NextResponse.json({
      success: true,
      file: {
        id: dbData.id,
        filename: dbData.original_filename,
        type: dbData.file_type,
        size: dbData.file_size,
        url: urlData.publicUrl,
        created_at: dbData.created_at,
        metadata: dbData.metadata
      }
    });

  } catch (error: any) {
    console.error('❌ Upload completion error:', error);
    return NextResponse.json(
      { error: 'Failed to complete upload', details: error.message },
      { status: 500 }
    );
  }
}