import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

const AI_MODEL_URL = 'https://guardiavision-prod-448517754159.europe-west3.run.app';

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse form data
    const formData = await request.formData();
    const imageFile = formData.get('image') as File;
    const textPrompt = formData.get('text_prompt') as string;
    const scoreThreshold = formData.get('score_threshold') as string;
    const visualize = formData.get('visualize') as string;
    const blurDetections = formData.get('blur_detections') as string;
    const blurStrength = formData.get('blur_strength') as string;

    if (!imageFile) {
      return NextResponse.json({ error: 'No image file provided' }, { status: 400 });
    }

    // Validate file type
    if (!imageFile.type.startsWith('image/')) {
      return NextResponse.json({ error: 'File must be an image' }, { status: 400 });
    }

    // Create form data for AI model
    const aiFormData = new FormData();
    aiFormData.append('image', imageFile);
    aiFormData.append('text_prompt', textPrompt || 'person');
    aiFormData.append('score_threshold', scoreThreshold || '0.5');
    aiFormData.append('visualize', visualize || 'true');
    aiFormData.append('blur_detections', blurDetections || 'false');
    aiFormData.append('blur_strength', blurStrength || '25');

    console.log('🤖 Sending request to AI model:', {
      url: `${AI_MODEL_URL}/process_image`,
      textPrompt: textPrompt || 'person',
      scoreThreshold: scoreThreshold || '0.5',
      visualize: visualize || 'true',
      blurDetections: blurDetections || 'false',
      blurStrength: blurStrength || '25',
      fileName: imageFile.name,
      fileSize: imageFile.size
    });

    // Send request to AI model
    const aiResponse = await fetch(`${AI_MODEL_URL}/process_image`, {
      method: 'POST',
      body: aiFormData,
      headers: {
        'Accept': 'application/json',
      },
    });

    if (!aiResponse.ok) {
      const errorText = await aiResponse.text();
      console.error('❌ AI model error:', {
        status: aiResponse.status,
        statusText: aiResponse.statusText,
        error: errorText
      });
      
      return NextResponse.json({ 
        error: `AI processing failed: ${aiResponse.status} ${aiResponse.statusText}`,
        details: errorText
      }, { status: aiResponse.status });
    }

    // Check if response is an image or JSON
    const contentType = aiResponse.headers.get('content-type');
    
    if (contentType?.startsWith('image/')) {
      // Return processed image
      const imageBuffer = await aiResponse.arrayBuffer();
      const fileName = aiResponse.headers.get('content-disposition')?.match(/filename\*?=([^;]+)/)?.[1] || 'processed_image.jpg';
      
      console.log('✅ AI processing successful - returning processed image');
      
      return new NextResponse(imageBuffer, {
        status: 200,
        headers: {
          'Content-Type': contentType,
          'Content-Disposition': `attachment; filename="${fileName.replace(/['"]/g, '')}"`,
          'Content-Length': imageBuffer.byteLength.toString(),
        },
      });
    } else {
      // Return JSON response
      const result = await aiResponse.json();
      console.log('✅ AI processing successful - returning JSON result');
      
      return NextResponse.json({
        success: true,
        result
      });
    }

  } catch (error: any) {
    console.error('❌ Error in AI processing:', error);
    
    return NextResponse.json({
      error: 'Internal server error during AI processing',
      details: error.message
    }, { status: 500 });
  }
}