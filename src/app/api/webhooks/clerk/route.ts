// File: app/api/webhooks/clerk/route.ts

import { Webhook } from 'svix';
import { headers } from 'next/headers';
import { WebhookEvent } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';
import { NextResponse } from 'next/server';

// This GET endpoint is for testing and does not need changes.
export async function GET() {
  return NextResponse.json({ message: 'Clerk webhook endpoint is working' });
}

export async function POST(req: Request) {
  const headerPayload = headers();
  const svix_id = headerPayload.get('svix-id');
  const svix_timestamp = headerPayload.get('svix-timestamp');
  const svix_signature = headerPayload.get('svix-signature');

  if (!svix_id || !svix_timestamp || !svix_signature) {
    return new Response('Missing svix headers', { status: 400 });
  }

  const payload = await req.json();
  const body = JSON.stringify(payload);
  const webhookSecret = process.env.CLERK_WEBHOOK_SECRET;

  if (!webhookSecret) {
    return new Response('Missing CLERK_WEBHOOK_SECRET', { status: 500 });
  }

  const wh = new Webhook(webhookSecret);
  let evt: WebhookEvent;

  try {
    evt = wh.verify(body, {
      'svix-id': svix_id,
      'svix-timestamp': svix_timestamp,
      'svix-signature': svix_signature,
    }) as WebhookEvent;
  } catch (err: any) {
    console.error('❌ WEBHOOK VERIFICATION FAILED:', err.message);
    return NextResponse.json({ error: 'Webhook verification failed' }, { status: 400 });
  }

  const eventType = evt.type;
  const supabase = createServiceRoleSupabaseClient();

  try {
    switch (eventType) {
      case 'user.created':
        const newUser = evt.data;
        await supabase.from('users').insert({
          id: newUser.id,
          email: newUser.email_addresses?.[0]?.email_address || '',
          username: newUser.username || null,
          first_name: newUser.first_name || null,
          last_name: newUser.last_name || null,
          avatar_url: newUser.image_url || null,
          credits: 5,
          subscription_type: 'Free',
        });
        await supabase.from('credit_transactions').insert({
          user_id: newUser.id,
          amount: 5,
          description: 'Initial signup credits',
          transaction_type: 'bonus',
        });
        break;

      case 'user.updated':
        const updatedUser = evt.data;
        await supabase
          .from('users')
          .update({
            email: updatedUser.email_addresses?.[0]?.email_address || '',
            username: updatedUser.username || null,
            first_name: updatedUser.first_name || null,
            last_name: updatedUser.last_name || null,
            avatar_url: updatedUser.image_url || null,
            updated_at: new Date().toISOString(),
          })
          .eq('id', updatedUser.id);
        break;

      case 'user.deleted':
        const deletedUserId = evt.data.id;
        if (!deletedUserId) {
          throw new Error("User ID not found in webhook payload for deletion.");
        }
        console.log('🗑️ DELETING USER FROM SUPABASE:', deletedUserId);

        // --- ✨ THE FIX IS HERE ---
        // Step 1: Delete all records from the 'child' table (credit_transactions) first.
        console.log(`Deleting credit transactions for user: ${deletedUserId}`);
        const { error: transactionDeleteError } = await supabase
          .from('credit_transactions')
          .delete()
          .eq('user_id', deletedUserId);

        if (transactionDeleteError) {
          console.error('Error deleting credit transactions:', transactionDeleteError);
          throw transactionDeleteError; // Stop the process if this fails
        }
        console.log(`✅ Successfully deleted credit transactions for user: ${deletedUserId}`);
        
        // Step 2: Now that the child records are gone, delete the 'parent' record (the user).
        console.log(`Deleting user record: ${deletedUserId}`);
        const { error: userDeleteError } = await supabase
          .from('users')
          .delete()
          .eq('id', deletedUserId);

        if (userDeleteError) {
          console.error('Error deleting user:', userDeleteError);
          throw userDeleteError;
        }
        console.log(`✅ Successfully deleted user: ${deletedUserId}`);
        // --- END OF FIX ---
        break;
    }

    return NextResponse.json({ success: true, eventType });
  } catch (error: any) {
    console.error('❌ Error processing webhook:', error);
    return NextResponse.json({ success: false, error: error.message }, { status: 500 });
  }
}
