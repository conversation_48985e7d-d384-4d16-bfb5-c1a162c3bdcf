// File: app/api/webhooks/stripe/route.ts
// Robust webhook handler for all subscription scenarios

import Stripe from 'stripe';
import { NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { stripe } from '@/lib/stripe';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

const creditsPerPlan: { [key: string]: number } = {
    'price_1Rtczx8Ap4GWUvRfKowvGuGG': 700,
    'price_1Rta8u8Ap4GWUvRfreOwBwhw': 3500,
    'price_1RtaCC8Ap4GWUvRf30nF2vlN': 14000,
    'price_1Rta9g8Ap4GWUvRfnjvHIO10': 8400,
    'price_1RtaBl8Ap4GWUvRfDoGK2gdQ': 42000,
    'price_1RtaCU8Ap4GWUvRfiTS7yl6i': 168000,
};

const planNamePerPlan: { [key: string]: string } = {
    'price_1Rtczx8Ap4GWUvRfKowvGuGG': 'Standard',
    'price_1Rta8u8Ap4GWUvRfreOwBwhw': 'Pro',
    'price_1RtaCC8Ap4GWUvRf30nF2vlN': 'Premium',
    'price_1Rta9g8Ap4GWUvRfnjvHIO10': 'Standard',
    'price_1RtaBl8Ap4GWUvRfDoGK2gdQ': 'Pro',
    'price_1RtaCU8Ap4GWUvRfiTS7yl6i': 'Premium',

};

export async function POST(req: Request) {
  const body = await req.text();
  const signature = headers().get('Stripe-Signature');

  if (!signature) {
    return new NextResponse('Webhook Error: Missing Stripe-Signature header', { status: 400 });
  }

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
  } catch (error: any) {
    console.error('❌ Webhook signature verification failed:', error.message);
    return new NextResponse(`Webhook Error: ${error.message}`, { status: 400 });
  }

  const supabase = createServiceRoleSupabaseClient();
  const session = event.data.object as any;

  try {
    switch (event.type) {
      // ==========================================
      // NEW SUBSCRIPTION (Initial Purchase)
      // ==========================================
      case 'checkout.session.completed': {
        const userId = session.metadata?.userId;
        if (!userId) {
          console.error('❌ User ID not found in checkout session metadata');
          throw new Error('User ID not found in session metadata.');
        }

        console.log(`🎉 Processing new subscription for user: ${userId}`);

        const subscription = await stripe.subscriptions.retrieve(session.subscription as string);
        const priceId = subscription.items.data[0]?.price.id;
        const planName = priceId ? planNamePerPlan[priceId] : null;
        const creditsToAdd = priceId ? creditsPerPlan[priceId] : 0;

        if (!priceId || !planName) {
          console.error(`❌ Price ID ${priceId} not found in mapping`);
          throw new Error('Price or Plan not found.');
        }

        console.log(`📦 Plan: ${planName}, Credits: ${creditsToAdd}, Price ID: ${priceId}`);

        // Update subscription info AND clear any old expiration
        const { error: updateError } = await supabase
          .from('users')
          .update({
            stripe_customer_id: subscription.customer as string,
            stripe_subscription_id: subscription.id,
            clerk_plan_id: priceId,
            subscription_type: planName,
            subscription_status: 'active',
            subscription_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
            credits_expire_at: null,  // ✅ CRITICAL: Clear any old expiration
            updated_at: new Date().toISOString(),
          })
          .eq('id', userId);

        if (updateError) {
          console.error('❌ Error updating user subscription:', updateError);
          throw updateError;
        }

        // Add credits
        const { error: creditsError } = await supabase.rpc('add_user_credits', {
          user_clerk_id: userId,
          credits_to_add: creditsToAdd,
        });

        if (creditsError) {
          console.error('❌ Error adding credits:', creditsError);
          throw creditsError;
        }

        console.log(`✅ New subscription activated: ${userId} - ${planName} - ${creditsToAdd} credits`);
        break;
      }

      // ==========================================
      // RENEWAL (Monthly/Yearly Charge)
      // ==========================================
      case 'invoice.payment_succeeded': {
        // Skip first invoice - already handled by checkout.session.completed
        if (session.billing_reason === 'subscription_create') {
          console.log('ℹ️ Ignoring first payment invoice (handled by checkout.session.completed)');
          return new NextResponse('Ignoring first payment invoice, already handled.', { status: 200 });
        }

        const customerId = session.customer as string;
        const subscriptionId = session.subscription as string;

        if (!subscriptionId) {
          console.log('ℹ️ No subscription ID in invoice, skipping');
          break;
        }

        const { data: user } = await supabase
          .from('users')
          .select('id, email')
          .eq('stripe_customer_id', customerId)
          .single();

        if (!user) {
          console.error(`❌ User not found for customer ${customerId}`);
          break;
        }

        console.log(`🔄 Processing renewal for user: ${user.id} (${user.email})`);

        const subscription = await stripe.subscriptions.retrieve(subscriptionId);
        const priceId = subscription.items.data[0]?.price.id;
        const creditsToTopUp = priceId ? (creditsPerPlan[priceId] || 0) : 0;

        console.log(`📦 Renewal - Price: ${priceId}, Credits: ${creditsToTopUp}`);

        // Update period end, ensure status is active, clear any expiration
        const { error: updateError } = await supabase
          .from('users')
          .update({
            subscription_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
            subscription_status: 'active',     // ✅ Ensure active on renewal
            credits_expire_at: null,            // ✅ Clear any expiration
            updated_at: new Date().toISOString(),
          })
          .eq('id', user.id);

        if (updateError) {
          console.error('❌ Error updating renewal:', updateError);
          throw updateError;
        }

        // Add renewal credits
        if (creditsToTopUp > 0) {
          const { error: creditsError } = await supabase.rpc('add_user_credits', {
            user_clerk_id: user.id,
            credits_to_add: creditsToTopUp,
          });

          if (creditsError) {
            console.error('❌ Error adding renewal credits:', creditsError);
            throw creditsError;
          }
        }

        console.log(`✅ Renewal processed: ${user.id} - ${creditsToTopUp} credits added`);
        break;
      }

      // ==========================================
      // CANCELLATION (Set 30-Day Expiration)
      // ==========================================
      case 'customer.subscription.deleted': {
        const customerId = session.customer as string;

        const { data: user } = await supabase
          .from('users')
          .select('id, email, credits')
          .eq('stripe_customer_id', customerId)
          .single();

        if (!user) {
          console.error(`❌ User not found for customer ${customerId}`);
          break;
        }

        console.log(`🗑️ Processing cancellation for user: ${user.id} (${user.email}), credits: ${user.credits}`);

        // Update subscription status to cancelled
        const { error: updateError } = await supabase
          .from('users')
          .update({
            subscription_type: 'Free',
            subscription_status: 'cancelled',
            clerk_plan_id: null,
            stripe_subscription_id: null,
            subscription_period_end: null,
            updated_at: new Date().toISOString(),
          })
          .eq('id', user.id);

        if (updateError) {
          console.error('❌ Error updating cancellation status:', updateError);
          throw updateError;
        }

        // Set 30-day expiration ONLY if user has credits
        if (user.credits > 0) {
          const { error: expirationError } = await supabase.rpc('handle_subscription_cancellation', {
            p_user_id: user.id
          });

          if (expirationError) {
            console.error('❌ Error calling RPC, using direct update fallback:', expirationError);
            // Fallback to direct update if RPC fails
            const { error: directUpdateError } = await supabase
              .from('users')
              .update({
                credits_expire_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
              })
              .eq('id', user.id);

            if (directUpdateError) {
              console.error('❌ Direct update also failed:', directUpdateError);
              throw directUpdateError;
            } else {
              console.log(`✅ Cancellation processed via fallback: ${user.id} - ${user.credits} credits will expire in 30 days`);
            }
          } else {
            console.log(`✅ Cancellation processed: ${user.id} - ${user.credits} credits will expire in 30 days`);
          }
        } else {
          console.log(`ℹ️ Cancellation processed: ${user.id} - no credits to expire`);
        }

        break;
      }

      // ==========================================
      // OTHER EVENTS (Log but don't process)
      // ==========================================
      default:
        console.log(`ℹ️ Unhandled event type: ${event.type}`);
    }

    return new NextResponse(null, { status: 200 });

  } catch (error: any) {
    console.error('❌ Webhook processing error:', {
      eventType: event.type,
      eventId: event.id,
      error: error.message,
      stack: error.stack,
    });

    return new NextResponse(
      JSON.stringify({
        error: `Webhook handler failed: ${error.message}`,
        eventType: event.type,
        eventId: event.id,
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  }
}
