import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

export async function POST(request: NextRequest) {
  console.log('🚀 Process image API called');

  try {
    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      console.log('❌ Unauthorized request');
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log('✅ User authenticated:', userId);
    console.log('🔍 Processing image detection request for user:', userId);

    // Get the form data from the request
    const formData = await request.formData();
    const image = formData.get('image') as File;
    const textPrompt = formData.get('text_prompt') as string;
    const scoreThreshold = formData.get('score_threshold') as string;
    const visualize = formData.get('visualize') as string;
    const blurDetections = formData.get('blur_detections') as string;
    const blurStrength = formData.get('blur_strength') as string;
    const blurType = formData.get('blur_type') as string;
    const testMode = formData.get('test_mode') as string;

    if (!image) {
      console.log('❌ No image provided in request');
      return NextResponse.json(
        { error: 'No image provided' },
        { status: 400 }
      );
    }

    console.log('📁 Image received:', {
      name: image.name,
      type: image.type,
      size: image.size
    });

    // Test mode - return mock data without calling AI model
    if (testMode === 'true') {
      console.log('🧪 Test mode enabled - returning mock data');
      return NextResponse.json({
        success: true,
        test_mode: true,
        bboxes: [[100, 100, 200, 200]],
        labels: [textPrompt || 'person'],
        scores: [0.85],
        message: 'Test mode - mock detection data'
      });
    }

    console.log('📁 Image details:', {
      name: image.name,
      type: image.type,
      size: image.size
    });

    console.log('💬 Detection settings:', {
      textPrompt: textPrompt || 'person',
      scoreThreshold: scoreThreshold || '0.2',
      visualize: visualize || 'true',
      blurDetections: blurDetections || 'false',
      blurStrength: blurStrength || '25',
      blurType: blurType || 'blur'
    });

    // Create FormData for the Google Cloud AI model - match studio parameters
    const aiFormData = new FormData();
    aiFormData.append('image', image);
    aiFormData.append('text_prompt', textPrompt || 'person');
    aiFormData.append('score_threshold', scoreThreshold || '0.2');
    aiFormData.append('visualize', visualize || 'true');
    aiFormData.append('blur_detections', blurDetections || 'false');
    aiFormData.append('blur_strength', blurStrength || '25');
    aiFormData.append('blur_type', blurType || 'blur');

    // First, test connectivity to the AI model
    console.log('🔍 Testing connectivity to AI model...');
    try {
      const healthCheck = await fetch('https://guardiavision-prod-448517754159.europe-west1.run.app/', {
        method: 'GET',
        signal: AbortSignal.timeout(5000), // 5 second timeout for health check
      });
      console.log('✅ AI model is reachable, status:', healthCheck.status);
    } catch (healthError) {
      console.error('❌ AI model health check failed:', healthError.message);
      return NextResponse.json(
        {
          error: 'AI model is not reachable',
          details: `Health check failed: ${healthError.message}. Please check if the AI model is running.`
        },
        { status: 503 }
      );
    }

    console.log('🚀 Calling Google Cloud AI model...');
    console.log('🔗 AI Model URL: https://guardiavision-prod-448517754159.europe-west1.run.app/process_image');
    console.log('📋 FormData contents:', {
      hasImage: !!aiFormData.get('image'),
      textPrompt: aiFormData.get('text_prompt'),
      scoreThreshold: aiFormData.get('score_threshold'),
      visualize: aiFormData.get('visualize'),
      blurDetections: aiFormData.get('blur_detections')
    });

    // Create abort controller with shorter timeout for testing
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      console.log('⏰ Aborting request after 30 seconds...');
      controller.abort();
    }, 30000); // 30 seconds for faster debugging

    try {
      console.log('📡 Sending request to AI model...');
      const startTime = Date.now();

      // Call the Google Cloud AI model
      const aiResponse = await fetch('https://guardiavision-prod-448517754159.europe-west1.run.app/process_image', {
        method: 'POST',
        body: aiFormData,
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'GuardiaVision-Studio/1.0',
        },
        signal: controller.signal,
      });

      const responseTime = Date.now() - startTime;
      clearTimeout(timeoutId);

      console.log(`📥 AI model response received in ${responseTime}ms`);
      console.log('📊 Response details:', {
        status: aiResponse.status,
        statusText: aiResponse.statusText,
        headers: Object.fromEntries(aiResponse.headers.entries())
      });

      console.log('📥 AI model response status:', aiResponse.status);

      if (!aiResponse.ok) {
        const errorText = await aiResponse.text();
        console.error('❌ AI model error:', {
          status: aiResponse.status,
          statusText: aiResponse.statusText,
          error: errorText
        });

        return NextResponse.json(
          {
            error: `AI model error: ${aiResponse.status} ${aiResponse.statusText}`,
            details: errorText
          },
          { status: aiResponse.status }
        );
      }

      // Check content type
      const contentType = aiResponse.headers.get('content-type');
      console.log('📋 AI response content-type:', contentType);

      if (!contentType?.includes('application/json')) {
        console.error('❌ AI model returned non-JSON response:', contentType);
        return NextResponse.json(
          {
            error: 'AI model returned invalid response format',
            details: `Expected JSON but got ${contentType}`
          },
          { status: 500 }
        );
      }

      // Parse the AI response
      const aiResult = await aiResponse.json();
      console.log('✅ AI model response:', aiResult);

      // Validate the response format
      if (!aiResult.bboxes && !aiResult.detections) {
        console.log('⚠️ No detections found in AI response');
        return NextResponse.json({
          success: true,
          detections: [],
          bboxes: [],
          labels: [],
          scores: [],
          message: 'No objects detected'
        });
      }

      // Return the AI result
      return NextResponse.json({
        success: true,
        ...aiResult
      });

    } catch (fetchError: any) {
      clearTimeout(timeoutId);
      if (fetchError.name === 'AbortError') {
        console.error('❌ AI model request timeout');
        return NextResponse.json(
          {
            error: 'Request timeout',
            details: 'The AI model took too long to respond. Please try again.'
          },
          { status: 408 }
        );
      }
      throw fetchError;
    }

  } catch (error: any) {
    console.error('❌ Process image API error:', error);
    
    if (error.name === 'AbortError') {
      return NextResponse.json(
        { 
          error: 'Request timeout',
          details: 'The AI model took too long to respond. Please try again.'
        },
        { status: 408 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error.message
      },
      { status: 500 }
    );
  }
}

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';
export const maxDuration = 120; // 2 minutes max duration
