import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function POST(request: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    console.log(`🔒 Secure upload for user ${userId}: ${file.name} (${file.size} bytes)`);

    // Validate file size and type
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSize) {
      return NextResponse.json({ error: 'File too large' }, { status: 400 });
    }

    const allowedTypes = [
      'image/jpeg', 'image/png', 'image/webp', 'image/gif',
      'video/mp4', 'video/quicktime', 'video/webm'
    ];
    
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({ error: 'File type not allowed' }, { status: 400 });
    }

    const supabase = createServiceRoleSupabaseClient();

    // Step 1: Upload to storage (EXACTLY like test-upload)
    const timestamp = Date.now();
    const sanitizedFilename = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
    const filePath = `${userId}/uploads/${timestamp}_${sanitizedFilename}`;

    console.log('📁 Uploading to path:', filePath);

    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('user-uploads')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (uploadError) {
      console.error('❌ Upload error:', uploadError);
      return NextResponse.json({ error: `Upload failed: ${uploadError.message}` }, { status: 500 });
    }

    console.log('✅ File uploaded to storage:', uploadData);

    // Step 2: Get public URL (EXACTLY like test-upload)
    const { data: urlData } = supabase.storage
      .from('user-uploads')
      .getPublicUrl(filePath);

    console.log('🔗 Public URL:', urlData.publicUrl);

    // Step 3: Create database record (EXACTLY like test-upload)
    const fileType = file.type.startsWith('image/') ? 'image' : 'video';

    const { data: dbData, error: dbError } = await supabase
      .from('user_files')
      .insert({
        user_id: userId,
        original_filename: file.name,
        file_path: filePath,
        file_type: fileType,
        file_size: file.size,
        mime_type: file.type,
        processing_status: 'completed',
        processing_progress: 100,
        metadata: {
          originalUrl: urlData.publicUrl,
          uploadedAt: new Date().toISOString(),
          secureUpload: true
        }
      })
      .select('*')
      .single();

    if (dbError) {
      console.error('❌ Database error:', dbError);
      return NextResponse.json({ error: `Database error: ${dbError.message}` }, { status: 500 });
    }

    console.log('✅ Database record created:', dbData);

    // Step 4: Verify the record exists (EXACTLY like test-upload)
    const { data: verifyData, error: verifyError } = await supabase
      .from('user_files')
      .select('*')
      .eq('user_id', userId)
      .eq('id', dbData.id)
      .single();

    if (verifyError) {
      console.error('❌ Verification error:', verifyError);
    } else {
      console.log('✅ Record verified:', verifyData);
    }

    return NextResponse.json({
      success: true,
      fileId: dbData.id,
      path: filePath,
      url: urlData.publicUrl,
      message: 'File uploaded securely',
      steps: {
        upload: !!uploadData,
        url: !!urlData.publicUrl,
        database: !!dbData,
        verification: !!verifyData
      }
    });

  } catch (error: any) {
    console.error('❌ Secure upload error:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}