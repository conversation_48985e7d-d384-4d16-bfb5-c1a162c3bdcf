import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function POST(request: Request) {
  const startTime = Date.now();
  console.log('🔒 SECURE UPLOAD API START:', new Date().toISOString());
  
  try {
    console.log('🔐 Checking authentication...');
    const { userId } = await auth();
    if (!userId) {
      console.log('❌ Authentication failed - no userId');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    console.log('✅ User authenticated:', userId);

    console.log('📦 Parsing form data...');
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      console.log('❌ No file in form data');
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    console.log('📁 FILE RECEIVED:', {
      name: file.name,
      type: file.type,
      size: file.size,
      lastModified: file.lastModified
    });

    // SERVER-SIDE VALIDATION - STRICT
    console.log('🔍 SERVER-SIDE VALIDATION START...');
    const allowedImageTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
    const allowedVideoTypes = ['video/mp4', 'video/quicktime', 'video/webm', 'video/avi', 'video/x-msvideo', 'video/3gpp', 'video/x-ms-wmv'];
    const allAllowedTypes = [...allowedImageTypes, ...allowedVideoTypes];
    
    const fileType = file.type.toLowerCase();
    console.log('🔍 File type validation:', fileType, 'in', allAllowedTypes);
    
    if (!allAllowedTypes.includes(fileType)) {
      console.log('❌ SERVER VALIDATION FAILED - File type not allowed:', fileType);
      return NextResponse.json({ error: `File type not allowed: ${file.type}` }, { status: 400 });
    }

    // Size validation with different limits
    const isVideo = allowedVideoTypes.includes(fileType);
    const maxSize = isVideo ? 50 * 1024 * 1024 : 10 * 1024 * 1024; // 50MB for videos, 10MB for images
    
    console.log('🔍 Size validation - File:', file.size, 'Max:', maxSize, 'IsVideo:', isVideo);
    
    if (file.size > maxSize) {
      const fileSizeMB = (file.size / (1024 * 1024)).toFixed(1);
      const maxSizeMB = (maxSize / (1024 * 1024)).toFixed(0);
      console.log('❌ SERVER VALIDATION FAILED - File too large:', fileSizeMB, 'MB, max:', maxSizeMB, 'MB');
      return NextResponse.json({ 
        error: `File too large: ${fileSizeMB}MB. Maximum size is ${maxSizeMB}MB for ${isVideo ? 'videos' : 'images'}.` 
      }, { status: 400 });
    }

    console.log('✅ SERVER VALIDATION PASSED');

    console.log('🗄️ Creating Supabase client...');
    const supabase = createServiceRoleSupabaseClient();

    // Step 1: Upload to storage
    const timestamp = Date.now();
    const sanitizedFilename = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
    const filePath = `${userId}/uploads/${timestamp}_${sanitizedFilename}`;

    console.log('📁 STORAGE UPLOAD START - Path:', filePath);

    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('user-uploads')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (uploadError) {
      console.log('❌ STORAGE UPLOAD FAILED:', uploadError);
      return NextResponse.json({ error: `Upload failed: ${uploadError.message}` }, { status: 500 });
    }

    console.log('✅ STORAGE UPLOAD SUCCESS:', uploadData);

    // Step 2: Generate URLs
    console.log('🔗 URL GENERATION START...');
    const { data: urlData } = supabase.storage
      .from('user-uploads')
      .getPublicUrl(filePath);

    console.log('🔗 Public URL generated:', urlData.publicUrl);

    // Test if public URL works, fallback to signed URL if bucket is not public
    let finalUrl = urlData.publicUrl;
    try {
      console.log('🔗 Testing public URL accessibility...');
      const testResponse = await fetch(urlData.publicUrl, { method: 'HEAD' });
      if (!testResponse.ok) {
        console.log('🔗 Public URL failed, creating signed URL...');
        const { data: signedUrlData, error: signedUrlError } = await supabase.storage
          .from('user-uploads')
          .createSignedUrl(filePath, 3600 * 24 * 7); // 7 days expiry

        if (signedUrlError) {
          console.log('❌ Signed URL creation failed:', signedUrlError);
        } else {
          finalUrl = signedUrlData.signedUrl;
          console.log('✅ Using signed URL:', finalUrl);
        }
      } else {
        console.log('✅ Public URL is accessible');
      }
    } catch (error) {
      console.log('🔗 Public URL test failed, creating signed URL...', error);
      const { data: signedUrlData, error: signedUrlError } = await supabase.storage
        .from('user-uploads')
        .createSignedUrl(filePath, 3600 * 24 * 7); // 7 days expiry

      if (!signedUrlError && signedUrlData) {
        finalUrl = signedUrlData.signedUrl;
        console.log('✅ Using signed URL:', finalUrl);
      }
    }

    // Step 3: Create database record
    console.log('🗄️ DATABASE INSERT START...');
    const detectedFileType = file.type.startsWith('image/') ? 'image' : 'video';
    console.log('🗄️ Detected file type:', detectedFileType);

    const dbRecord = {
      user_id: userId,
      original_filename: file.name,
      file_path: filePath,
      file_type: detectedFileType,
      file_size: file.size,
      mime_type: file.type,
      processing_status: 'completed',
      processing_progress: 100,
      metadata: {
        originalUrl: finalUrl,
        publicUrl: urlData.publicUrl,
        uploadedAt: new Date().toISOString(),
        secureUpload: true,
        urlType: finalUrl === urlData.publicUrl ? 'public' : 'signed'
      }
    };

    console.log('🗄️ Inserting record:', dbRecord);

    const { data: dbData, error: dbError } = await supabase
      .from('user_files')
      .insert(dbRecord)
      .select('*')
      .single();

    if (dbError) {
      console.log('❌ DATABASE INSERT FAILED:', dbError);
      return NextResponse.json({ error: `Database error: ${dbError.message}` }, { status: 500 });
    }

    console.log('✅ DATABASE INSERT SUCCESS:', dbData);

    // Step 4: Verify the record exists
    console.log('🔍 VERIFICATION START...');
    const { data: verifyData, error: verifyError } = await supabase
      .from('user_files')
      .select('*')
      .eq('user_id', userId)
      .eq('id', dbData.id)
      .single();

    if (verifyError) {
      console.log('❌ VERIFICATION FAILED:', verifyError);
    } else {
      console.log('✅ VERIFICATION SUCCESS:', verifyData);
    }

    const endTime = Date.now();
    const duration = endTime - startTime;
    console.log('🎉 SECURE UPLOAD API SUCCESS - Duration:', duration, 'ms');

    const response = {
      success: true,
      fileId: dbData.id,
      path: filePath,
      url: finalUrl,
      publicUrl: urlData.publicUrl,
      urlType: finalUrl === urlData.publicUrl ? 'public' : 'signed',
      message: 'File uploaded successfully',
      duration: duration,
      steps: {
        upload: !!uploadData,
        url: !!finalUrl,
        database: !!dbData,
        verification: !!verifyData
      }
    };

    console.log('📤 SENDING RESPONSE:', response);
    return NextResponse.json(response);

  } catch (error: any) {
    const endTime = Date.now();
    const duration = endTime - startTime;
    console.log('❌ SECURE UPLOAD API ERROR - Duration:', duration, 'ms');
    console.log('❌ Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    
    return NextResponse.json({
      success: false,
      error: error.message,
      duration: duration
    }, { status: 500 });
  }
}