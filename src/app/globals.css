/* Enhanced smooth scroll animations */
.scroll-animate {
  opacity: 0;
  transform: translateY(30px) scale(0.95);
  transition: opacity 1.2s cubic-bezier(0.16, 1, 0.3, 1),
              transform 1.2s cubic-bezier(0.16, 1, 0.3, 1);
  will-change: opacity, transform;
}

.animate-in {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* Delay classes for staggered animations */
.delay-100 {
  transition-delay: 0.1s;
}

.delay-200 {
  transition-delay: 0.2s;
}

.delay-300 {
  transition-delay: 0.3s;
}

.delay-400 {
  transition-delay: 0.4s;
}

.delay-500 {
  transition-delay: 0.5s;
}

/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

/* Enhanced hover effects for interactive elements */
.scroll-animate:hover {
  transform: translateY(-2px) scale(1.02);
  transition: transform 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

/* Laser-like border animation for prompt box - continuous traveling light with uniform thickness */
@keyframes laser-travel {
  0% {
    top: -3px;
    left: -25px;
    width: 25px;
    height: 3px;
  }
  24.99% {
    top: -3px;
    left: calc(100% - 25px);
    width: 25px;
    height: 3px;
  }
  25% {
    top: -25px;
    left: calc(100% - 3px);
    width: 3px;
    height: 25px;
  }
  49.99% {
    top: calc(100% - 25px);
    left: calc(100% - 3px);
    width: 3px;
    height: 25px;
  }
  50% {
    top: calc(100% - 3px);
    left: calc(100% + 25px);
    width: 25px;
    height: 3px;
  }
  74.99% {
    top: calc(100% - 3px);
    left: 0px;
    width: 25px;
    height: 3px;
  }
  75% {
    top: calc(100% + 25px);
    left: -3px;
    width: 3px;
    height: 25px;
  }
  99.99% {
    top: 0px;
    left: -3px;
    width: 3px;
    height: 25px;
  }
  100% {
    top: -3px;
    left: -25px;
    width: 25px;
    height: 3px;
  }
}

.prompt-laser-border {
  position: absolute;
  inset: -1px;
  border-radius: 0.5rem;
  overflow: hidden;
  z-index: 0;
  pointer-events: none;
}

.prompt-laser-border::before {
  content: "";
  position: absolute;
  background: linear-gradient(90deg, transparent, #8b5cf6, #a855f7, #8b5cf6, transparent);
  border-radius: 1.5px;
  box-shadow: 0 0 8px #8b5cf6, 0 0 12px #a855f7;
  animation: laser-travel 3.5s linear infinite;
}

.prompt-laser-border::after {
  content: "";
  position: absolute;
  inset: 1px;
  background: rgba(30, 58, 111, 0.3);
  border-radius: calc(0.5rem - 1px);
  z-index: 1;
}



.delay-100 { transition-delay: 0.2s; }
.delay-200 { transition-delay: 0.4s; }
.delay-300 { transition-delay: 0.6s; }
.delay-400 { transition-delay: 0.8s; }
.delay-500 { transition-delay: 1s; }
.delay-600 { transition-delay: 1.2s; }
.delay-700 { transition-delay: 1.4s; }
.delay-800 { transition-delay: 1.6s; }

.vision-text {
  background: linear-gradient(90deg, #10b981, #3b82f6, #10b981);
  background-size: 200% auto;
  color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  animation: gradient 3s linear infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes shimmer {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 100% 50%;
  }
}

@keyframes shine {
  0% {
    transform: translateX(-100%);
  }
  60%, 100% {
    transform: translateX(100%);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes float-button {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

.upgrade-button {
  transform-style: preserve-3d;
  perspective: 1000px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.upgrade-button:hover {
  transform: scale(1.05) translateY(-4px) rotateX(5deg);
  box-shadow: 0 15px 25px -5px rgba(124, 58, 237, 0.5);
}

/* Professional scrollable image container styling */
.studio-image-container {
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 246, 0.5) rgba(15, 22, 41, 0.3);
}

.studio-image-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.studio-image-container::-webkit-scrollbar-track {
  background: rgba(15, 22, 41, 0.3);
  border-radius: 4px;
}

.studio-image-container::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.5);
  border-radius: 4px;
  transition: background 0.2s ease;
}

.studio-image-container::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 92, 246, 0.7);
}

.studio-image-container::-webkit-scrollbar-corner {
  background: rgba(15, 22, 41, 0.3);
}
