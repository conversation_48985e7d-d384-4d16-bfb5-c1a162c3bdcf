import { Code, Layers, Lock, Settings, Zap } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { unstable_setRequestLocale } from 'next-intl/server';

// Import components
import { ShieldCheck } from '@/components/icons/ShieldCheck';
import { UploadRedirect } from '@/components/landing/UploadRedirect';
import { InteractiveNavbar } from '@/components/landing/InteractiveNavbar';
import { ScrollAnimations } from '@/components/landing/ScrollAnimations';
import { BeforeAfterShowcase } from '@/components/landing/BeforeAfterShowcase';
import { FeedbackWidget } from '@/components/landing/FeedbackWidget';
import { SecurityBadges } from '@/components/landing/SecurityBadges';
import { PricingSection } from '@/components/pricing/PricingSection';
import { EnglishCTA } from '@/features/landing/EnglishCTA';
import { Footer } from '@/templates/Footer';

interface IndexPageProps {
  params: {
    locale: string;
  };
}



export default function IndexPage({ params }: IndexPageProps) {
  const locale = params.locale || 'en';

  // Enable static rendering
  unstable_setRequestLocale(locale);

  return (
    <div className="min-h-screen bg-navy text-white">
      {/* Skip to content link for accessibility */}
      <a href="#main-content" className="skip-to-content">
        Skip to main content
      </a>

      {/* Add scroll animations */}
      <ScrollAnimations />

      {/* Navbar */}
      <InteractiveNavbar locale={locale} />

      {/* Main content wrapper */}
      <main id="main-content">

      {/* Hero Section */}
      <section className="relative overflow-hidden bg-navy" aria-labelledby="hero-heading">
        <div className="container relative mx-auto container-padding py-8 sm:py-12 lg:py-16">
          <div className="grid grid-cols-1 gap-12 lg:grid-cols-2 lg:gap-16">
            <div className="flex flex-col justify-center scroll-animate-group">
              <div className="mb-6 inline-flex items-center gap-2 rounded-full border border-green-400/30 bg-green-400/10 px-4 py-2 text-sm font-semibold text-green-400 scroll-animate">
                <ShieldCheck className="size-4" />
                <span>Privacy-first content protection</span>
              </div>

              <h1 id="hero-heading" className="text-display-lg mb-6 text-white scroll-animate">
                <span className="block">Blur ANYTHING</span>
                <span className="gradient-text block">
                  in photos and videos with AI in 3 seconds with a simple description!
                </span>
              </h1>

              <p className="text-body-xl mb-10 max-w-2xl text-secondary scroll-animate">
                No software installation needed, use it right on the web. Protect sensitive information instantly with AI-powered precision.
              </p>

              <div className="flex flex-col gap-4 sm:flex-row sm:gap-4 scroll-animate">
                <a
                  href="https://youtube.com/shorts/mvF6Vd01yks"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="blue-button-glow inline-flex w-full items-center justify-center rounded-lg border-2 border-blue-400 px-8 py-4 text-lg font-bold text-blue-400 transition-all duration-300 hover:border-transparent hover:bg-blue-500 hover:text-white sm:w-auto"
                  aria-label="Watch product demo video"
                >
                  <svg className="mr-2 size-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                  </svg>
                  Watch Demo
                </a>

                <div className="group relative sm:w-auto">
                  <div className="rainbow-border rounded-lg"></div>
                  <Link
                    href={`/${locale}/sign-up`}
                    className="relative inline-flex w-full items-center justify-center rounded-lg bg-green-400 px-8 py-4 text-lg font-bold text-navy transition-all duration-300 hover:bg-green-500"
                  >
                    <ShieldCheck className="mr-2 size-5" />
                    Start For Free Now
                  </Link>
                </div>
              </div>

              <div className="mt-8 flex items-center gap-2 text-sm text-muted scroll-animate">
                <Lock className="size-4 text-green-400" aria-hidden="true" />
                <span>No credit card required • Cancel anytime</span>
              </div>
            </div>

            <div className="flex items-center justify-center scroll-animate">
              <div className="demo-box relative">
                <div className="absolute -inset-1 rounded-xl bg-gradient-to-r from-blue-400 via-purple-400 to-green-400 opacity-80 blur-lg"></div>
                <div className="relative w-[300px] overflow-hidden rounded-lg border border-navy-light bg-navy-dark shadow-2xl">
                  <div className="flex items-center justify-between border-b border-navy-light bg-navy p-2">
                    <div className="flex space-x-1.5">
                      <div className="size-3 rounded-full bg-red-500"></div>
                      <div className="size-3 rounded-full bg-yellow-500"></div>
                      <div className="size-3 rounded-full bg-green-500"></div>
                    </div>
                    <div className="text-sm font-medium text-blue-400">Guardiavision Demo</div>
                    <div className="w-16"></div>
                  </div>
                  <div className="p-0">
                    <div className="flex justify-center">
                      <div className="h-[500px] w-full overflow-hidden rounded-none bg-gray-800">
                        <a
                          href="https://youtube.com/shorts/mvF6Vd01yks"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="group relative flex size-full cursor-pointer items-center justify-center"
                        >
                          <div className="absolute inset-0 bg-black bg-opacity-50"></div>
                          <div className="relative z-10 rounded-full bg-blue-500 bg-opacity-80 p-6 shadow-lg transition-transform group-hover:scale-110">
                            <svg className="size-12 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                            </svg>
                          </div>
                          <img
                            src="https://img.youtube.com/vi/mvF6Vd01yks/maxresdefault.jpg"
                            alt="Video Thumbnail"
                            className="absolute inset-0 size-full object-cover"
                          />
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Demo Section */}
      <section id="demo" className="section-padding bg-navy-dark" aria-labelledby="demo-heading">
        <div className="container mx-auto container-padding">
          <div className="mx-auto max-w-3xl text-center scroll-animate-group">
            <h2 id="demo-heading" className="text-heading-xl text-white">
              See Guardia<span className="vision-text">Vision</span> in Action
            </h2>
            <p className="text-body-lg mt-6 text-secondary">
              Drag the slider to reveal how our AI precisely blurs sensitive content while preserving the integrity of your media.
            </p>
          </div>

          <div className="mt-16">
            <BeforeAfterShowcase />
          </div>

          {/* Upload Your Own Image Section */}
          <div className="mt-20">
            <div className="mx-auto max-w-3xl text-center scroll-animate-group">
              <h3 className="text-heading-lg text-white">
                Try It With Your Own Media
              </h3>
              <p className="text-body-lg mt-6 text-secondary">
                Tell us what to blur, then upload your photos or videos to see how our AI can protect your sensitive content.
              </p>
            </div>

            <div className="mt-8 scroll-animate">
              <UploadRedirect signUpUrl={`/${locale}/sign-up`} />
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="section-padding" aria-labelledby="features-heading">
        <div className="container mx-auto container-padding">
          <div className="mx-auto max-w-3xl text-center scroll-animate-group">
            <h2 id="features-heading" className="text-heading-xl text-white">
              Advanced Privacy Protection Features
            </h2>
            <p className="text-body-lg mt-6 text-secondary">
              Our AI-powered platform offers comprehensive tools to protect sensitive information in your visual content.
            </p>
          </div>

          <div className="mt-16 grid grid-gap-lg md:grid-cols-2 lg:grid-cols-3 scroll-animate-group">
            {[
              {
                icon: <Settings className="size-6 text-green-400" />,
                title: 'Customizable Blur',
                description: 'Specify exactly what you want to blur or remove: faces, license plates, text, or entire people.',
                tooltip: 'Use simple text descriptions to define what content should be blurred',
              },
              {
                icon: <Zap className="size-6 text-green-400" />,
                title: 'Real-Time Processing',
                description: 'Process videos and images in real-time with minimal latency, perfect for live streaming.',
                tooltip: 'Average processing time: 2-3 seconds per image',
              },
              {
                icon: <ShieldCheck className="size-6 text-green-400" />,
                title: 'AI Precision',
                description: 'Our advanced AI ensures accurate detection and blurring of sensitive content with 99.8% accuracy.',
                tooltip: 'Powered by state-of-the-art computer vision models',
              },
              {
                icon: <Code className="size-6 text-green-400" />,
                title: 'Developer API',
                description: 'Integrate our privacy protection directly into your applications with our simple REST API.',
                tooltip: 'RESTful API with comprehensive documentation and SDKs',
              },
              {
                icon: <Layers className="size-6 text-green-400" />,
                title: 'Batch Processing',
                description: 'Upload and process thousands of files at once with our efficient batch processing system.',
                tooltip: 'Process up to 1000 files simultaneously on premium plans',
              },
              {
                icon: <Lock className="size-6 text-green-400" />,
                title: 'Privacy Guaranteed',
                description: 'Your content is processed securely and never stored or used for training our AI models.',
                tooltip: 'Files are automatically deleted within 24 hours',
              },
            ].map((feature, index) => (
              <article
                key={index}
                className="card-interactive card-padding group"
                role="article"
                aria-labelledby={`feature-${index}-title`}
              >
                <div className="mb-4 inline-flex size-12 items-center justify-center rounded-lg bg-navy transition-colors group-hover:bg-navy-light">
                  {feature.icon}
                </div>
                <h3 id={`feature-${index}-title`} className="text-heading-sm mb-3 text-white">{feature.title}</h3>
                <p className="text-body-md text-secondary">{feature.description}</p>
              </article>
            ))}
          </div>
        </div>
      </section>

      {/* Security Badges Section */}
      <SecurityBadges />

      {/* Pricing Section */}
      <PricingSection />

      {/* Feedback Widget (bottom right) */}
      <FeedbackWidget />

      {/* Use existing components for the rest */}
      <div className="bg-navy">
        <EnglishCTA />
        <Footer />
      </div>

      </main>
    </div>
  );
}
