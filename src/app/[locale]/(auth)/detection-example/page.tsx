'use client';

import { useState } from 'react';
import { LiveDetectionViewer } from '@/components/detection/LiveDetectionViewer';
import { useImageDetection } from '@/hooks/useImageDetection';
import { Upload, Target } from 'lucide-react';

export default function DetectionExamplePage() {
  const { detect, isDetecting, result, error } = useImageDetection();

  // Example values - replace with your actual file path and image URL
  const [filePath] = useState('user_320WoCx7oaj7coakpK117Liq8Rk/uploads/1761857581122_pp.png');
  const [imageUrl] = useState('https://your-supabase-url.supabase.co/storage/v1/object/public/user-uploads/user_320WoCx7oaj7coakpK117Liq8Rk/uploads/1761857581122_pp.png');
  const [textPrompt, setTextPrompt] = useState('person');
  const [scoreThreshold, setScoreThreshold] = useState(0.1);

  const handleDetect = async () => {
    try {
      await detect(filePath, textPrompt, scoreThreshold);
    } catch (err) {
      console.error('Detection failed:', err);
    }
  };

  return (
    <div className="min-h-screen bg-navy p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="bg-navy-dark rounded-lg border border-navy-light p-6">
          <h1 className="text-3xl font-bold text-white mb-2">
            Real-Time Detection Rendering
          </h1>
          <p className="text-gray-400">
            Client-side rendering of AI detection results with interactive controls
          </p>
        </div>

        {/* Instructions */}
        <div className="bg-blue-400/10 border border-blue-400/30 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-blue-400 mb-3">
            New Client-Side Rendering Flow
          </h2>
          <ol className="list-decimal list-inside space-y-2 text-gray-300 text-sm">
            <li>Upload an image to Supabase storage (user-uploads bucket)</li>
            <li>Click "Detect Objects" - calls backend <code className="text-green-400 bg-navy-dark px-2 py-1 rounded">/process-image-detection</code></li>
            <li>Backend returns JSON metadata instantly (no processed image)</li>
            <li>Frontend renders bounding boxes/blur effects using Canvas API</li>
            <li>Adjust threshold and blur strength in real-time!</li>
          </ol>
        </div>

        {/* Detection Controls */}
        <div className="bg-navy-dark rounded-lg border border-navy-light p-6">
          <h2 className="text-lg font-semibold text-white mb-4">Detection Settings</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="text-sm text-gray-400 mb-2 block">Text Prompt</label>
              <input
                type="text"
                value={textPrompt}
                onChange={(e) => setTextPrompt(e.target.value)}
                className="w-full px-4 py-2 bg-navy-light border border-navy-light text-white rounded-lg focus:outline-none focus:border-green-400"
                placeholder="person, car, etc."
              />
            </div>
            <div>
              <label className="text-sm text-gray-400 mb-2 block">Score Threshold (Backend)</label>
              <input
                type="number"
                min="0"
                max="1"
                step="0.1"
                value={scoreThreshold}
                onChange={(e) => setScoreThreshold(parseFloat(e.target.value))}
                className="w-full px-4 py-2 bg-navy-light border border-navy-light text-white rounded-lg focus:outline-none focus:border-green-400"
              />
            </div>
          </div>

          <button
            onClick={handleDetect}
            disabled={isDetecting}
            className="w-full bg-green-400 hover:bg-green-500 text-navy font-semibold py-3 px-6 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            {isDetecting ? (
              <>
                <div className="animate-spin h-5 w-5 border-2 border-navy border-t-transparent rounded-full" />
                Detecting...
              </>
            ) : (
              <>
                <Target className="h-5 w-5" />
                Detect Objects
              </>
            )}
          </button>

          {error && (
            <div className="mt-4 p-4 bg-red-400/10 border border-red-400 rounded-lg">
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          )}
        </div>

        {/* Detection Results */}
        {result && (
          <>
            <div className="bg-green-400/10 border border-green-400/30 rounded-lg p-6">
              <h2 className="text-lg font-semibold text-green-400 mb-3">
                ✅ Detection Complete!
              </h2>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">Job ID:</span>
                  <p className="text-white font-mono text-xs mt-1">{result.job_id}</p>
                </div>
                <div>
                  <span className="text-gray-400">Processing Time:</span>
                  <p className="text-white font-mono">{result.processing_time.toFixed(2)}s</p>
                </div>
                <div>
                  <span className="text-gray-400">Detections Found:</span>
                  <p className="text-white font-mono">{result.detection_count}</p>
                </div>
                <div>
                  <span className="text-gray-400">Filename:</span>
                  <p className="text-white font-mono text-xs">{result.metadata.filename}</p>
                </div>
              </div>
            </div>

            <LiveDetectionViewer
              imageUrl={imageUrl}
              detectionMetadata={result.metadata}
              initialThreshold={0.3}
              initialBlurStrength={35}
            />
          </>
        )}

        {/* Code Example */}
        <div className="bg-navy-dark rounded-lg border border-navy-light p-6">
          <h2 className="text-lg font-semibold text-white mb-4">Code Example</h2>
          <pre className="bg-navy-light rounded-lg p-4 overflow-x-auto text-sm">
            <code className="text-green-400">
              {`import { useImageDetection } from '@/hooks/useImageDetection';
import { LiveDetectionViewer } from '@/components/detection/LiveDetectionViewer';

function MyStudio() {
  const { detect, isDetecting, result } = useImageDetection();

  const handleDetect = async () => {
    // Call backend detection endpoint
    await detect(
      'user_123/uploads/image.jpg',  // file_path in Supabase
      'person',                       // text_prompt
      0.1                            // score_threshold
    );
  };

  return (
    <>
      <button onClick={handleDetect} disabled={isDetecting}>
        Detect Objects
      </button>

      {result && (
        <LiveDetectionViewer
          imageUrl={originalImageUrl}
          detectionMetadata={result.metadata}
          initialThreshold={0.3}
          initialBlurStrength={35}
        />
      )}
    </>
  );
}`}
            </code>
          </pre>
        </div>
      </div>
    </div>
  );
}
