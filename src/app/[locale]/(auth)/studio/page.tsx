'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams, useParams } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import GuardiaVisionStudio from '@/components/studio/GuardiaVisionStudio';
import Link from 'next/link';

interface MediaFile {
  id: string;
  original_filename: string;
  file_path: string;
  file_type: 'image' | 'video';
  file_size: number;
  mime_type: string;
  metadata: any;
  created_at: string;
}

function StudioContent() {
  const { user } = useUser();
  const searchParams = useSearchParams();
  const fileId = searchParams.get('file');
  const testMode = searchParams.get('test') === 'true';
  const fileType = searchParams.get('type') || 'image'; // Default to image if not specified

  const [mediaFile, setMediaFile] = useState<MediaFile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  

  useEffect(() => {
    // Handle test mode
    if (testMode) {
      console.log('🧪 Test mode enabled - creating mock file');
      const mockFile: MediaFile = {
        id: 'test-file-id',
        original_filename: fileType === 'video' ? 'test-video.mp4' : 'test-image.jpg',
        file_path: 'test/path',
        file_type: fileType as 'image' | 'video',
        file_size: 1024,
        mime_type: fileType === 'video' ? 'video/mp4' : 'image/jpeg',
        metadata: {},
        created_at: new Date().toISOString()
      };
      setMediaFile(mockFile);
      setLoading(false);
      return;
    }
  
    if (!fileId) {
      console.log('⏳ No file ID provided');
      setLoading(false);
      return;
    }
  
    // CRITICAL FIX: Wait for Clerk to fully initialize
    if (user === undefined) {
      console.log('⏳ Clerk still loading, waiting...');
      return; // Keep loading=true
    }
  
    if (user === null) {
      console.log('❌ User not authenticated');
      setLoading(false);
      return; // This will show "Access Denied"
    }
  
    if (!user.id) {
      console.log('⏳ User object exists but ID not ready, waiting...');
      return; // Keep loading=true
    }
  
    console.log('✅ User fully authenticated, fetching file:', { userId: user.id, fileId });
    
    // Reset error state before fetching
    setError(null);
    
    // Add small delay to ensure Clerk session is fully established
    const timer = setTimeout(() => {
      fetchMediaFile();
    }, 200);
  
    return () => clearTimeout(timer);
  }, [user, fileId, testMode, fileType]); // Watch 'user' directly, not user?.id

  const fetchMediaFile = async () => {
    try {
      console.log('🔍 Fetching file with ID:', fileId);
      console.log('🔍 Current user:', user?.id);
  
      // Get auth token for requests
      const authHeaders: Record<string, string> = {
        'Cache-Control': 'no-cache',
      };
  
      // Try to get Clerk session token if available
      try {
        const token = await user?.getToken?.();
        if (token) {
          authHeaders['Authorization'] = `Bearer ${token}`;
        }
      } catch (tokenError) {
        console.log('⚠️ Could not get auth token, proceeding without it');
      }
  
      // Try multiple endpoints to find the file
      const endpoints = [
        `/api/user/files-fixed`,
        `/api/user/files`,
        `/api/files/${fileId}`
      ];
  
      let targetFile = null;
      
      for (const endpoint of endpoints) {
        try {
          console.log(`📁 Trying endpoint: ${endpoint}`);
          const response = await fetch(endpoint, {
            cache: 'no-store',
            headers: authHeaders,
          });
  
          if (response.ok) {
            const result = await response.json();
            
            // Handle different response formats
            if (endpoint.includes('files-fixed') || endpoint.includes('/files')) {
              targetFile = result.files?.find((f: any) => f.id === fileId);
            } else {
              targetFile = result; // Direct file response
            }
            
            if (targetFile) {
              console.log('✅ Found file:', targetFile.original_filename);
              break;
            }
          } else if (response.status === 401) {
            console.log(`🔐 Auth required for ${endpoint}, user may not be fully authenticated yet`);
            continue;
          }
        } catch (endpointError) {
          console.log(`❌ Endpoint ${endpoint} failed:`, endpointError);
          continue;
        }
      }
  
      if (!targetFile) {
        throw new Error('File not found in any endpoint - user may not have access');
      }
  
      // Rest of your existing fetchMediaFile code for building imageUrl...
      // [Keep the existing image URL building logic from my previous response]
      
      // Validate file data
      if (!targetFile.id || !targetFile.original_filename) {
        throw new Error('Invalid file data received');
      }

      // Build proper image URL with fallbacks
      let imageUrl = '';

      // Priority 1: Use the 'url' field from API (this is the signed URL from Supabase)
      if (targetFile.url) {
        imageUrl = targetFile.url;
        console.log('✅ Using signed URL from API:', imageUrl);
      }
      // Priority 2: Use metadata.originalUrl if available
      else if (targetFile.metadata?.originalUrl) {
        imageUrl = targetFile.metadata.originalUrl;
        console.log('✅ Using metadata originalUrl:', imageUrl);
      }
      // Priority 3: Fetch from API and extract secureUrl
      else {
        try {
          console.log('🔍 Fetching secure URL from API for file:', targetFile.id);
          const fileResponse = await fetch(`/api/files/${targetFile.id}`, {
            cache: 'no-store',
            headers: authHeaders,
          });

          if (fileResponse.ok) {
            const fileData = await fileResponse.json();
            if (fileData.success && fileData.file?.secureUrl) {
              imageUrl = fileData.file.secureUrl;
              console.log('✅ Got secure URL from API:', imageUrl);
            } else {
              throw new Error('No secure URL in API response');
            }
          } else {
            throw new Error(`API returned ${fileResponse.status}`);
          }
        } catch (apiError) {
          console.error('❌ Failed to get secure URL from API:', apiError);
          throw new Error('Unable to load image - no valid URL available');
        }
      }

      // Update metadata with working URL
      const finalFile = {
        ...targetFile,
        metadata: {
          ...targetFile.metadata,
          originalUrl: imageUrl
        }
      };

      setMediaFile(finalFile);
      setLoading(false);
      console.log('✅ File loaded successfully with working image URL');
  
    } catch (error: any) {
      console.error('❌ Error fetching media file:', error);
      setError(`Failed to load file: ${error.message}`);
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="flex h-screen items-center justify-center bg-gray-900">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">Access Denied</h1>
          <p className="text-gray-400">Please sign in to access the studio.</p>
        </div>
      </div>
    );
  }

  if (!fileId) {
    return (
      <div className="flex h-screen items-center justify-center bg-gray-900">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">No File Selected</h1>
          <p className="text-gray-400">Please select a file from your dashboard to edit.</p>
          <a 
            href="/dashboard" 
            className="mt-4 inline-block px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Go to Dashboard
          </a>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-white text-lg font-medium">Loading GuardiaVision Studio...</p>
          <p className="text-gray-400 text-sm mt-2">Preparing your media for editing</p>
        </div>
      </div>
    );
  }

  if (error || !mediaFile) {
    return (
      <div className="flex h-screen items-center justify-center bg-gray-900">
        <div className="text-center max-w-2xl mx-auto p-6">
          <h1 className="text-2xl font-bold text-red-400 mb-4">Error</h1>
          <p className="text-gray-400 mb-4">{error || 'File not found'}</p>
          

          
          <div className="space-x-4">
            <a 
              href="/dashboard" 
              className="inline-block px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Back to Dashboard
            </a>
            <button
              onClick={() => window.location.reload()}
              className="inline-block px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-gray-900">
      <GuardiaVisionStudio
        mediaFile={mediaFile}
        originalImageUrl={mediaFile.metadata?.originalUrl || ''}
        onBack={() => window.location.href = '/dashboard'}
      />
    </div>
  );
}

export default function StudioPage() {
  return (
    <Suspense fallback={
      <div className="flex h-screen items-center justify-center bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-gray-400">Loading studio...</p>
        </div>
      </div>
    }>
      <StudioContent />
    </Suspense>
  );
}