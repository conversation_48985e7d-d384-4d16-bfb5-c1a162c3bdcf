'use client';

import { UserProfile } from '@clerk/nextjs';
import { useTranslations } from 'next-intl';
import { useState, useEffect } from 'react';
import { CreditCard, Loader2, LogOut, Repeat } from 'lucide-react';

import { getI18nPath } from '@/utils/Helpers';
import { clerkAppearance } from '@/utils/clerk-appearance';

// --- Type Definitions ---
interface SubscriptionData {
  planName: string | null;
  status: string | null;
  credits: number;
  renewsOn: string | null;
}

// --- Custom Billing Component ---
const CustomBillingPage = () => {
  const [subscription, setSubscription] = useState<SubscriptionData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isCancelling, setIsCancelling] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchSubscriptionStatus = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/billing/subscription-status');
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch subscription status.');
      }
      const data = await response.json();
      setSubscription(data);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchSubscriptionStatus();
  }, []);

  const handleCancelSubscription = async () => {
    if (!window.confirm('Are you sure you want to cancel your subscription? This will happen immediately, and your credits will be reset to zero.')) {
        return;
    }
    setIsCancelling(true);
    try {
        const response = await fetch('/api/billing/cancel', { method: 'POST' });
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to cancel subscription.');
        }
        // Refresh the subscription status to show the "Free Plan" state
        await fetchSubscriptionStatus();
    } catch (err: any) {
        alert(`Error: ${err.message}`);
    } finally {
        setIsCancelling(false);
    }
  };

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex justify-center items-center h-40">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        </div>
      );
    }

    if (error) {
      return <p className="text-red-500">Error: {error}</p>;
    }

    if (!subscription || !subscription.planName || subscription.planName === 'Free') {
      return (
        <div>
          <h4 className="text-lg font-semibold text-gray-800">You are on the Free Plan</h4>
          <p className="mt-1 text-gray-500">Upgrade to a paid plan to unlock more features and increase your credit limit.</p>
          <a href="/dashboard/billing" className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700">
            View Plans
          </a>
        </div>
      );
    }

    return (
      <div>
        <div className="flex justify-between items-start">
          <div>
            <h4 className="text-lg font-semibold text-gray-800">Current Plan</h4>
            <p className="mt-1 text-2xl font-bold text-indigo-600">{subscription.planName}</p>
            <p className={`mt-1 text-sm font-medium capitalize ${subscription.status === 'cancelling' ? 'text-yellow-600' : 'text-green-600'}`}>
              Status: {subscription.status}
            </p>
          </div>
          <div className="text-right">
            <h4 className="text-lg font-semibold text-gray-800">Credits</h4>
            <p className="mt-1 text-2xl font-bold text-indigo-600">{subscription.credits}</p>
            {subscription.renewsOn && (
              <p className="mt-1 text-sm text-gray-500">
                Renews on {subscription.renewsOn}
              </p>
            )}
          </div>
        </div>

        <div className="mt-6 border-t border-gray-200 pt-6 space-y-4">
          {/* ✨ FIX: Replaced Manage button with Change Plan and Cancel buttons */}
          <a
            href="/dashboard/billing"
            className="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <Repeat className="mr-2 h-4 w-4" />
            Change Plan
          </a>

          {subscription.status !== 'cancelling' && (
            <button
                onClick={handleCancelSubscription}
                disabled={isCancelling}
                className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 disabled:opacity-50"
            >
                {isCancelling ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <LogOut className="mr-2 h-4 w-4" />}
                Cancel Subscription
            </button>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white shadow-sm ring-1 ring-gray-900/5 sm:rounded-xl p-6">
      {renderContent()}
    </div>
  );
};


// --- Main Page Component ---
const UserProfilePage = (props: { params: { locale: string } }) => {
  const t = useTranslations('UserProfile');

  const finalAppearance = {
    ...clerkAppearance,
    elements: {
      ...(clerkAppearance.elements as any),
      '[data-testid="user-profile-nav-item-billing"]': {
        display: 'none',
      },
    },
  };

  return (
    <div className="space-y-8">
      <div className="rounded-lg border border-navy-light bg-navy p-6">
        <h1 className="text-2xl font-bold text-white">{t('title_bar')}</h1>
        <p className="mt-2 text-gray-300">
          {t('title_bar_description')}
        </p>
      </div>

      <div className="rounded-lg border border-navy-light bg-navy p-6">
        <UserProfile
          routing="path"
          path={getI18nPath('/dashboard/user-profile', props.params.locale)}
          appearance={finalAppearance}
        >
          <UserProfile.Page
            label="Billing"
            url="billing"
            labelIcon={<CreditCard className="h-4 w-4" />}
          >
            <CustomBillingPage />
          </UserProfile.Page>
        </UserProfile>
      </div>
    </div>
  );
};

export default UserProfilePage;