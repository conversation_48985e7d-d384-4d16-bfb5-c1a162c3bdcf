"use client";

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { ChevronDown } from 'lucide-react';
import { MediaCard } from '@/components/media/MediaCard';

interface MediaItem {
  id: string;
  user_id: string;
  filename: string;
  original_filename: string;
  file_path: string;
  file_type: 'image' | 'video';
  file_size: number;
  mime_type?: string;
  url: string;
  workflow_status?: 'draft' | 'completed' | null;
  created_at: string;
  updated_at?: string;
  metadata?: {
    originalUrl?: string;
    duration?: number;
  };
}

export default function RecentPage() {
  const { user } = useUser();
  const router = useRouter();
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortOrder, setSortOrder] = useState<'latest' | 'oldest'>('latest');
  const [deletingFiles, setDeletingFiles] = useState<Set<string>>(new Set());

  // Fetch media from the last 24 hours
  useEffect(() => {
    if (!user) return;

    async function fetchRecentMedia() {
      setLoading(true);

      try {
        console.log('📅 Fetching files from the last 24 hours...');

        // Use centralized API endpoint for consistent URL generation and orphan cleanup
        const response = await fetch('/api/user/files?limit=100', {
          cache: 'no-store'
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.success) {
          // Calculate timestamp for 24 hours ago
          const oneDayAgo = new Date();
          oneDayAgo.setHours(oneDayAgo.getHours() - 24);

          // Filter files from the last 24 hours
          const recentFiles = data.files.filter((file: MediaItem) => {
            const fileDate = new Date(file.created_at);
            return fileDate >= oneDayAgo;
          });

          // Sort by created_at
          recentFiles.sort((a: MediaItem, b: MediaItem) => {
            const dateA = new Date(a.created_at).getTime();
            const dateB = new Date(b.created_at).getTime();
            return sortOrder === 'latest' ? dateB - dateA : dateA - dateB;
          });

          console.log(`✅ Found ${recentFiles.length} files from the last 24 hours`);
          if (data.orphanedRemoved > 0) {
            console.log(`🧹 Cleaned up ${data.orphanedRemoved} orphaned records`);
          }

          setMediaItems(recentFiles);
        } else {
          throw new Error(data.error || 'Failed to load files');
        }
      } catch (err: any) {
        console.error('Error fetching recent media:', err);
      } finally {
        setLoading(false);
      }
    }

    fetchRecentMedia();
  }, [user, sortOrder]);

  const handleDeleteFile = async (fileId: string, fileName: string) => {
    if (!confirm(`Are you sure you want to delete "${fileName}"?`)) return;

    setDeletingFiles(prev => new Set(prev).add(fileId));

    try {
      const response = await fetch(`/api/user/files/${fileId}`, { method: 'DELETE' });
      const result = await response.json();

      if (result.success) {
        setMediaItems(prev => prev.filter(item => item.id !== fileId));
      } else {
        alert(`Failed to delete file: ${result.error}`);
      }
    } catch (error: any) {
      alert(`Error deleting file: ${error.message}`);
    } finally {
      setDeletingFiles(prev => {
        const newSet = new Set(prev);
        newSet.delete(fileId);
        return newSet;
      });
    }
  };

  if (!user) {
    return null;
  }

  return (
    <div className="flex h-full flex-col">
      <h1 className="mb-4 text-xl font-bold text-white">Last 24 Hours</h1>

      {/* Sort dropdown */}
      <div className="mb-4 flex items-center justify-end">
        <button
          onClick={() => setSortOrder(prev => prev === 'latest' ? 'oldest' : 'latest')}
          className="flex items-center space-x-1 text-sm text-white hover:text-gray-300 transition-colors"
        >
          <span>{sortOrder === 'latest' ? 'Latest First' : 'Oldest First'}</span>
          <ChevronDown className="h-4 w-4" />
        </button>
      </div>

      {/* Media grid */}
      {loading ? (
        <div className="flex h-40 items-center justify-center">
          <div className="text-center text-gray-400">Loading...</div>
        </div>
      ) : mediaItems.length === 0 ? (
        <div className="flex h-40 items-center justify-center">
          <div className="text-center text-gray-400">No media from the last 24 hours</div>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {mediaItems.map((item) => (
            <MediaCard
              key={item.id}
              file={item}
              onPreview={() => {}}
              onDelete={handleDeleteFile}
              onOpenStudio={(fileId) => {
                console.log('🎬 Opening studio for file:', fileId);
                router.push(`/studio?file=${fileId}`);
              }}
              isDeleting={deletingFiles.has(item.id)}
            />
          ))}
        </div>
      )}
    </div>
  );
}
