"use client";

import { useTranslations } from 'next-intl';
import Link from 'next/link';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import { OrganizationSwitcher, UserButton } from '@clerk/nextjs';
import { Home, Users, Settings, Menu, X, Database, Webhook, Upload, ImageIcon, Bug } from 'lucide-react';
import { useState } from 'react';

import { getI18nPath } from '@/utils/Helpers';

import { clerkAppearance } from '@/utils/clerk-appearance';

export function DashboardLayoutClient(props: { children: React.ReactNode }) {
  const t = useTranslations('DashboardLayout');
  const params = useParams();
  // Handle the case where params might be null or locale might not exist
  const locale = params && typeof params.locale === 'string' ? params.locale : 'en';
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const menuItems = [
    {
      href: '/dashboard',
      label: t('home'),
      icon: <Home className="h-5 w-5" />
    },
    {
      href: '/dashboard/organization-profile/organization-members',
      label: t('members'),
      icon: <Users className="h-5 w-5" />
    },
    {
      href: '/dashboard/organization-profile',
      label: t('settings'),
      icon: <Settings className="h-5 w-5" />
    },
    {
      href: '/dashboard/supabase-test',
      label: 'Supabase Test',
      icon: <Database className="h-5 w-5" />
    },
    {
      href: '/dashboard/webhook-test',
      label: 'Webhook Test',
      icon: <Webhook className="h-5 w-5" />
    },
    {
      href: '/dashboard/debug-files',
      label: 'Debug Files',
      icon: <Bug className="h-5 w-5" />
    },
  ];

  return (
    <div className="min-h-screen bg-navy text-white">
      {/* Navbar */}
      <nav className="sticky top-0 z-50 border-b border-navy-light bg-navy/90 backdrop-blur-md">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center">
              <Link href="/dashboard" className="flex items-center">
                <Image src="/images/logo.png" alt="Guardiavision Logo" width={40} height={40} className="h-10 w-10" />
                <span className="ml-3 text-xl font-bold text-white">Guardiavision</span>
              </Link>

              <div className="ml-4">
                <OrganizationSwitcher
                  organizationProfileMode="navigation"
                  organizationProfileUrl={getI18nPath(
                    '/dashboard/organization-profile',
                    locale,
                  )}
                  afterCreateOrganizationUrl="/dashboard"
                  hidePersonal
                  skipInvitationScreen
                  appearance={clerkAppearance}
                />
              </div>
            </div>

            <div className="hidden md:block">
              <div className="ml-10 flex items-center space-x-4">
                {menuItems.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className="flex items-center space-x-1 text-gray-300 hover:text-white"
                  >
                    {item.icon}
                    <span>{item.label}</span>
                  </Link>
                ))}

                <UserButton
                  userProfileMode="navigation"
                  userProfileUrl="/dashboard/user-profile"
                  appearance={clerkAppearance}
                />
              </div>
            </div>

            <div className="md:hidden">
              <button
                type="button"
                className="text-gray-300 hover:text-white"
                onClick={() => setIsMenuOpen(!isMenuOpen)}
              >
                {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile menu */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="space-y-1 px-2 pb-3 pt-2">
              {menuItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className="flex items-center space-x-2 rounded-md px-3 py-2 text-base font-medium text-gray-300 hover:bg-navy-light hover:text-white"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.icon}
                  <span>{item.label}</span>
                </Link>
              ))}
              <div className="mt-4 flex items-center justify-center px-3">
                <UserButton
                  userProfileMode="navigation"
                  userProfileUrl="/dashboard/user-profile"
                  appearance={clerkAppearance}
                />
              </div>
            </div>
          </div>
        )}
      </nav>

      {/* Main content */}
      <div className="min-h-[calc(100vh-64px)] bg-navy-dark">
        <div className="container mx-auto px-4 py-6 sm:px-6 lg:px-8">
          {props.children}
        </div>
      </div>
    </div>
  );
}
