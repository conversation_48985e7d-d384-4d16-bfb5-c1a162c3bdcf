import { getTranslations } from 'next-intl/server';
import { DirectUploadComponent } from '@/components/uploads/DirectUploadComponent';

export async function generateMetadata(props: { params: { locale: string } }) {
  const t = await getTranslations({
    locale: props.params.locale,
    namespace: 'Dashboard',
  });

  return {
    title: `${t('meta_title')} - Uploads`,
    description: t('meta_description'),
  };
}

export default function UploadsPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="mb-6 text-3xl font-bold text-white">Your Uploads</h1>
      <p className="mb-8 text-gray-300">
        Upload photos and videos to blur faces, license plates, and other sensitive information.
      </p>

      <div className="mb-4 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
        <h3 className="text-lg font-semibold text-blue-400 mb-2">🚀 New Direct Upload System</h3>
        <p className="text-sm text-blue-300">
          Now using direct uploads to Supabase Storage - faster, more reliable, and supports larger files!
        </p>
      </div>

      <DirectUploadComponent
        onUploadComplete={(result) => {
          console.log('Upload completed:', result);
          // Optionally refresh the page or show success message
        }}
      />
    </div>
  );
}

export const dynamic = 'force-dynamic';
