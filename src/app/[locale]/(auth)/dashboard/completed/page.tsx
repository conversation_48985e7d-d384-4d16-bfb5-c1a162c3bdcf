"use client";

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { ChevronDown, Download } from 'lucide-react';
import Image from 'next/image';

interface MediaItem {
  id: string;
  user_id: string;
  filename: string;
  original_filename?: string;
  file_path: string;
  file_type: 'image' | 'video';
  file_size: number;
  mime_type?: string;
  url: string;
  workflow_status?: 'draft' | 'completed' | null;
  created_at: string;
  updated_at?: string;
}

export default function CompletedPage() {
  const { user } = useUser();
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortOrder, setSortOrder] = useState<'latest' | 'oldest'>('latest');

  // Fetch completed media items (workflow_status = 'completed')
  useEffect(() => {
    if (!user) return;

    async function fetchCompletedMedia() {
      setLoading(true);

      try {
        console.log('✅ Fetching completed files...');

        // Use centralized API endpoint for consistent URL generation and orphan cleanup
        const response = await fetch('/api/user/files?limit=100', {
          cache: 'no-store'
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.success) {
          // Filter files with workflow_status = 'completed'
          const completedFiles = data.files.filter((file: MediaItem) => {
            return file.workflow_status === 'completed';
          });

          // Sort by updated_at or created_at
          completedFiles.sort((a: MediaItem, b: MediaItem) => {
            const dateA = new Date(a.updated_at || a.created_at).getTime();
            const dateB = new Date(b.updated_at || b.created_at).getTime();
            return sortOrder === 'latest' ? dateB - dateA : dateA - dateB;
          });

          console.log(`✅ Found ${completedFiles.length} completed files`);
          if (data.orphanedRemoved > 0) {
            console.log(`🧹 Cleaned up ${data.orphanedRemoved} orphaned records`);
          }

          setMediaItems(completedFiles);
        } else {
          throw new Error(data.error || 'Failed to load files');
        }
      } catch (err: any) {
        console.error('Error fetching completed media:', err);
      } finally {
        setLoading(false);
      }
    }

    fetchCompletedMedia();
  }, [user, sortOrder]);

  // Format time elapsed
  const formatTimeElapsed = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffMonths = Math.floor(diffDays / 30);

    if (diffMonths > 0) {
      return `${diffMonths} month${diffMonths !== 1 ? 's' : ''} ago`;
    } else if (diffDays > 0) {
      return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
    } else {
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      if (diffHours > 0) {
        return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
      } else {
        const diffMinutes = Math.floor(diffMs / (1000 * 60));
        return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`;
      }
    }
  };

  const formatFileSize = (bytes: number) => {
    const mb = bytes / (1024 * 1024);
    return mb < 1 ? `${(bytes / 1024).toFixed(0)}KB` : `${mb.toFixed(1)}MB`;
  };

  const handleDownload = (item: MediaItem) => {
    if (!item.url) return;

    const link = document.createElement('a');
    link.href = item.url;
    link.download = item.original_filename || item.filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (!user) {
    return null;
  }

  return (
    <div className="flex h-full flex-col">
      <div className="mb-4">
        <h1 className="text-xl font-bold text-white">Completed</h1>
        <p className="text-sm text-gray-400 mt-1">Files you've processed and saved from the studio</p>
      </div>

      {/* Sort dropdown */}
      <div className="mb-4 flex items-center justify-end">
        <button
          onClick={() => setSortOrder(prev => prev === 'latest' ? 'oldest' : 'latest')}
          className="flex items-center space-x-1 text-sm text-white hover:text-gray-300 transition-colors"
        >
          <span>{sortOrder === 'latest' ? 'Recently Completed' : 'Oldest First'}</span>
          <ChevronDown className="h-4 w-4" />
        </button>
      </div>

      {/* Media grid */}
      {loading ? (
        <div className="flex h-40 items-center justify-center">
          <div className="text-center text-gray-400">Loading...</div>
        </div>
      ) : mediaItems.length === 0 ? (
        <div className="flex h-40 items-center justify-center">
          <div className="text-center">
            <p className="text-gray-400">No completed items found</p>
            <p className="text-sm text-gray-500 mt-1">Process and save a file from the studio to see it here</p>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
          {mediaItems.map((item) => {
            const fileName = item.original_filename || item.filename;
            const isVideo = item.file_type === 'video' || item.mime_type?.startsWith('video/');

            return (
              <div key={item.id} className="group overflow-hidden rounded-lg bg-gray-800 hover:bg-gray-750 transition-colors relative">
                {/* Thumbnail */}
                <div className="relative aspect-video bg-gray-700">
                  {item.url && !isVideo ? (
                    <Image
                      src={item.url}
                      alt={fileName}
                      fill
                      className="object-cover"
                      unoptimized
                    />
                  ) : isVideo ? (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <svg className="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  ) : (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <Download className="h-8 w-8 text-gray-500" />
                    </div>
                  )}

                  {/* Completed status badge */}
                  <div className="absolute top-2 left-2 rounded bg-green-500/90 px-1.5 py-0.5 text-xs text-white">
                    Completed
                  </div>

                  {/* File type badge */}
                  <div className="absolute top-2 right-2 rounded bg-black/70 px-1.5 py-0.5 text-xs text-white">
                    {item.file_type?.toUpperCase() || (isVideo ? 'VIDEO' : 'IMAGE')}
                  </div>

                  {/* Download button - shows on hover */}
                  <button
                    onClick={() => handleDownload(item)}
                    className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity bg-blue-600 hover:bg-blue-700 text-white rounded-full p-2"
                    title="Download file"
                  >
                    <Download className="h-4 w-4" />
                  </button>
                </div>

                {/* File info */}
                <div className="p-3">
                  <p className="truncate text-sm font-medium text-white" title={fileName}>
                    {fileName}
                  </p>
                  <div className="flex justify-between items-center mt-1">
                    <span className="text-xs text-gray-400">
                      {formatFileSize(item.file_size)}
                    </span>
                    <span className="text-xs text-gray-500">
                      {item.updated_at
                        ? `Saved ${formatTimeElapsed(item.updated_at)}`
                        : formatTimeElapsed(item.created_at)}
                    </span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}
