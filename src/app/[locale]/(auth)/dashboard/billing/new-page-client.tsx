'use client';

import React, { useState, useEffect } from 'react';
import { Check, X, Zap, Users, CloudUpload, HelpCircle, FileUp, Star, LucideProps } from 'lucide-react';
import { motion, AnimatePresence, Transition } from 'framer-motion';
import { loadStripe } from '@stripe/stripe-js';
import { EmbeddedCheckout, EmbeddedCheckoutProvider } from '@stripe/react-stripe-js';
import * as Dialog from '@radix-ui/react-dialog';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

type Feature = {
  text: string;
  icon: React.ForwardRefExoticComponent<Omit<LucideProps, "ref"> & React.RefAttributes<SVGSVGElement>>;
  included: boolean;
  tooltip?: string | null;
};

// Map feature text to icons
const featureIconMap: { [key: string]: Feature['icon'] } = {
  'AI Blurring': Star,
  'credits': Zap,
  'Batch processing': CloudUpload,
  'No watermark': Check,
  'support': Check,
  'Account Manager': Users,
  'beta': Star,
  'file size': FileUp,
};

// Helper to get icon for a feature
const getFeatureIcon = (featureText: string): Feature['icon'] => {
  for (const [key, icon] of Object.entries(featureIconMap)) {
    if (featureText.toLowerCase().includes(key.toLowerCase())) {
      return icon;
    }
  }
  return Check; // default icon
};

const PricingToggle = ({ isYearly, onToggle }: { isYearly: boolean; onToggle: (yearly: boolean) => void }) => (
  <div className="relative flex justify-center mt-8">
    <div className="relative flex items-center bg-gray-800 rounded-full p-1.5">
      <motion.div
        className="absolute h-[85%] w-[48%] bg-indigo-600 rounded-full"
        layout
        transition={{ type: 'spring', stiffness: 300, damping: 30 }}
        animate={{ x: isYearly ? '100%' : '2%' }}
      />
      <button onClick={() => onToggle(false)} className="relative z-10 px-6 py-2 rounded-full text-sm font-medium transition-colors text-white">Monthly</button>
      <button onClick={() => onToggle(true)} className="relative z-10 px-6 py-2 rounded-full text-sm font-medium transition-colors text-white">
        Yearly
        <span className="absolute -top-2.5 -right-5 bg-green-500 text-white text-xs font-bold px-2 py-0.5 rounded-full transform rotate-[15deg] shadow-lg">
          -40%
        </span>
      </button>
    </div>
  </div>
);

const PricingCard = ({ plan, isYearly, onSubscribe, isLoading, currentPriceId }: any) => {
  const isCurrentPlan = plan.stripePriceId === currentPriceId;

  const buttonVariants: { [key: string]: { bg: string, glow: string } } = {
    teal:   { bg: 'bg-teal-500 hover:bg-teal-600',   glow: 'hover:shadow-teal-500/40' },
    green:  { bg: 'bg-green-500 hover:bg-green-600',  glow: 'hover:shadow-green-500/40' },
    purple: { bg: 'bg-purple-600 hover:bg-purple-700', glow: 'hover:shadow-purple-500/40' },
    blue: { bg: 'bg-blue-600 hover:bg-blue-700', glow: 'hover:shadow-blue-500/40' },
  };

  const cardAnimation = {
    initial: { opacity: 0, y: 50, scale: 0.95 },
    animate: { opacity: 1, y: 0, scale: 1 },
    exit: { opacity: 0, y: -30, scale: 0.95 },
  };

  const cardTransition: Transition = { duration: 0.4, ease: 'easeInOut' };
  const { bg, glow } = buttonVariants[plan.buttonVariant] || { bg: 'bg-indigo-600 hover:bg-indigo-700', glow: 'hover:shadow-indigo-500/40' };

  const displayPrice = isYearly ? Math.round(plan.yearlyPrice / 12) : plan.price;
  const billedPrice = isYearly ? plan.yearlyPrice : null;

  return (
    <motion.div
      variants={cardAnimation}
      initial="initial"
      animate="animate"
      exit="exit"
      transition={cardTransition}
      className={`group relative flex flex-col h-full p-6 rounded-2xl border ${isCurrentPlan ? 'border-blue-500 opacity-80' : plan.isPopular ? 'border-green-500' : 'border-gray-700'} bg-gray-800/50 transform hover:scale-[1.03] transition-all duration-300 shadow-lg ${glow}`}
    >
      {isCurrentPlan && (
        <div className="absolute -top-4 left-1/2 -translate-x-1/2 bg-blue-500 text-white text-xs font-bold px-4 py-1 rounded-full shadow-md">
          Current Plan
        </div>
      )}
      {plan.isPopular && !isCurrentPlan && (
        <div className="absolute -top-4 left-1/2 -translate-x-1/2 bg-green-500 text-white text-xs font-bold px-4 py-1 rounded-full shadow-md">
          Most Popular
        </div>
      )}

      <div className="flex-grow">
        <h3 className="text-xl font-semibold text-white">{plan.name}</h3>
        <p className="mt-2 text-sm text-gray-400">{plan.description}</p>
        <div className="mt-4">
          <span className="text-4xl font-bold text-white">€{displayPrice}</span>
          <span className="text-lg font-medium text-gray-400">/month</span>
        </div>
        {billedPrice && (
          <p className="text-sm text-gray-400 mt-1">Billed as €{billedPrice}/year</p>
        )}
        <ul className="mt-4 space-y-2 text-sm">
          {plan.features.map((feature: any, index: number) => {
            const Icon = getFeatureIcon(feature.text);
            return (
              <li key={index} className="flex items-start relative group/tooltip">
                <Icon className={`h-5 w-5 mr-3 flex-shrink-0 mt-0.5 ${feature.included ? 'text-green-500' : 'text-gray-500'}`} />
                <div className="flex-1">
                  <span className={feature.included ? 'text-gray-300' : 'text-gray-500 line-through'}>{feature.text}</span>
                </div>
              </li>
            );
          })}
        </ul>
      </div>

      <div className="mt-auto pt-4">
        <button
          onClick={() => onSubscribe(plan.stripePriceId)}
          disabled={isLoading || isCurrentPlan}
          className={`w-full py-2.5 px-6 rounded-lg font-semibold text-white transition-all duration-300 ${isCurrentPlan ? 'bg-gray-600 cursor-not-allowed' : `${bg} disabled:bg-gray-500 disabled:cursor-not-allowed`}`}
        >
          {isCurrentPlan ? 'Current Plan' : isLoading ? 'Processing...' : plan.buttonText || 'Choose Plan'}
        </button>
      </div>
    </motion.div>
  );
};

const CheckoutPanel = ({ isOpen, onClose, clientSecret }: { isOpen: boolean; onClose: () => void; clientSecret: string | null; }) => {
  if (!clientSecret) return null;
  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 z-50 bg-black/70 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
        <Dialog.Content className="fixed z-50 left-1/2 top-1/2 w-[95vw] max-w-xl -translate-x-1/2 -translate-y-1/2 rounded-xl bg-white shadow-2xl data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95">
          <div className="h-[90vh] max-h-[650px] overflow-hidden rounded-xl">
            <EmbeddedCheckoutProvider stripe={stripePromise} options={{ clientSecret }}>
              <EmbeddedCheckout className="h-full w-full overflow-y-auto" />
            </EmbeddedCheckoutProvider>
          </div>
          <Dialog.Close className="absolute top-3 right-3 rounded-full p-1.5 text-gray-500 transition-colors hover:bg-gray-100 hover:text-gray-800">
            <X className="h-5 w-5" />
            <span className="sr-only">Close</span>
          </Dialog.Close>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

// --- Main Page Component ---
export function NewBillingPageClient() {
  const [isYearly, setIsYearly] = useState(true);
  const [showSuccess, setShowSuccess] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [isPanelOpen, setIsPanelOpen] = useState(false);
  const [currentPriceId, setCurrentPriceId] = useState<string | null>(null);
  const [plans, setPlans] = useState<any[]>([]);
  const [plansLoading, setPlansLoading] = useState(true);

  // Fetch pricing plans from API
  useEffect(() => {
    const fetchPricingPlans = async () => {
      try {
        setPlansLoading(true);
        const response = await fetch('/api/pricing/plans');
        const data = await response.json();
        if (data.success && data.plans) {
          setPlans(data.plans.filter((p: any) => p.planId !== 'free')); // Exclude free plan
        }
      } catch (error) {
        console.error('Failed to fetch pricing plans:', error);
      } finally {
        setPlansLoading(false);
      }
    };

    fetchPricingPlans();
  }, []);

  // Fetch the user's subscription status when the page loads
  useEffect(() => {
    const fetchSubscriptionStatus = async () => {
      try {
        const response = await fetch('/api/billing/subscription-status');
        if (response.ok) {
          const data = await response.json();
          setCurrentPriceId(data.priceId);
        }
      } catch (error) {
        console.error('Failed to fetch subscription status:', error);
      }
    };

    fetchSubscriptionStatus();
  }, []);

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('checkout_session_id')) {
      setShowSuccess(true);
      window.history.replaceState(null, '', window.location.pathname);
    }
  }, []);

  const handleSubscribe = async (priceId: string) => {
    setIsLoading(true);
    setClientSecret(null);
    try {
      const response = await fetch('/api/billing/checkout', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ priceId: priceId }),
      });
      const responseBody = await response.json();
      if (!response.ok) throw new Error(responseBody.error || 'Server error');
      const { clientSecret } = responseBody;
      if (!clientSecret) throw new Error('Client Secret was not returned.');
      setClientSecret(clientSecret);
      setIsPanelOpen(true);
    } catch (error: any) {
      console.error("Stripe checkout error:", error);
      alert(`Could not initiate payment: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Transform plans for display based on yearly/monthly selection
  const currentPlans = plans.map(plan => ({
    name: plan.name,
    description: plan.description,
    price: isYearly ? Math.round(plan.price.yearly / 12) : plan.price.monthly,
    yearlyPrice: isYearly ? plan.price.yearly : null,
    stripePriceId: isYearly ? plan.stripePriceId?.yearly : plan.stripePriceId?.monthly,
    features: plan.features,
    isPopular: plan.isPopular,
    buttonVariant: plan.buttonVariant,
    buttonText: plan.buttonText,
  }));

  if (showSuccess) {
    return (
      <div className="flex flex-col items-center justify-center p-8 min-h-screen text-center bg-gray-900 text-white">
        <motion.div initial={{ opacity: 0, scale: 0.9 }} animate={{ opacity: 1, scale: 1 }} transition={{ duration: 0.5, ease: "easeOut" }} className="flex flex-col items-center">
          <Check className="h-16 w-16 text-green-500 bg-green-500/10 rounded-full p-3 mb-6" />
          <h1 className="text-4xl font-bold mb-4">Payment Successful!</h1>
          <p className="text-gray-400 mb-8">Thank you for subscribing. Your plan has been updated.</p>
          <button onClick={() => { setShowSuccess(false); setIsPanelOpen(false); }} className="px-6 py-2 bg-indigo-600 text-white font-semibold rounded-lg hover:bg-indigo-700 transition-colors">
            Back to Plans
          </button>
        </motion.div>
      </div>
    );
  }

  return (
    <>
      <section id="pricing" className="py-8 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="text-3xl font-semibold tracking-tight text-white sm:text-4xl">Subscription Plans</h2>
            <p className="mt-4 text-lg text-gray-300">Choose the right plan, reduce work time by ~95%.</p>
          </div>
          <PricingToggle isYearly={isYearly} onToggle={setIsYearly} />

          {plansLoading ? (
            <div className="mt-10 flex items-center justify-center py-12">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-4 border-purple-600/30 border-t-purple-600 mx-auto mb-3"></div>
                <p className="text-gray-400 text-sm">Loading pricing plans...</p>
              </div>
            </div>
          ) : (
            <div className="mt-10 grid max-w-md mx-auto gap-8 md:max-w-none md:grid-cols-3">
              <AnimatePresence mode="wait">
                {currentPlans.map((plan, index) => (
                  <PricingCard
                    key={`${plan.stripePriceId}-${index}`}
                    plan={plan}
                    isYearly={isYearly}
                    onSubscribe={handleSubscribe}
                    isLoading={isLoading}
                    currentPriceId={currentPriceId}
                  />
                ))}
              </AnimatePresence>
            </div>
          )}
        </div>
      </section>

      <CheckoutPanel isOpen={isPanelOpen} onClose={() => setIsPanelOpen(false)} clientSecret={clientSecret} />
    </>
  );
}