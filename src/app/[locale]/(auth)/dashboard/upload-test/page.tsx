'use client';

import { useState } from 'react';
import { DirectUploadComponent } from '@/components/uploads/DirectUploadComponent';
import { FileUploaderFixed } from '@/components/uploads/FileUploaderFixed';

export default function UploadTestPage() {
  const [activeTab, setActiveTab] = useState<'direct' | 'proxy'>('direct');

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="mb-6 text-3xl font-bold text-white">Upload System Comparison</h1>
      
      <div className="mb-8 p-6 bg-gray-800/50 rounded-lg">
        <h2 className="text-xl font-semibold text-white mb-4">🔬 Test Both Upload Methods</h2>
        <p className="text-gray-300 mb-4">
          Compare the old proxy-based upload system with the new direct upload system.
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
          <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
            <h3 className="font-semibold text-red-400 mb-2">❌ Old System (Proxy Upload)</h3>
            <ul className="text-red-300 space-y-1">
              <li>• File goes through Next.js API first</li>
              <li>• Limited to ~4-10MB (Vercel limit)</li>
              <li>• Double upload (browser → API → Supabase)</li>
              <li>• Slower and uses more resources</li>
              <li>• Can timeout on large files</li>
            </ul>
          </div>
          
          <div className="p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
            <h3 className="font-semibold text-green-400 mb-2">✅ New System (Direct Upload)</h3>
            <ul className="text-green-300 space-y-1">
              <li>• File goes directly to Supabase Storage</li>
              <li>• Supports up to 50MB files</li>
              <li>• Single upload (browser → Supabase)</li>
              <li>• Faster and more efficient</li>
              <li>• No timeout issues</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 mb-6 bg-gray-800/50 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab('direct')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'direct'
              ? 'bg-green-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-gray-700'
          }`}
        >
          🚀 New Direct Upload
        </button>
        <button
          onClick={() => setActiveTab('proxy')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'proxy'
              ? 'bg-red-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-gray-700'
          }`}
        >
          🐌 Old Proxy Upload
        </button>
      </div>

      {/* Upload Components */}
      <div className="space-y-6">
        {activeTab === 'direct' && (
          <div className="p-6 bg-green-500/5 border border-green-500/20 rounded-lg">
            <h3 className="text-lg font-semibold text-green-400 mb-4">
              🚀 Direct Upload System
            </h3>
            <p className="text-green-300 mb-4 text-sm">
              Files upload directly to Supabase Storage using signed URLs. Supports larger files and is much faster.
            </p>
            <DirectUploadComponent
              onUploadComplete={(result) => {
                console.log('✅ Direct upload completed:', result);
              }}
              onUploadStart={() => {
                console.log('🚀 Direct upload started');
              }}
              onUploadEnd={() => {
                console.log('🏁 Direct upload ended');
              }}
            />
          </div>
        )}

        {activeTab === 'proxy' && (
          <div className="p-6 bg-red-500/5 border border-red-500/20 rounded-lg">
            <h3 className="text-lg font-semibold text-red-400 mb-4">
              🐌 Proxy Upload System (Legacy)
            </h3>
            <p className="text-red-300 mb-4 text-sm">
              Files go through the Next.js API first, then to Supabase. Limited to smaller files and slower.
            </p>
            <div className="mb-4 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded">
              <p className="text-yellow-400 text-sm">
                ⚠️ <strong>Warning:</strong> This method may fail with large files (>10MB) due to Vercel limits.
              </p>
            </div>
            <FileUploaderFixed
              onUploadComplete={(result) => {
                console.log('✅ Proxy upload completed:', result);
              }}
            />
          </div>
        )}
      </div>

      {/* Performance Tips */}
      <div className="mt-8 p-6 bg-blue-500/10 border border-blue-500/20 rounded-lg">
        <h3 className="text-lg font-semibold text-blue-400 mb-4">💡 Performance Tips</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <h4 className="font-medium text-blue-300 mb-2">For Images:</h4>
            <ul className="text-blue-200 space-y-1">
              <li>• Use JPEG for photos (smaller file size)</li>
              <li>• Use PNG for graphics with transparency</li>
              <li>• WebP offers best compression</li>
              <li>• Keep under 10MB for best performance</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-blue-300 mb-2">For Videos:</h4>
            <ul className="text-blue-200 space-y-1">
              <li>• Use MP4 format for best compatibility</li>
              <li>• Compress videos before upload</li>
              <li>• Keep under 50MB (Supabase limit)</li>
              <li>• Consider 720p for faster processing</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
