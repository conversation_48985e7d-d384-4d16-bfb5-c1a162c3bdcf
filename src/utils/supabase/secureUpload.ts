// Secure upload utility that uses service role for storage but maintains user isolation
import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';

export async function secureUploadFile(
  file: File,
  userId: string,
  type: 'original' | 'processed' | 'thumbnail' = 'original'
): Promise<{ path: string; url: string } | null> {
  try {
    // Use service role client for storage operations
    const supabase = createServiceRoleSupabaseClient();
    
    // Determine bucket based on type
    let bucket: string;
    switch (type) {
      case 'original':
        bucket = 'user-uploads';
        break;
      case 'processed':
        bucket = 'processed-content';
        break;
      case 'thumbnail':
        bucket = 'thumbnails';
        break;
      default:
        bucket = 'user-uploads';
    }
    
    // Generate secure file path with user isolation (consistent with existing structure)
    const timestamp = Date.now();
    const sanitizedFilename = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
    const filePath = `${userId}/uploads/${timestamp}_${sanitizedFilename}`;
    
    // Upload file using service role (bypasses RLS for upload)
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      });
    
    if (error) {
      console.error('Secure upload error:', error);
      return null;
    }
    
    // Get public URL
    const { data: urlData } = supabase.storage
      .from(bucket)
      .getPublicUrl(filePath);
    
    return {
      path: filePath,
      url: urlData.publicUrl
    };
    
  } catch (error) {
    console.error('Secure upload error:', error);
    return null;
  }
}

// Secure file listing that respects user isolation
export async function getUserFiles(
  userId: string,
  type?: 'original' | 'processed' | 'thumbnail'
): Promise<any[] | null> {
  try {
    const supabase = createServiceRoleSupabaseClient();
    
    const bucket = type === 'processed' ? 'processed-content' : 
                   type === 'thumbnail' ? 'thumbnails' : 'user-uploads';
    
    const folder = type ? `${userId}/${type}` : `${userId}`;
    
    // List files in user's folder only
    const { data, error } = await supabase.storage
      .from(bucket)
      .list(folder);
    
    if (error) {
      console.error('List files error:', error);
      return null;
    }
    
    return data || [];
  } catch (error) {
    console.error('List files error:', error);
    return null;
  }
}

// Secure file deletion that checks user ownership
export async function deleteUserFile(
  userId: string,
  filePath: string
): Promise<boolean> {
  try {
    // Verify the file path belongs to the user
    if (!filePath.startsWith(`${userId}/`)) {
      console.error('Unauthorized file deletion attempt');
      return false;
    }
    
    const supabase = createServiceRoleSupabaseClient();
    
    // Determine bucket from file path
    let bucket = 'user-uploads';
    if (filePath.includes('/processed/')) {
      bucket = 'processed-content';
    } else if (filePath.includes('/thumbnail/')) {
      bucket = 'thumbnails';
    }
    
    const { error } = await supabase.storage
      .from(bucket)
      .remove([filePath]);
    
    if (error) {
      console.error('Delete file error:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Delete file error:', error);
    return false;
  }
}
