// Direct client-side upload utilities
import { useUser } from '@clerk/nextjs';

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface UploadResult {
  success: boolean;
  fileId?: string;
  filePath?: string;
  publicUrl?: string;
  error?: string;
  fileData?: {
    id: string;
    original_filename: string;
    file_path: string;
    file_type: 'image' | 'video';
    file_size: number;
    mime_type: string;
    created_at: string;
  };
}

export interface DirectUploadOptions {
  onProgress?: (progress: UploadProgress) => void;
  onSuccess?: (result: UploadResult) => void;
  onError?: (error: string) => void;
}

// Simplified direct upload function using fallback API
export async function directUploadToSupabase(
  file: File,
  userId: string,
  onProgress?: (progress: UploadProgress) => void
): Promise<UploadResult> {
  try {
    console.log('🚀 Starting upload:', { fileName: file.name, fileSize: file.size, userId });

    // Step 1: Validate file
    const validation = validateFile(file);
    if (!validation.isValid) {
      return { success: false, error: validation.error };
    }

    onProgress?.({ loaded: 0, total: file.size, percentage: 0 });
    
    const formData = new FormData();
    formData.append('file', file);

    const uploadResponse = await fetch('/api/upload-fallback', {
      method: 'POST',
      body: formData
    });

    if (!uploadResponse.ok) {
      const errorData = await uploadResponse.json().catch(() => ({ error: 'Upload failed' }));
      console.error('❌ Upload failed:', errorData);
      return { success: false, error: errorData.error || 'Upload failed' };
    }

    const uploadResult = await uploadResponse.json();
    
    console.log('✅ File uploaded successfully');
    onProgress?.({ loaded: file.size, total: file.size, percentage: 100 });

    const result: UploadResult = {
      success: true,
      fileId: uploadResult.fileId,
      filePath: uploadResult.filePath,
      publicUrl: uploadResult.publicUrl,
      fileData: uploadResult.fileData
    };

    console.log('🎉 Upload completed successfully:', result);
    return result;

  } catch (error: any) {
    console.error('❌ Upload error:', error);
    return { success: false, error: error.message || 'Upload failed' };
  }
}

// File validation
function validateFile(file: File): { isValid: boolean; error?: string } {
  const allowedImageTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
  const allowedVideoTypes = ['video/mp4', 'video/quicktime', 'video/webm', 'video/avi', 'video/x-msvideo'];
  const allAllowedTypes = [...allowedImageTypes, ...allowedVideoTypes];

  if (!allAllowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: `File type ${file.type} not supported. Allowed: Images (JPEG, PNG, WebP, GIF) and Videos (MP4, MOV, WebM, AVI)`
    };
  }

  // Size limits: 50MB for videos, 10MB for images
  const isVideo = allowedVideoTypes.includes(file.type);
  const maxSize = isVideo ? 50 * 1024 * 1024 : 10 * 1024 * 1024;

  if (file.size > maxSize) {
    const maxSizeMB = maxSize / (1024 * 1024);
    const fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);
    return {
      isValid: false,
      error: `File too large: ${fileSizeMB}MB. Maximum allowed: ${maxSizeMB}MB for ${isVideo ? 'videos' : 'images'}`
    };
  }

  return { isValid: true };
}

// Hook for direct Supabase uploads
export function useDirectUpload() {
  const { user } = useUser();

  const uploadFile = async (
    file: File,
    options: DirectUploadOptions = {}
  ): Promise<UploadResult> => {
    const { onProgress, onSuccess, onError } = options;

    if (!user?.id) {
      const error = 'User not authenticated';
      onError?.(error);
      return { success: false, error };
    }

    try {
      const result = await directUploadToSupabase(file, user.id, onProgress);
      
      if (result.success) {
        onSuccess?.(result);
      } else {
        onError?.(result.error || 'Upload failed');
      }
      
      return result;
    } catch (error: any) {
      const errorMessage = error.message || 'Upload failed';
      onError?.(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  return { uploadFile };
}
