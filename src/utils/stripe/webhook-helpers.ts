import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

export async function getUserByCustomerId(supabase: any, customerId: string) {
  try {
    // First try to find user by stripe_customer_id
    const { data: user, error } = await supabase
      .from('users')
      .select('*')
      .eq('stripe_customer_id', customerId)
      .single();

    if (!error && user) {
      return user;
    }

    // If not found, try to find by email
    const customer = await stripe.customers.retrieve(customerId);
    if (!customer || customer.deleted || !customer.email) {
      return null;
    }

    const { data: userByEmail, error: emailError } = await supabase
      .from('users')
      .select('*')
      .eq('email', customer.email)
      .single();

    if (!emailError && userByEmail) {
      // Update the user with the stripe_customer_id for future lookups
      await supabase
        .from('users')
        .update({ stripe_customer_id: customerId })
        .eq('id', userByEmail.id);

      return userByEmail;
    }

    return null;
  } catch (error) {
    console.error('Error finding user by customer ID:', error);
    return null;
  }
}

export async function recordPayment(
  supabase: any,
  userId: string,
  paymentIntent: Stripe.PaymentIntent,
  status: string
) {
  try {
    await supabase
      .from('payment_history')
      .insert({
        user_id: userId,
        stripe_payment_intent_id: paymentIntent.id,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
        status: status,
        payment_method: paymentIntent.payment_method_types?.[0] || 'unknown',
        description: `Payment ${status}: $${(paymentIntent.amount / 100).toFixed(2)}`,
        metadata: {
          payment_method_id: paymentIntent.payment_method,
          client_secret: paymentIntent.client_secret,
        },
      });

    console.log(`✅ Payment recorded: ${paymentIntent.id} - ${status}`);
  } catch (error) {
    console.error('❌ Error recording payment:', error);
    throw error;
  }
}

export async function processSubscription(
  supabase: any,
  userId: string,
  subscription: Stripe.Subscription
) {
  try {
    const priceId = subscription.items.data[0]?.price.id;
    const planId = await getPlanIdFromPriceId(priceId, supabase);

    if (!planId) {
      console.error('❌ Unknown price ID:', priceId);
      return;
    }

    console.log(`🔄 Processing subscription for user ${userId}: ${planId} plan`);

    // Try using the database function first
    try {
      const { error } = await supabase.rpc('update_user_subscription', {
        p_user_id: userId,
        p_plan_id: planId,
        p_stripe_customer_id: subscription.customer,
        p_stripe_subscription_id: subscription.id,
        p_stripe_price_id: priceId,
        p_subscription_status: subscription.status,
        p_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
        p_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
      });

      if (error) {
        throw error;
      }

      console.log(`✅ Subscription processed using database function for user ${userId}: ${planId} plan`);
    } catch (dbError: any) {
      console.error('❌ Database function failed, using manual approach:', dbError);

      // Fallback: Update subscription manually
      await processSubscriptionManually(supabase, userId, subscription, planId, priceId);
    }
  } catch (error) {
    console.error('❌ Error processing subscription:', error);
    throw error;
  }
}

export async function processSubscriptionManually(
  supabase: any,
  userId: string,
  subscription: Stripe.Subscription,
  planId: string,
  priceId: string
) {
  console.log(`🔧 Processing subscription manually for user ${userId}`);

  // Get plan details from pricing_plans table (new structure)
  const { data: plan, error: planError } = await supabase
    .from('pricing_plans')
    .select('*')
    .eq('id', planId)
    .single();

  if (planError || !plan) {
    console.error('❌ Plan not found:', planId);
    throw new Error(`Plan not found: ${planId}`);
  }

  // Extract plan type and billing cycle from the plan ID
  // e.g., 'standard_monthly' -> type='standard', cycle='monthly'
  const planType = plan.plan_type || 'standard';
  const billingCycle = plan.billing_cycle || 'monthly';

  // Update user subscription details
  // Using actual columns from your users table:
  // id, email, username, first_name, last_name, avatar_url, credits, created_at, updated_at,
  // subscription_type, subscription_status, clerk_plan_id, subscription_period_end,
  // credits_expire_at, stripe_customer_id, stripe_subscription_id
  const { error: userUpdateError } = await supabase
    .from('users')
    .update({
      subscription_type: planType, // 'standard', 'pro', 'premium'
      subscription_status: subscription.status,
      stripe_customer_id: subscription.customer,
      stripe_subscription_id: subscription.id,
      clerk_plan_id: priceId, // Store Stripe price ID in clerk_plan_id
      subscription_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
      updated_at: new Date().toISOString(),
    })
    .eq('id', userId);

  if (userUpdateError) {
    console.error('❌ Error updating user:', userUpdateError);
    throw userUpdateError;
  }

  // Handle credits for paid plans
  // IMPORTANT: Credits do NOT expire automatically while subscription is active
  // They only expire 30 days after cancellation
  if (plan.credits_per_cycle > 0) {
    // NO expiration date for active subscriptions
    const expiresAt = null;

    // Check if this is a plan change (upgrade/downgrade) or new subscription
    const { data: currentUser } = await supabase
      .from('users')
      .select('subscription_type, credits')
      .eq('id', userId)
      .single();

    const isNewSubscription = !currentUser?.subscription_type || currentUser.subscription_type === 'free';
    const isPlanChange = currentUser?.subscription_type && currentUser.subscription_type !== planType;

    if (isNewSubscription || isPlanChange) {
      // For new subscriptions or plan changes: SET credits to new plan amount
      console.log(`🔄 ${isNewSubscription ? 'New subscription' : 'Plan change'}: Setting credits to ${plan.credits_per_cycle}`);

      // Expire all existing credits for this user
      await supabase
        .from('credit_transactions')
        .update({ is_expired: true })
        .eq('user_id', userId)
        .eq('is_expired', false);

      // Insert new credit transaction for the new plan
      const { error: creditError } = await supabase
        .from('credit_transactions')
        .insert({
          user_id: userId,
          amount: plan.credits_per_cycle,
          description: `${isNewSubscription ? 'New subscription' : 'Plan change'}: ${planType} ${billingCycle}`,
          transaction_type: 'subscription',
          expires_at: expiresAt, // null - no expiration while subscription active
          plan_id: planId,
        });

      if (creditError) {
        console.error('❌ Error setting credits:', creditError);
        throw creditError;
      }

      // Set user's total credits to the new plan amount
      await supabase
        .from('users')
        .update({
          credits: plan.credits_per_cycle,
          credits_expire_at: expiresAt, // null - no expiration while subscription active
        })
        .eq('id', userId);

      console.log(`✅ Set credits to ${plan.credits_per_cycle} for user ${userId} (${isNewSubscription ? 'new subscription' : 'plan change'})`);
    } else {
      // For renewals: ADD credits (existing behavior)
      console.log(`🔄 Subscription renewal: Adding ${plan.credits_per_cycle} credits`);

      // Insert credit transaction
      const { error: creditError } = await supabase
        .from('credit_transactions')
        .insert({
          user_id: userId,
          amount: plan.credits_per_cycle,
          description: `Subscription renewal: ${planType} ${billingCycle}`,
          transaction_type: 'subscription_renewal',
          expires_at: expiresAt, // null - no expiration while subscription active
          plan_id: planId,
        });

      if (creditError) {
        console.error('❌ Error adding renewal credits:', creditError);
        throw creditError;
      }

      // Update user's total credits by summing all non-expired transactions
      const { data: totalCredits, error: totalError } = await supabase
        .from('credit_transactions')
        .select('amount')
        .eq('user_id', userId)
        .eq('is_expired', false);

      if (!totalError) {
        const newTotal = totalCredits?.reduce((sum, t) => sum + t.amount, 0) || 0;

        await supabase
          .from('users')
          .update({
            credits: newTotal,
            credits_expire_at: expiresAt, // null - no expiration while subscription active
          })
          .eq('id', userId);
      }

      console.log(`✅ Added ${plan.credits_per_cycle} credits to user ${userId} (renewal)`);
    }
  }

  // Record subscription history
  const { error: historyError } = await supabase
    .from('subscription_history')
    .insert({
      user_id: userId,
      plan_id: planId,
      action: 'created',
      stripe_subscription_id: subscription.id,
      effective_date: new Date().toISOString(),
    });

  if (historyError) {
    console.error('❌ Error recording subscription history:', historyError);
    // Don't throw here as it's not critical
  }

  console.log(`✅ Subscription processed manually for user ${userId}: ${planId} plan`);
}

export async function getPlanIdFromPriceId(priceId: string, supabase?: any): Promise<string | null> {
  console.log(`🔍 Looking up price ID in database: ${priceId}`);

  // If supabase client not provided, create one
  if (!supabase) {
    const { createClient } = await import('@supabase/supabase-js');
    supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
  }

  try {
    // Use the database function to get plan by Stripe price ID
    const { data, error } = await supabase.rpc('get_plan_by_stripe_price_id', {
      p_price_id: priceId
    });

    if (error) {
      console.error('❌ Database error looking up price ID:', error);
      throw error;
    }

    if (data && data.length > 0) {
      const planId = data[0].plan_id;
      console.log(`✅ Found plan in database: ${priceId} → ${planId}`);
      return planId;
    }

    // Fallback: try direct query in case the function doesn't exist yet
    const { data: plans, error: queryError } = await supabase
      .from('pricing_plans')
      .select('id')
      .or(
        `stripe_price_id_monthly_test.eq.${priceId},` +
        `stripe_price_id_yearly_test.eq.${priceId},` +
        `stripe_price_id_monthly_dev.eq.${priceId},` +
        `stripe_price_id_yearly_dev.eq.${priceId},` +
        `stripe_price_id_monthly_prod.eq.${priceId},` +
        `stripe_price_id_yearly_prod.eq.${priceId}`
      )
      .eq('is_active', true)
      .limit(1);

    if (!queryError && plans && plans.length > 0) {
      const planId = plans[0].id;
      console.log(`✅ Found plan via direct query: ${priceId} → ${planId}`);
      return planId;
    }

    // If not found in database, log error and default to standard
    console.error(`❌ Unknown price ID: ${priceId}. Not found in pricing_plans table.`);
    console.error(`💡 Add this price ID to the pricing_plans table in Supabase.`);
    console.error(`🔧 UPDATE pricing_plans SET stripe_price_id_monthly_dev = '${priceId}' WHERE id = 'your_plan_id';`);
    console.log(`⚠️ Defaulting to 'standard' plan for unknown price ID`);

    return 'standard';
  } catch (err) {
    console.error('❌ Error in getPlanIdFromPriceId:', err);
    // Fallback to standard on error
    return 'standard';
  }
}

export async function handleOneTimePayment(
  supabase: any,
  userId: string,
  session: Stripe.Checkout.Session
) {
  try {
    // Handle one-time credit purchases
    const amount = session.amount_total || 0;
    const creditsToAdd = calculateCreditsFromAmount(amount);

    if (creditsToAdd > 0) {
      // Use the database function to add credits with expiration
      const { error } = await supabase.rpc('add_credits_with_expiration', {
        p_user_id: userId,
        p_amount: creditsToAdd,
        p_description: `One-time credit purchase: $${(amount / 100).toFixed(2)}`,
        p_transaction_type: 'purchase',
        p_expires_days: 15, // One-time purchases expire in 15 days (like standard plan)
      });

      if (error) {
        console.error('❌ Error adding one-time credits:', error);
        throw error;
      }

      console.log(`✅ Added ${creditsToAdd} credits to user ${userId} (expires in 30 days)`);
    }
  } catch (error) {
    console.error('❌ Error handling one-time payment:', error);
    throw error;
  }
}

export function calculateCreditsFromAmount(amountInCents: number): number {
  // Define your credit pricing
  // Example: $1 = 100 credits
  const creditsPerDollar = 100;
  const dollars = amountInCents / 100;
  return Math.floor(dollars * creditsPerDollar);
}

export async function handleFailedPayment(
  supabase: any,
  userId: string,
  subscriptionId: string | null
) {
  try {
    // Get current user data
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('subscription_type, subscription_status')
      .eq('id', userId)
      .single();

    if (userError || !user) {
      console.error('❌ User not found for failed payment handling');
      return;
    }

    // If this is the first failed payment, mark as past_due
    // If multiple failures, downgrade to free plan
    if (subscriptionId) {
      const subscription = await stripe.subscriptions.retrieve(subscriptionId);

      if (subscription.status === 'past_due') {
        // First failure - mark as past due but keep access
        await supabase
          .from('users')
          .update({
            subscription_status: 'past_due',
            updated_at: new Date().toISOString(),
          })
          .eq('id', userId);

        console.log(`⚠️ User ${userId} marked as past due`);
      } else if (subscription.status === 'unpaid') {
        // Multiple failures - downgrade to free
        await downgradeToFree(supabase, userId);
        console.log(`⬇️ User ${userId} downgraded to free due to failed payments`);
      }
    }
  } catch (error) {
    console.error('❌ Error handling failed payment:', error);
    throw error;
  }
}

export async function downgradeToFree(supabase: any, userId: string) {
  try {
    // Use the database function to update to free plan
    const { error } = await supabase.rpc('update_user_subscription', {
      p_user_id: userId,
      p_plan_id: 'free',
      p_subscription_status: 'canceled',
    });

    if (error) {
      console.error('❌ Error downgrading to free:', error);
      throw error;
    }

    // Record the downgrade in subscription history
    await supabase
      .from('subscription_history')
      .insert({
        user_id: userId,
        plan_id: 'free',
        action: 'downgraded',
        effective_date: new Date().toISOString(),
        metadata: { reason: 'failed_payment' },
      });

    console.log(`✅ User ${userId} downgraded to free plan`);
  } catch (error) {
    console.error('❌ Error in downgradeToFree:', error);
    throw error;
  }
}

export async function cancelSubscriptionInStripe(subscriptionId: string) {
  try {
    const subscription = await stripe.subscriptions.cancel(subscriptionId);
    console.log(`✅ Stripe subscription cancelled: ${subscriptionId}`);
    return subscription;
  } catch (error) {
    console.error('❌ Error cancelling Stripe subscription:', error);
    throw error;
  }
}
