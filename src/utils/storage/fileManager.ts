'use client';

// File upload configuration
export const STORAGE_CONFIG = {
  buckets: {
    uploads: 'user-uploads',
    processed: 'processed-content',
    thumbnails: 'thumbnails'
  },
  maxFileSize: {
    image: 50 * 1024 * 1024, // 50MB
    video: 500 * 1024 * 1024, // 500MB
  },
  allowedTypes: {
    image: ['image/jpeg', 'image/png', 'image/webp'],
    video: ['video/mp4', 'video/quicktime', 'video/x-msvideo', 'video/webm']
  }
};

// Generate user-specific file path
export function generateFilePath(userId: string, filename: string, type: 'original' | 'processed' | 'thumbnail'): string {
  const timestamp = Date.now();
  const sanitizedFilename = filename.replace(/[^a-zA-Z0-9.-]/g, '_');

  switch (type) {
    case 'original':
      return `${userId}/uploads/${timestamp}_${sanitizedFilename}`;
    case 'processed':
      return `${userId}/processed/${timestamp}_processed_${sanitizedFilename}`;
    case 'thumbnail':
      return `${userId}/thumbnails/${timestamp}_thumb_${sanitizedFilename}`;
    default:
      return `${userId}/misc/${timestamp}_${sanitizedFilename}`;
  }
}

// Upload file to Supabase Storage (client-side)
export async function uploadFileClient(
  file: File,
  userId: string,
  supabaseClient: any,
  type: 'original' | 'processed' | 'thumbnail' = 'original'
): Promise<{ path: string; url: string } | null> {
  try {

    // Determine bucket based on type
    let bucket: string;
    switch (type) {
      case 'original':
        bucket = STORAGE_CONFIG.buckets.uploads;
        break;
      case 'processed':
        bucket = STORAGE_CONFIG.buckets.processed;
        break;
      case 'thumbnail':
        bucket = STORAGE_CONFIG.buckets.thumbnails;
        break;
      default:
        bucket = STORAGE_CONFIG.buckets.uploads;
    }

    // Generate file path
    const filePath = generateFilePath(userId, file.name, type);

    // Upload file
    const { data, error } = await supabaseClient.storage
      .from(bucket)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (error) {
      console.error('Upload error:', error);
      return null;
    }

    // Get public URL
    const { data: urlData } = supabaseClient.storage
      .from(bucket)
      .getPublicUrl(filePath);

    return {
      path: filePath,
      url: urlData.publicUrl
    };

  } catch (error) {
    console.error('Upload file error:', error);
    return null;
  }
}

// Get file URL from storage (client-side)
export function getFileUrl(bucket: string, path: string, supabaseClient: any): string | null {
  try {
    const { data } = supabaseClient.storage
      .from(bucket)
      .getPublicUrl(path);

    return data.publicUrl;
  } catch (error) {
    console.error('Get file URL error:', error);
    return null;
  }
}

// Format file size for display
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Format date for display
export function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

// Get status color for processing status
export function getStatusColor(status: string): string {
  switch (status) {
    case 'completed': return 'text-green-400';
    case 'processing': return 'text-blue-400';
    case 'failed': return 'text-red-400';
    default: return 'text-yellow-400';
  }
}

// Validate file type and size
export function validateFile(file: File): { valid: boolean; error?: string } {
  const fileType = file.type.startsWith('image/') ? 'image' : 'video';

  // Check file type
  if (!STORAGE_CONFIG.allowedTypes[fileType].includes(file.type)) {
    return {
      valid: false,
      error: `File type ${file.type} is not supported. Allowed types: ${STORAGE_CONFIG.allowedTypes[fileType].join(', ')}`
    };
  }

  // Check file size
  if (file.size > STORAGE_CONFIG.maxFileSize[fileType]) {
    const maxSizeMB = STORAGE_CONFIG.maxFileSize[fileType] / (1024 * 1024);
    return {
      valid: false,
      error: `File size exceeds ${maxSizeMB}MB limit for ${fileType} files`
    };
  }

  return { valid: true };
}
