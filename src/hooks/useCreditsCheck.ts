'use client';

import { useEffect, useState } from 'react';
import { useUser } from '@clerk/nextjs';
import { useSupabaseClient } from '@/utils/supabase/client';

interface CreditsData {
  credits: number;
  subscriptionType: string;
  isLowCredits: boolean;
  needsUpgrade: boolean;
}

export function useCreditsCheck() {
  const { user } = useUser();
  const supabase = useSupabaseClient();
  const [creditsData, setCreditsData] = useState<CreditsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!user) {
      setIsLoading(false);
      return;
    }

    const checkCredits = async () => {
      try {
        const { data, error } = await supabase
          .from('users')
          .select('credits, subscription_type')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('Error fetching credits:', error);
          return;
        }

        if (data) {
          const credits = data.credits || 0;
          const subscriptionType = data.subscription_type || 'Free';
          const isLowCredits = credits < 20;
          const needsUpgrade = credits < 5;

          setCreditsData({
            credits,
            subscriptionType,
            isLowCredits,
            needsUpgrade,
          });
        }
      } catch (error) {
        console.error('Error in credits check:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkCredits();
  }, [user, supabase]);

  return {
    creditsData,
    isLoading,
    isLowCredits: creditsData?.isLowCredits || false,
    needsUpgrade: creditsData?.needsUpgrade || false,
    credits: creditsData?.credits || 0,
    subscriptionType: creditsData?.subscriptionType || 'Free',
  };
}
