'use client';

import { useState, useEffect, useCallback } from 'react';
import { useUser } from '@clerk/nextjs';

interface UserData {
  credits: number;
  subscriptionType: string;
  email: string;
  firstName?: string;
  lastName?: string;
  username?: string;
  lastUpdated: number;
}

interface CachedUserData extends UserData {
  isFromCache: boolean;
  isLoading: boolean;
}

const CACHE_KEY = 'guardiavision_user_data';
const CACHE_DURATION = 10 * 60 * 1000; // 10 minutes (longer cache)
const BACKGROUND_REFRESH_INTERVAL = 30 * 1000; // 30 seconds (faster background refresh)

export function useUserDataCache() {
  const { user } = useUser();
  const [userData, setUserData] = useState<CachedUserData>({
    credits: 0,
    subscriptionType: 'Free',
    email: '',
    firstName: '',
    lastName: '',
    username: '',
    lastUpdated: 0,
    isFromCache: false,
    isLoading: true
  });

  // Get cached data from localStorage
  const getCachedData = useCallback((): UserData | null => {
    if (typeof window === 'undefined') return null;
    
    try {
      const cached = localStorage.getItem(CACHE_KEY);
      if (!cached) return null;
      
      const data = JSON.parse(cached) as UserData;
      const now = Date.now();
      
      // Check if cache is still valid
      if (now - data.lastUpdated < CACHE_DURATION) {
        console.log('📦 Using cached user data:', data);
        return data;
      } else {
        console.log('⏰ Cache expired, will refresh');
        localStorage.removeItem(CACHE_KEY);
        return null;
      }
    } catch (error) {
      console.error('❌ Error reading cached user data:', error);
      localStorage.removeItem(CACHE_KEY);
      return null;
    }
  }, []);

  // Save data to cache
  const setCachedData = useCallback((data: Omit<UserData, 'lastUpdated'>) => {
    if (typeof window === 'undefined') return;
    
    try {
      const dataWithTimestamp: UserData = {
        ...data,
        lastUpdated: Date.now()
      };
      
      localStorage.setItem(CACHE_KEY, JSON.stringify(dataWithTimestamp));
      console.log('💾 Cached user data:', dataWithTimestamp);
    } catch (error) {
      console.error('❌ Error caching user data:', error);
    }
  }, []);

  // Fetch fresh data from API
  const fetchUserData = useCallback(async (silent = false) => {
    if (!user?.id) return;

    try {
      if (!silent) {
        setUserData(prev => ({ ...prev, isLoading: true }));
      }

      const response = await fetch('/api/user/data', {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache',
        },
      });

      if (response.ok) {
        const result = await response.json();
        
        const freshData = {
          credits: result.credits || 0,
          subscriptionType: result.subscriptionType || 'Free',
          email: user.emailAddresses[0]?.emailAddress || '',
          firstName: user.firstName || '',
          lastName: user.lastName || '',
          username: user.username || ''
        };

        // Update state
        setUserData({
          ...freshData,
          lastUpdated: Date.now(),
          isFromCache: false,
          isLoading: false
        });

        // Cache the data
        setCachedData(freshData);

        if (!silent) {
          console.log('✅ Fresh user data loaded:', freshData);
        }
      } else {
        console.error('❌ Failed to fetch user data:', response.status);
        if (!silent) {
          setUserData(prev => ({ ...prev, isLoading: false }));
        }
      }
    } catch (error) {
      console.error('❌ Error fetching user data:', error);
      if (!silent) {
        setUserData(prev => ({ ...prev, isLoading: false }));
      }
    }
  }, [user, setCachedData]);

  // Force refresh data
  const refreshUserData = useCallback(() => {
    console.log('🔄 Force refreshing user data...');
    fetchUserData(false);
  }, [fetchUserData]);

  // Initialize data on mount
  useEffect(() => {
    if (!user?.id) {
      setUserData(prev => ({ ...prev, isLoading: false }));
      return;
    }

    // Try to load from cache first
    const cached = getCachedData();
    
    if (cached) {
      // Show cached data immediately
      setUserData({
        ...cached,
        email: user.emailAddresses[0]?.emailAddress || cached.email,
        firstName: user.firstName || cached.firstName,
        lastName: user.lastName || cached.lastName,
        username: user.username || cached.username,
        isFromCache: true,
        isLoading: false
      });
      
      // Fetch fresh data in background
      setTimeout(() => fetchUserData(true), 100);
    } else {
      // No cache, show default values with user info from Clerk
      setUserData({
        credits: 0,
        subscriptionType: 'Free',
        email: user.emailAddresses[0]?.emailAddress || '',
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        username: user.username || '',
        lastUpdated: 0,
        isFromCache: false,
        isLoading: true
      });
      
      // Fetch fresh data
      fetchUserData(false);
    }
  }, [user, getCachedData, fetchUserData]);

  // Set up background refresh interval
  useEffect(() => {
    if (!user?.id) return;

    const interval = setInterval(() => {
      console.log('🔄 Background refresh of user data...');
      fetchUserData(true); // Silent refresh
    }, BACKGROUND_REFRESH_INTERVAL);

    return () => clearInterval(interval);
  }, [user, fetchUserData]);

  // Clear cache when user changes
  useEffect(() => {
    if (!user?.id && typeof window !== 'undefined') {
      localStorage.removeItem(CACHE_KEY);
    }
  }, [user?.id]);

  return {
    userData,
    refreshUserData,
    isLoading: userData.isLoading,
    isFromCache: userData.isFromCache
  };
}
