import { useState, useCallback } from 'react';
import { DetectionMetadata } from '@/lib/guardiavision-api';

export interface DetectionResult {
  job_id: string;
  metadata: DetectionMetadata;
  detection_count: number;
  processing_time: number;
}

/**
 * React hook for detecting objects in images and returning JSON metadata
 *
 * Unlike useGuardiaVisionProcessing, this hook:
 * - Returns immediately with JSON metadata (no polling)
 * - No processed image downloading
 * - Client-side rendering of bounding boxes/blur
 *
 * @example
 * ```tsx
 * const { detect, isDetecting, result, error } = useImageDetection();
 *
 * const handleDetect = async () => {
 *   await detect('user_123/uploads/image.jpg', 'person', 0.1);
 * };
 *
 * // result.metadata contains detection JSON for client-side rendering
 * ```
 */
export function useImageDetection() {
  const [isDetecting, setIsDetecting] = useState(false);
  const [result, setResult] = useState<DetectionResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  /**
   * Detect objects in an image
   * @param filePath - File path in format: {userId}/uploads/{filename}
   * @param textPrompt - Text prompt for detection (e.g., 'person', 'car')
   * @param scoreThreshold - Minimum confidence score (0-1)
   */
  const detect = useCallback(async (
    filePath: string,
    textPrompt: string = 'person',
    scoreThreshold: number = 0.1
  ) => {
    try {
      setIsDetecting(true);
      setError(null);
      setResult(null);

      console.log('🔍 Starting object detection...');
      console.log('📁 File path:', filePath);
      console.log('🎯 Prompt:', textPrompt);
      console.log('📊 Threshold:', scoreThreshold);

      const response = await fetch('/api/guardiavision/detect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          file_path: filePath,
          text_prompt: textPrompt,
          score_threshold: scoreThreshold,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: response.statusText }));
        throw new Error(errorData.error || `Failed to detect objects: ${response.statusText}`);
      }

      const data = await response.json();

      console.log('✅ Detection completed:', {
        job_id: data.job_id,
        detection_count: data.detection_count,
        processing_time: `${data.processing_time.toFixed(2)}s`,
      });

      setResult({
        job_id: data.job_id,
        metadata: data.metadata,
        detection_count: data.detection_count,
        processing_time: data.processing_time,
      });

      return data;

    } catch (err) {
      console.error('❌ Error detecting objects:', err);
      const errorMsg = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMsg);
      throw err;
    } finally {
      setIsDetecting(false);
    }
  }, []);

  /**
   * Reset hook state to initial values
   */
  const reset = useCallback(() => {
    setIsDetecting(false);
    setResult(null);
    setError(null);
  }, []);

  return {
    // State
    isDetecting,
    result,
    error,

    // Actions
    detect,
    reset,
  };
}
