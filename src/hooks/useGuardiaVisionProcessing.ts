import { useState, useCallback, useRef, useEffect } from 'react';

export interface ProcessingJob {
  job_id: string;
  status: 'submitted' | 'processing' | 'completed' | 'failed';
  media_type?: 'image' | 'video';
  created_at?: string;
  completed_at?: string;
  error_message?: string;
}

export interface ProcessingOptions {
  text_prompt?: string;
  score_threshold?: number;
  blur_detections?: boolean;
  blur_strength?: number;
  priority?: 'high' | 'normal' | 'low';
  visualize?: boolean;
}

/**
 * React hook for processing media files (images/videos) using the GuardiaVision backend
 *
 * Features:
 * - Submit processing jobs to RunPod backend
 * - Automatic status polling until completion
 * - Download URL retrieval for processed results
 * - Error handling and retry logic
 * - Progress callbacks
 *
 * @example
 * ```tsx
 * const { processMedia, isProcessing, currentJob, downloadUrl, error } = useGuardiaVisionProcessing();
 *
 * const handleProcess = async () => {
 *   await processMedia('user_123/uploads/image.jpg', 'image', {
 *     text_prompt: 'person',
 *     blur_detections: true,
 *     blur_strength: 35
 *   });
 * };
 *
 * // When processing completes, downloadUrl will be set
 * useEffect(() => {
 *   if (downloadUrl) {
 *     console.log('Result ready:', downloadUrl);
 *   }
 * }, [downloadUrl]);
 * ```
 */
export function useGuardiaVisionProcessing() {
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentJob, setCurrentJob] = useState<ProcessingJob | null>(null);
  const [downloadUrl, setDownloadUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Cleanup function
  const cleanup = useCallback(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  /**
   * Poll job status until completion or failure
   */
  const pollJobStatus = useCallback(async (jobId: string): Promise<void> => {
    const maxAttempts = 60; // 5 minutes with 5-second intervals
    let attempts = 0;

    return new Promise((resolve, reject) => {
      const poll = async () => {
        attempts++;

        try {
          const response = await fetch(`/api/guardiavision/status/${jobId}`);

          if (!response.ok) {
            throw new Error(`Failed to get job status: ${response.statusText}`);
          }

          const jobData: ProcessingJob = await response.json();
          setCurrentJob(jobData);

          console.log(`📊 Job ${jobId} status: ${jobData.status} (attempt ${attempts}/${maxAttempts})`);

          if (jobData.status === 'completed') {
            cleanup();

            // Fetch download URL
            console.log('✅ Job completed, fetching download URL...');
            const downloadResponse = await fetch(`/api/guardiavision/download/${jobId}`);

            if (!downloadResponse.ok) {
              throw new Error(`Failed to get download URL: ${downloadResponse.statusText}`);
            }

            const downloadData = await downloadResponse.json();

            if (downloadData.download_url) {
              setDownloadUrl(downloadData.download_url);
              setIsProcessing(false);
              console.log('🎉 Download URL ready:', downloadData.download_url);
              resolve();
            } else {
              throw new Error('No download URL in response');
            }
          } else if (jobData.status === 'failed') {
            cleanup();
            const errorMsg = jobData.error_message || 'Job failed';
            setError(errorMsg);
            setIsProcessing(false);
            reject(new Error(errorMsg));
          } else if (attempts >= maxAttempts) {
            cleanup();
            const timeoutMsg = 'Job timeout - processing took too long';
            setError(timeoutMsg);
            setIsProcessing(false);
            reject(new Error(timeoutMsg));
          }
          // Otherwise continue polling (submitted or processing status)
        } catch (err) {
          console.error('❌ Error polling job status:', err);
          cleanup();
          const errorMsg = err instanceof Error ? err.message : 'Unknown error';
          setError(errorMsg);
          setIsProcessing(false);
          reject(err);
        }
      };

      // Start polling every 5 seconds
      pollingIntervalRef.current = setInterval(poll, 5000);

      // Initial poll
      poll();
    });
  }, [cleanup]);

  /**
   * Process a media file (image or video)
   *
   * @param filePath - File path in format: {userId}/uploads/{filename}
   * @param mediaType - Type of media: 'image' or 'video'
   * @param options - Processing options (text_prompt, blur settings, etc.)
   */
  const processMedia = useCallback(async (
    filePath: string,
    mediaType: 'image' | 'video',
    options: ProcessingOptions = {}
  ) => {
    try {
      // Reset state
      setIsProcessing(true);
      setError(null);
      setDownloadUrl(null);
      setCurrentJob(null);
      cleanup();

      console.log('🚀 Starting media processing...');
      console.log('📁 File path:', filePath);
      console.log('🎬 Media type:', mediaType);
      console.log('⚙️ Options:', options);

      // Create new abort controller for this request
      abortControllerRef.current = new AbortController();

      // Submit processing job
      const response = await fetch('/api/guardiavision/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          file_path: filePath,
          media_type: mediaType,
          ...options,
        }),
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: response.statusText }));
        throw new Error(errorData.error || `Failed to submit job: ${response.statusText}`);
      }

      const jobData = await response.json();

      if (!jobData.job_id) {
        throw new Error('No job ID returned from server');
      }

      console.log('✅ Job submitted successfully:', jobData.job_id);

      setCurrentJob({
        job_id: jobData.job_id,
        status: jobData.status || 'submitted',
        media_type: mediaType,
      });

      // Start polling for status
      await pollJobStatus(jobData.job_id);

    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        console.log('⏸️ Processing cancelled by user');
        return;
      }

      console.error('❌ Error processing media:', err);
      const errorMsg = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMsg);
      setIsProcessing(false);
      cleanup();
      throw err;
    }
  }, [pollJobStatus, cleanup]);

  /**
   * Cancel current processing job
   */
  const cancel = useCallback(() => {
    console.log('🛑 Cancelling processing...');
    cleanup();
    setIsProcessing(false);
    setCurrentJob(null);
  }, [cleanup]);

  /**
   * Reset hook state to initial values
   */
  const reset = useCallback(() => {
    cleanup();
    setIsProcessing(false);
    setCurrentJob(null);
    setDownloadUrl(null);
    setError(null);
  }, [cleanup]);

  return {
    // State
    isProcessing,
    currentJob,
    downloadUrl,
    error,

    // Actions
    processMedia,
    cancel,
    reset,
  };
}
