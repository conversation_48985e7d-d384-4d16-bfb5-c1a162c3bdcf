'use client';

import { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import { useUser } from '@clerk/nextjs';
import { useSupabaseClient } from '@/utils/supabase/client';

interface UserDataContextType {
  credits: number | null;
  subscriptionType: string | null;
  email: string;
  firstName?: string;
  lastName?: string;
  username?: string;
  isLoading: boolean;
  isFromCache: boolean;
  refreshUserData: () => Promise<void>;
}

// Cache configuration - More aggressive caching for speed
const CACHE_KEY = 'guardiavision_user_data';
const CACHE_DURATION = 10 * 60 * 1000; // 10 minutes (longer cache)
const BACKGROUND_REFRESH_INTERVAL = 30 * 1000; // 30 seconds (faster background refresh)

const UserDataContext = createContext<UserDataContextType | undefined>(undefined);

export function UserDataProvider({ children }: { children: ReactNode }) {
  const { user } = useUser();
  const supabase = useSupabaseClient();

  // Initialize with smart defaults for instant display
  const [credits, setCredits] = useState<number | null>(user ? 50 : null);
  const [subscriptionType, setSubscriptionType] = useState<string | null>(user ? 'Free' : null);
  const [email, setEmail] = useState<string>(user?.emailAddresses[0]?.emailAddress || '');
  const [firstName, setFirstName] = useState<string>(user?.firstName || '');
  const [lastName, setLastName] = useState<string>(user?.lastName || '');
  const [username, setUsername] = useState<string>(user?.username || '');
  const [isLoading, setIsLoading] = useState(!user);
  const [isFromCache, setIsFromCache] = useState(false);

  // Cache utilities
  const getCachedData = useCallback(() => {
    if (typeof window === 'undefined') return null;

    try {
      const cached = localStorage.getItem(CACHE_KEY);
      if (!cached) return null;

      const data = JSON.parse(cached);
      const now = Date.now();

      // Check if cache is still valid
      if (now - data.lastUpdated < CACHE_DURATION) {
        console.log('📦 Using cached user data:', data);
        return data;
      } else {
        console.log('⏰ Cache expired, will refresh');
        localStorage.removeItem(CACHE_KEY);
        return null;
      }
    } catch (error) {
      console.error('❌ Error reading cached user data:', error);
      localStorage.removeItem(CACHE_KEY);
      return null;
    }
  }, []);

  const setCachedData = useCallback((data: any) => {
    if (typeof window === 'undefined') return;

    try {
      const dataWithTimestamp = {
        ...data,
        lastUpdated: Date.now()
      };

      localStorage.setItem(CACHE_KEY, JSON.stringify(dataWithTimestamp));
      console.log('💾 Cached user data:', dataWithTimestamp);
    } catch (error) {
      console.error('❌ Error caching user data:', error);
    }
  }, []);

  const refreshUserData = useCallback(async (silent = false) => {
    // Skip if user is undefined
    if (!user) {
      console.log('UserDataContext: user is undefined, skipping refresh');
      setIsLoading(false);
      return;
    }

    if (!silent) {
      setIsLoading(true);
      setIsFromCache(false);
    }

    try {
      console.log('Refreshing user data for ID:', user.id);

      // Use server API instead of direct Supabase call
      const response = await fetch('/api/user/data', {
        cache: 'no-store'
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch user data');
      }

      const userData = result.user;

      console.log('User data refresh result:', userData);

      // Update state with API response
      setCredits(userData.credits);
      setSubscriptionType(userData.subscriptionType);
      setEmail(userData.email);
      setFirstName(userData.firstName);
      setLastName(userData.lastName);
      setUsername(userData.username);

      // Cache the data
      setCachedData({
        credits: userData.credits,
        subscriptionType: userData.subscriptionType,
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        username: userData.username
      });

      if (!silent) {
        console.log('✅ Fresh user data loaded and cached');
      }

    } catch (err) {
      console.error('Error refreshing user data:', err);
      // Set default values in case of error
      setCredits(60);
      setSubscriptionType('Free');
      setEmail(user.emailAddresses[0]?.emailAddress || '');
      setFirstName(user.firstName || '');
      setLastName(user.lastName || '');
      setUsername(user.username || '');
    } finally {
      if (!silent) {
        setIsLoading(false);
      }
    }
  }, [user, supabase, setCachedData]);

  // Initialize data on mount with instant caching
  useEffect(() => {
    if (!user) {
      setIsLoading(false);
      return;
    }

    // Immediately update with user info from Clerk
    setEmail(user.emailAddresses[0]?.emailAddress || '');
    setFirstName(user.firstName || '');
    setLastName(user.lastName || '');
    setUsername(user.username || '');

    // Try to load from cache first
    const cached = getCachedData();

    if (cached) {
      // Show cached data immediately
      console.log('⚡ Loading cached data instantly');
      setCredits(cached.credits);
      setSubscriptionType(cached.subscriptionType);
      setIsFromCache(true);
      setIsLoading(false);

      // Fetch fresh data in background immediately
      setTimeout(() => refreshUserData(true), 10);
    } else {
      // No cache, fetch fresh data but show defaults immediately
      console.log('🔄 No cache found, showing defaults while loading');
      setCredits(5); // Show default credits immediately
      setSubscriptionType('Free'); // Show default plan immediately
      setIsLoading(false); // Don't show loading state

      // Fetch fresh data in background
      setTimeout(() => refreshUserData(true), 10);
    }
  }, [user, getCachedData, refreshUserData]);

  // Set up background refresh interval
  useEffect(() => {
    if (!user?.id) return;

    const interval = setInterval(() => {
      console.log('🔄 Background refresh of user data...');
      refreshUserData(true); // Silent refresh
    }, BACKGROUND_REFRESH_INTERVAL);

    return () => clearInterval(interval);
  }, [user, refreshUserData]);

  // Clear cache when user changes
  useEffect(() => {
    if (!user?.id && typeof window !== 'undefined') {
      localStorage.removeItem(CACHE_KEY);
    }
  }, [user?.id]);

  // Log the current state before rendering
  console.log('UserDataProvider rendering with state:', {
    credits,
    subscriptionType,
    email,
    isLoading,
    isFromCache,
    userPresent: !!user
  });

  return (
    <UserDataContext.Provider value={{
      credits,
      subscriptionType,
      email,
      firstName,
      lastName,
      username,
      isLoading,
      isFromCache,
      refreshUserData: () => refreshUserData(false)
    }}>
      {children}
    </UserDataContext.Provider>
  );
}

export function useUserData() {
  const context = useContext(UserDataContext);

  // Add debugging
  console.log('useUserData hook called, context:', context);

  if (context === undefined) {
    console.error('UserDataContext is undefined - not wrapped in provider');
    throw new Error('useUserData must be used within a UserDataProvider');
  }

  return context;
}
