/**
 * GuardiaVision API Client testing
 *
 * Type-safe client for communicating with the RunPod backend
 * Handles image and video processing with async job management
 */

// Backend configuration
const BACKEND_CONFIG = {
  RUNPOD_PROXY_URL: process.env.NEXT_PUBLIC_RUNPOD_PROXY_URL,

  getBaseUrl: () => { 
    if (BACKEND_CONFIG.RUNPOD_PROXY_URL) {
      // Remove trailing slash to prevent double slashes in URLs
      return BACKEND_CONFIG.RUNPOD_PROXY_URL.replace(/\/$/, '');
    }
    throw new Error('NEXT_PUBLIC_RUNPOD_PROXY_URL environment variable is not set');
  }
};

// Type definitions
export interface ProcessingOptions {
  text_prompt?: string;
  score_threshold?: number;
  visualize?: boolean;
  blur_detections?: boolean;
  blur_strength?: number;
  priority?: 'high' | 'normal' | 'low';
}

export interface JobResponse {
  job_id: string;
  status: 'submitted' | 'processing' | 'completed' | 'failed';
  message?: string;
}

export interface JobStatusResponse {
  job_id: string;
  status: 'submitted' | 'processing' | 'completed' | 'failed';
  created_at?: string;
  completed_at?: string;
  error_message?: string;
}

export interface DownloadResponse {
  job_id: string;
  download_url: string;
  expires_at: string;
}

export interface DetectionMetadata {
  filename: string;
  detection_all: {
    box_ids: string[];
    labels: string[];
    boxes: number[][];  // [[x1, y1, x2, y2], ...]
    scores: number[];
  };
}

export interface DetectionResponse {
  job_id: string;
  status: 'completed';
  metadata: DetectionMetadata;
  metadata_path: string;
  detection_count: number;
  processing_time: number;
}

/**
 * GuardiaVision API Client
 * Communicates with RunPod backend for AI processing
 */
export class GuardiaVisionAPI {
  private baseUrl: string;
  private authToken?: string;

  constructor(authToken?: string) {
    this.baseUrl = BACKEND_CONFIG.getBaseUrl();
    this.authToken = authToken;
  }

  /**
   * Get headers for API requests
   */
  private getHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (this.authToken) {
      headers['Authorization'] = `Bearer ${this.authToken}`;
    }

    return headers;
  }

  /**
   * Process an image with AI detection and optional blurring
   */
  async processImage(
    filePath: string,
    options: ProcessingOptions = {}
  ): Promise<JobResponse> {
    const response = await fetch(`${this.baseUrl}/process`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify({
        file_path: filePath,
        text_prompt: options.text_prompt || 'person',
        score_threshold: options.score_threshold || 0.3,
        visualize: options.visualize !== undefined ? options.visualize : false,
        blur_detections: options.blur_detections !== undefined ? options.blur_detections : false,
        blur_strength: options.blur_strength || 35,
        priority: options.priority || 'normal',
      }),
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => response.statusText);
      throw new Error(`Failed to process image: ${errorText}`);
    }

    return await response.json();
  }

  /**
   * Detect objects in an image and return JSON metadata (no processed image)
   * Returns immediately with detection results for client-side rendering
   */
  async detectImage(
    filePath: string,
    options: Omit<ProcessingOptions, 'visualize' | 'blur_detections' | 'blur_strength' | 'priority'> = {}
  ): Promise<DetectionResponse> {
    console.log('🔍 Calling backend /process-image-detection:', {
      baseUrl: this.baseUrl,
      file_path: filePath,
      text_prompt: options.text_prompt || 'person',
      score_threshold: options.score_threshold || 0.1,
    });

    const response = await fetch(`${this.baseUrl}/process-image-detection`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify({
        file_path: filePath,
        text_prompt: options.text_prompt || 'person',
        score_threshold: options.score_threshold || 0.1,
      }),
    });

    console.log('📡 Backend response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text().catch(() => response.statusText);
      console.error('❌ Backend error:', errorText);
      throw new Error(`Failed to detect objects: ${errorText}`);
    }

    const result = await response.json();
    console.log('✅ Backend response:', result);
    return result;
  }

  /**
   * Process a video with AI detection and optional blurring
   */
  async processVideo(
    filePath: string,
    options: ProcessingOptions = {}
  ): Promise<JobResponse> {
    const response = await fetch(`${this.baseUrl}/process_video`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify({
        file_path: filePath,
        text_prompt: options.text_prompt || 'person',
        score_threshold: options.score_threshold || 0.3,
        blur_detections: options.blur_detections !== undefined ? options.blur_detections : false,
        blur_strength: options.blur_strength || 35,
        priority: options.priority || 'normal',
      }),
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => response.statusText);
      throw new Error(`Failed to process video: ${errorText}`);
    }

    return await response.json();
  }

  /**
   * Get the status of a processing job
   */
  async getJobStatus(jobId: string): Promise<JobStatusResponse> {
    const response = await fetch(`${this.baseUrl}/jobs/${jobId}`, {
      method: 'GET',
      headers: this.getHeaders(),
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => response.statusText);
      throw new Error(`Failed to get job status: ${errorText}`);
    }

    return await response.json();
  }

  /**
   * Get the download URL for a completed job
   */
  async getDownloadUrl(jobId: string): Promise<DownloadResponse> {
    const response = await fetch(`${this.baseUrl}/results/${jobId}/download-url`, {
      method: 'GET',
      headers: this.getHeaders(),
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => response.statusText);
      throw new Error(`Failed to get download URL: ${errorText}`);
    }

    return await response.json();
  }

  /**
   * Check if the backend is healthy
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/health`, {
        method: 'GET',
      });
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  /**
   * Poll job status until completion or failure
   * @param jobId - Job ID to poll
   * @param onProgress - Optional callback for progress updates
   * @param maxAttempts - Maximum polling attempts (default: 60)
   * @param intervalMs - Polling interval in milliseconds (default: 5000)
   */
  async pollJobUntilComplete(
    jobId: string,
    onProgress?: (status: JobStatusResponse) => void,
    maxAttempts: number = 60,
    intervalMs: number = 5000
  ): Promise<JobStatusResponse> {
    let attempts = 0;

    while (attempts < maxAttempts) {
      attempts++;

      const status = await this.getJobStatus(jobId);

      if (onProgress) {
        onProgress(status);
      }

      if (status.status === 'completed') {
        return status;
      }

      if (status.status === 'failed') {
        throw new Error(status.error_message || 'Job failed');
      }

      // Wait before next poll
      await new Promise(resolve => setTimeout(resolve, intervalMs));
    }

    throw new Error('Job timeout - processing took too long');
  }
}
