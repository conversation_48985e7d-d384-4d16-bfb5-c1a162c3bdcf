'use client';

import { useState } from 'react';
import { ArrowLeft, Play, Settings } from 'lucide-react';

interface MediaFile {
  id: string;
  original_filename: string;
  file_path: string;
  file_type: 'image' | 'video';
  file_size: number;
  mime_type: string;
  metadata: any;
  created_at: string;
}

interface VideoStudioProps {
  mediaFile: MediaFile;
}

export function VideoStudio({ mediaFile }: VideoStudioProps) {
  const [showSettings, setShowSettings] = useState(true);

  // Format file size
  const formatFileSize = (bytes: number) => {
    return (bytes / (1024 * 1024)).toFixed(2);
  };

  return (
    <div className="flex h-full bg-gray-900 text-white">
      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => window.history.back()}
              className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Back to Dashboard</span>
            </button>
            <div className="h-6 w-px bg-gray-600" />
            <h1 className="text-lg font-semibold truncate max-w-md" title={mediaFile.original_filename}>
              {mediaFile.original_filename}
            </h1>
          </div>
        </div>

        {/* Video Display Area */}
        <div className="flex-1 flex items-center justify-center p-4 bg-gray-800">
          <div className="text-center">
            <div className="w-24 h-24 mx-auto mb-6 bg-gray-700 rounded-full flex items-center justify-center">
              <Play className="h-12 w-12 text-gray-400" />
            </div>
            <h2 className="text-2xl font-bold mb-4">Video Studio</h2>
            <p className="text-gray-400 mb-6 max-w-md">
              Video processing capabilities are coming soon! For now, you can use the image studio 
              to process individual frames or screenshots from your videos.
            </p>
            <div className="bg-gray-700 rounded-lg p-4 max-w-md mx-auto">
              <h3 className="font-medium mb-2">Current Video:</h3>
              <p className="text-sm text-gray-300 truncate">{mediaFile.original_filename}</p>
              <p className="text-sm text-gray-400 mt-1">
                Size: {formatFileSize(mediaFile.file_size)} MB
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="w-80 bg-gray-800 border-l border-gray-700 p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold">Video Settings</h2>
            <button
              onClick={() => setShowSettings(false)}
              className="text-gray-400 hover:text-white"
            >
              <Settings className="h-5 w-5" />
            </button>
          </div>

          <div className="text-center text-gray-400">
            <p className="mb-4">Video processing features will include:</p>
            <ul className="text-left space-y-2 text-sm">
              <li>• Real-time object detection</li>
              <li>• Automatic face blurring</li>
              <li>• License plate detection</li>
              <li>• Custom object tracking</li>
              <li>• Batch processing</li>
            </ul>
          </div>
        </div>
      )}

      {/* Settings Toggle (when panel is hidden) */}
      {!showSettings && (
        <button
          onClick={() => setShowSettings(true)}
          className="fixed top-20 right-4 p-3 bg-gray-800 hover:bg-gray-700 rounded-lg shadow-lg transition-colors z-10"
        >
          <Settings className="h-5 w-5" />
        </button>
      )}
    </div>
  );
}