'use client';

import { useState, useRef, useEffect } from 'react';
import { ArrowLeft, Play, Pause, SkipForward, SkipBack, Volume2, VolumeX, Download, Plus, Eye, EyeOff, Trash2, LogOut, Settings, LayoutGrid, Target, Loader2, Zap } from 'lucide-react';
import { useUser, useClerk } from '@clerk/nextjs';
import { useSupabaseClient } from '@/utils/supabase/client';
import { useImageDetection } from '@/hooks/useImageDetection';

// Types
interface DetectedObject {
  id: number;
  label: string;
  confidence: number;
  selected: boolean;
  color: string;
  bbox?: [number, number, number, number];
  frame: number; // Frame number where detection occurred
}

interface MediaFile {
  id: string | null;
  original_filename: string;
  file_path: string;
  file_type: string;
  file_size: number;
  mime_type?: string;
  metadata?: { originalUrl: string };
  created_at: string;
}

interface CustomBlurArea {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  visible: boolean;
  frame: number; // Frame number where area was created
}

interface VideoStudioProps {
  mediaFile: MediaFile;
  originalVideoUrl: string;
  onBack: () => void;
}

export default function GuardiaVisionVideoStudio({ mediaFile, originalVideoUrl, onBack }: VideoStudioProps) {
  const { user } = useUser();
  const { signOut } = useClerk();
  const supabase = useSupabaseClient();
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const {
    detect,
    isDetecting,
    result: detectionResult,
    error: detectionError,
  } = useImageDetection();

  // UI State
  const [showUserDropdown, setShowUserDropdown] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [apiStatus, setApiStatus] = useState<'checking' | 'online' | 'offline'>('online');

  // Video Player State
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [playbackSpeed, setPlaybackSpeed] = useState(1);

  // Detection State
  const [textPrompts, setTextPrompts] = useState<string[]>(['']);
  const [hasUserTyped, setHasUserTyped] = useState(false);
  const [detectedObjects, setDetectedObjects] = useState<DetectedObject[]>([]);
  const [allDetectedObjects, setAllDetectedObjects] = useState<DetectedObject[]>([]);
  const [customBlurAreas, setCustomBlurAreas] = useState<CustomBlurArea[]>([]);
  const [confidenceThreshold, setConfidenceThreshold] = useState(0.2);

  // Detection Categories
  const [detectionCategories, setDetectionCategories] = useState({
    fullScreenBlur: false,
    customBlur: false,
    enableAIDetection: true,
    fullBody: false,
    face: true,
    vehicle: false,
    licensePlate: false,
  });

  // Blur Controls (0-100 professional scale)
  const [blurIntensity, setBlurIntensity] = useState(50);
  const [pendingBlurIntensity, setPendingBlurIntensity] = useState(50);
  const [blurType, setBlurType] = useState<'blur' | 'pixelate'>('blur');

  // Frame-specific state
  const [currentFrame, setCurrentFrame] = useState(0);
  const [processedFrames, setProcessedFrames] = useState<Map<number, string>>(new Map());
  const [isDrawingCustomArea, setIsDrawingCustomArea] = useState(false);

  // Tooltip Component
  const Tooltip = ({ text }: { text: string }) => (
    <div className="relative group inline-block ml-1">
      <div className="w-3.5 h-3.5 rounded-full bg-gray-600/50 hover:bg-gray-500/70 flex items-center justify-center cursor-help transition-all duration-200">
        <span className="text-[10px] text-gray-300 font-semibold">?</span>
      </div>
      <div className="absolute left-1/2 -translate-x-1/2 bottom-full mb-2 hidden group-hover:block z-50 w-48">
        <div className="bg-gray-900/95 backdrop-blur-sm text-white text-xs px-3 py-2 rounded-lg shadow-2xl border border-gray-700/50">
          {text}
          <div className="absolute left-1/2 -translate-x-1/2 top-full w-0 h-0 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-gray-900/95"></div>
        </div>
      </div>
    </div>
  );

  // Get user initials
  const getUserInitials = () => {
    if (!user) return 'U';
    const firstName = user.firstName || '';
    const lastName = user.lastName || '';
    if (firstName && lastName) {
      return `${firstName[0]}${lastName[0]}`.toUpperCase();
    }
    return (firstName[0] || '').toUpperCase() || 'U';
  };

  // Video Controls
  const handlePlayPause = () => {
    if (!videoRef.current) return;
    if (isPlaying) {
      videoRef.current.pause();
    } else {
      videoRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleTimeUpdate = () => {
    if (!videoRef.current) return;
    setCurrentTime(videoRef.current.currentTime);
    // Calculate current frame assuming 30fps
    setCurrentFrame(Math.floor(videoRef.current.currentTime * 30));
  };

  const handleSeek = (time: number) => {
    if (!videoRef.current) return;
    videoRef.current.currentTime = time;
    setCurrentTime(time);
  };

  const handleVolumeChange = (newVolume: number) => {
    if (!videoRef.current) return;
    videoRef.current.volume = newVolume;
    setVolume(newVolume);
    setIsMuted(newVolume === 0);
  };

  const toggleMute = () => {
    if (!videoRef.current) return;
    const newMuted = !isMuted;
    videoRef.current.muted = newMuted;
    setIsMuted(newMuted);
  };

  const skipForward = () => {
    if (!videoRef.current) return;
    videoRef.current.currentTime = Math.min(videoRef.current.duration, videoRef.current.currentTime + 5);
  };

  const skipBackward = () => {
    if (!videoRef.current) return;
    videoRef.current.currentTime = Math.max(0, videoRef.current.currentTime - 5);
  };

  // Format time for display
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Detect current frame
  const detectCurrentFrame = async () => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Draw current frame to canvas
    ctx.drawImage(video, 0, 0);

    // Convert to blob and detect
    canvas.toBlob(async (blob) => {
      if (!blob) return;

      setIsProcessing(true);
      try {
        // Use detection API (similar to image studio)
        const prompts = textPrompts.filter(p => p.trim()).join(' ');
        if (!prompts) {
          setError('Please enter at least one detection prompt');
          return;
        }

        // TODO: Implement frame-based detection
        console.log('Detecting frame:', currentFrame, 'with prompt:', prompts);
        setError('Video detection coming soon - processing frame ' + currentFrame);
      } catch (err) {
        console.error('Detection error:', err);
        setError('Detection failed');
      } finally {
        setIsProcessing(false);
      }
    }, 'image/jpeg', 0.95);
  };

  // Process and export video
  const exportProcessedVideo = async () => {
    setError('Video export functionality coming soon');
  };

  return (
    <div className="min-h-screen bg-[#0a192f] text-white font-sans antialiased">
      {/* Header */}
      <div className="bg-[#0a192f]/95 backdrop-blur-xl border-b border-[#1e3a6f]/50 px-6 lg:px-8 py-4 sticky top-0 z-50">
        <div className="relative flex items-center max-w-[2000px] mx-auto">
          <button
            onClick={onBack}
            className="group flex items-center gap-2 text-gray-400 hover:text-white transition-all duration-200 px-3 py-2 rounded-lg hover:bg-white/5 active:scale-95"
          >
            <ArrowLeft className="h-4 w-4 transition-transform group-hover:-translate-x-0.5" />
            <span className="text-sm font-medium hidden sm:inline tracking-tight">Dashboard</span>
          </button>

          <div className="absolute left-1/2 -translate-x-1/2 flex items-center gap-3">
            <img src="/photo-de-profil-linkedin.png" alt="Logo" className="h-9 w-auto opacity-95" />
            <h1 className="text-lg sm:text-2xl font-semibold text-white tracking-tight">
              <span className="hidden sm:inline">Guardia<span className="text-emerald-400">Vision</span> <span className="font-light text-gray-400">Video Studio</span></span>
              <span className="sm:hidden font-light">Video Studio</span>
            </h1>
          </div>

          <div className="ml-auto relative" ref={dropdownRef}>
            <button
              onClick={() => setShowUserDropdown(!showUserDropdown)}
              className="w-9 h-9 bg-gradient-to-br from-purple-500 to-purple-700 rounded-full flex items-center justify-center text-white font-semibold text-sm hover:shadow-lg hover:shadow-purple-500/50 transition-all duration-200 active:scale-95 ring-2 ring-purple-500/20"
            >
              {getUserInitials()}
            </button>

            {showUserDropdown && (
              <div className="absolute right-0 mt-3 w-56 bg-[#0f1629]/95 backdrop-blur-xl border border-[#1e3a6f]/50 rounded-xl shadow-2xl shadow-black/50 z-50 overflow-hidden">
                <div className="py-2">
                  <button
                    onClick={() => window.location.href = '/dashboard'}
                    className="flex items-center w-full px-4 py-2.5 text-sm font-medium text-gray-300 hover:text-white hover:bg-white/5 transition-all duration-150"
                  >
                    <LayoutGrid className="h-4 w-4 mr-3 opacity-70" />
                    Workspace
                  </button>
                  <button
                    onClick={() => window.location.href = '/settings'}
                    className="flex items-center w-full px-4 py-2.5 text-sm font-medium text-gray-300 hover:text-white hover:bg-white/5 transition-all duration-150"
                  >
                    <Settings className="h-4 w-4 mr-3 opacity-70" />
                    Account Settings
                  </button>
                  <div className="border-t border-[#1e3a6f]/50 my-2"></div>
                  <button
                    onClick={() => signOut()}
                    className="flex items-center w-full px-4 py-2.5 text-sm font-medium text-red-400 hover:text-red-300 hover:bg-red-500/10 transition-all duration-150"
                  >
                    <LogOut className="h-4 w-4 mr-3 opacity-70" />
                    Sign Out
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Layout */}
      <div className="flex flex-col lg:flex-row h-[calc(100vh-73px)]">
        {/* Left Sidebar - Controls */}
        <div className="w-full lg:w-[340px] bg-[#0f1629]/50 backdrop-blur-sm border-b lg:border-b-0 lg:border-r border-[#1e3a6f]/40 flex flex-col max-h-[40vh] lg:max-h-none overflow-y-auto">
          <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-700/50 scrollbar-track-transparent">

            {/* Detection Prompts */}
            <div className="p-3 border-b border-[#1e3a6f]">
              <label className="text-white text-sm font-medium flex items-center mb-3">
                <span className="text-purple-400 mr-2">●</span>
                Detection Prompts
                <Tooltip text="Describe what you want to detect in the video" />
              </label>
              {textPrompts.map((prompt, index) => (
                <div key={index} className="relative mb-2">
                  <textarea
                    value={prompt}
                    onChange={(e) => {
                      const newPrompts = [...textPrompts];
                      newPrompts[index] = e.target.value;
                      setTextPrompts(newPrompts);
                      setHasUserTyped(true);
                    }}
                    placeholder="e.g., person, face, car"
                    className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all resize-none"
                    rows={2}
                  />
                </div>
              ))}
            </div>

            {/* Detection Categories */}
            <div className="p-3 border-b border-[#1e3a6f]">
              <label className="text-white text-sm font-medium flex items-center mb-3">
                Detection Options
                <Tooltip text="Choose what to detect in video frames" />
              </label>
              <div className="space-y-2">
                {Object.entries({
                  face: 'Faces',
                  fullBody: 'Full Body',
                  vehicle: 'Vehicles',
                  licensePlate: 'License Plates',
                }).map(([key, label]) => (
                  <label key={key} className="flex items-center justify-between cursor-pointer">
                    <span className="text-sm text-gray-300">{label}</span>
                    <input
                      type="checkbox"
                      checked={detectionCategories[key as keyof typeof detectionCategories]}
                      onChange={(e) => setDetectionCategories(prev => ({ ...prev, [key]: e.target.checked }))}
                      className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500"
                    />
                  </label>
                ))}
              </div>
            </div>

            {/* Detect Current Frame Button */}
            <div className="p-3 border-b border-[#1e3a6f]">
              <button
                onClick={detectCurrentFrame}
                disabled={isProcessing || !hasUserTyped}
                className={`w-full flex items-center justify-center gap-2 px-4 py-3 rounded-lg font-medium transition-all ${
                  isProcessing || !hasUserTyped
                    ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                    : 'bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-lg shadow-purple-500/30'
                }`}
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="h-5 w-5 animate-spin" />
                    Detecting...
                  </>
                ) : (
                  <>
                    <Target className="h-5 w-5" />
                    Detect Current Frame
                  </>
                )}
              </button>
            </div>

            {/* Blur Intensity */}
            <div className="p-3 border-b border-[#1e3a6f]">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  <label className="text-white text-sm font-medium">
                    Blur Intensity
                  </label>
                  <Tooltip text="0 = Clear video, 100 = Maximum blur" />
                </div>
                <input
                  type="number"
                  min="0"
                  max="100"
                  value={pendingBlurIntensity}
                  onChange={(e) => {
                    const val = Math.max(0, Math.min(100, Number(e.target.value) || 0));
                    setPendingBlurIntensity(val);
                    setBlurIntensity(val);
                  }}
                  className="w-14 px-2 py-1 bg-gray-800/50 border border-gray-600/50 rounded text-white text-xs text-center focus:outline-none focus:ring-1 focus:ring-purple-500 focus:border-purple-500 transition-all"
                />
              </div>
              <input
                type="range"
                min="0"
                max="100"
                value={pendingBlurIntensity}
                onChange={(e) => {
                  const val = Number(e.target.value);
                  setPendingBlurIntensity(val);
                  setBlurIntensity(val);
                }}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                style={{
                  background: `linear-gradient(to right, #8B5CF6 0%, #8B5CF6 ${pendingBlurIntensity}%, #374151 ${pendingBlurIntensity}%, #374151 100%)`
                }}
              />
              <div className="flex justify-between text-[10px] text-gray-500 font-medium mt-2">
                <span>Clear (0)</span>
                <span>Maximum (100)</span>
              </div>
            </div>

            {/* Blur/Pixelate Toggle */}
            <div className="p-3 border-b border-[#1e3a6f]">
              <label className="text-white text-sm font-medium flex items-center mb-3">
                Effect Type
                <Tooltip text="Choose between blur or pixelation effect" />
              </label>
              <div className="flex gap-2">
                <button
                  onClick={() => setBlurType('blur')}
                  className={`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    blurType === 'blur'
                      ? 'bg-purple-600 text-white'
                      : 'bg-gray-700/50 text-gray-300 hover:bg-gray-700'
                  }`}
                >
                  Blur
                </button>
                <button
                  onClick={() => setBlurType('pixelate')}
                  className={`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    blurType === 'pixelate'
                      ? 'bg-purple-600 text-white'
                      : 'bg-gray-700/50 text-gray-300 hover:bg-gray-700'
                  }`}
                >
                  Pixelate
                </button>
              </div>
            </div>

            {/* Export Video */}
            <div className="p-3 border-b border-[#1e3a6f]">
              <button
                onClick={exportProcessedVideo}
                disabled={isProcessing}
                className="w-full flex items-center justify-center gap-2 px-4 py-3 rounded-lg font-medium transition-all bg-emerald-600 hover:bg-emerald-700 text-white shadow-lg shadow-emerald-500/30"
              >
                <Download className="h-5 w-5" />
                Export Processed Video
              </button>
            </div>

            {/* API Status */}
            <div className="p-3 border-b border-[#1e3a6f]/40 bg-gradient-to-r from-transparent to-[#1e3a6f]/10">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2.5">
                  <div className="relative">
                    <div className={`w-2 h-2 rounded-full ${
                      apiStatus === 'online' ? 'bg-emerald-400 shadow-lg shadow-emerald-500/50' :
                      apiStatus === 'offline' ? 'bg-red-400 shadow-lg shadow-red-500/50' :
                      'bg-amber-400 animate-pulse shadow-lg shadow-amber-500/50'
                    }`} />
                    {apiStatus === 'online' && (
                      <div className="absolute inset-0 w-2 h-2 rounded-full bg-emerald-400 animate-ping opacity-75" />
                    )}
                  </div>
                  <div className="flex items-center gap-1">
                    <span className="text-sm font-medium text-gray-300">
                      {apiStatus === 'online' ? 'AI Online' : apiStatus === 'offline' ? 'AI Offline' : 'Checking AI...'}
                    </span>
                    <Tooltip text="Real-time AI detection service availability" />
                  </div>
                </div>
              </div>
            </div>

            {/* Error Display */}
            {error && (
              <div className="p-3 bg-red-500/10 border-l-4 border-red-500">
                <p className="text-sm text-red-400">{error}</p>
              </div>
            )}
          </div>
        </div>

        {/* Main Video Area */}
        <div className="flex-1 bg-[#0f1629] relative flex flex-col">
          {/* Video Container */}
          <div ref={containerRef} className="flex-1 flex items-center justify-center p-4 relative">
            <video
              ref={videoRef}
              src={originalVideoUrl}
              onTimeUpdate={handleTimeUpdate}
              onLoadedMetadata={(e) => setDuration(e.currentTarget.duration)}
              onEnded={() => setIsPlaying(false)}
              className="max-w-full max-h-full rounded-lg shadow-2xl"
              onClick={handlePlayPause}
            />
            <canvas ref={canvasRef} className="hidden" />

            {/* Play/Pause Overlay */}
            {!isPlaying && (
              <button
                onClick={handlePlayPause}
                className="absolute inset-0 flex items-center justify-center bg-black/30 hover:bg-black/40 transition-colors"
              >
                <div className="w-20 h-20 rounded-full bg-white/90 hover:bg-white flex items-center justify-center shadow-2xl">
                  <Play className="h-10 w-10 text-purple-600 ml-1" />
                </div>
              </button>
            )}
          </div>

          {/* Video Controls */}
          <div className="bg-[#0a192f]/95 backdrop-blur-xl border-t border-[#1e3a6f]/50 p-4">
            {/* Timeline */}
            <div className="mb-4">
              <input
                type="range"
                min="0"
                max={duration || 0}
                step="0.01"
                value={currentTime}
                onChange={(e) => handleSeek(Number(e.target.value))}
                className="w-full h-1.5 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                style={{
                  background: `linear-gradient(to right, #8B5CF6 0%, #8B5CF6 ${(currentTime / duration) * 100}%, #374151 ${(currentTime / duration) * 100}%, #374151 100%)`
                }}
              />
              <div className="flex justify-between text-xs text-gray-400 mt-1">
                <span>{formatTime(currentTime)}</span>
                <span>Frame: {currentFrame}</span>
                <span>{formatTime(duration)}</span>
              </div>
            </div>

            {/* Control Buttons */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <button
                  onClick={skipBackward}
                  className="p-2 rounded-lg hover:bg-white/10 transition-colors"
                >
                  <SkipBack className="h-5 w-5 text-gray-300" />
                </button>
                <button
                  onClick={handlePlayPause}
                  className="p-3 rounded-lg bg-purple-600 hover:bg-purple-700 transition-colors"
                >
                  {isPlaying ? (
                    <Pause className="h-6 w-6 text-white" />
                  ) : (
                    <Play className="h-6 w-6 text-white ml-0.5" />
                  )}
                </button>
                <button
                  onClick={skipForward}
                  className="p-2 rounded-lg hover:bg-white/10 transition-colors"
                >
                  <SkipForward className="h-5 w-5 text-gray-300" />
                </button>
              </div>

              {/* Volume Control */}
              <div className="flex items-center gap-3">
                <button onClick={toggleMute} className="p-2 rounded-lg hover:bg-white/10 transition-colors">
                  {isMuted || volume === 0 ? (
                    <VolumeX className="h-5 w-5 text-gray-300" />
                  ) : (
                    <Volume2 className="h-5 w-5 text-gray-300" />
                  )}
                </button>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.01"
                  value={volume}
                  onChange={(e) => handleVolumeChange(Number(e.target.value))}
                  className="w-24 h-1.5 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                />
                <span className="text-xs text-gray-400 w-8">{Math.round(volume * 100)}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
