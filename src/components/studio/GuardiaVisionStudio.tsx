'use client';

import { useState, useRef, useEffect } from 'react';
import { ArrowLeft, Zap, Plus, LogOut, Settings, LayoutGrid, Eye, EyeOff, Trash2, Target, Loader2, ChevronLeft, ChevronRight } from 'lucide-react';
import { useUser, useClerk } from '@clerk/nextjs';
import { useSupabaseClient } from '@/utils/supabase/client';
import { InsufficientCreditsModal } from './InsufficientCreditsModal';
import GuardiaVisionVideoStudio from './GuardiaVisionVideoStudio';
import { useImageDetection } from '@/hooks/useImageDetection';

// API Configuration - RunPod Backend (NEW)
const USE_RUNPOD_BACKEND = true; // Set to false to use old Google Cloud Run endpoints

// Old Google Cloud Run endpoints (DEPRECATED - kept for fallback)
const API_BASE_URL = 'https://guardiavision-prod-473899915275.europe-west1.run.app';
const FALLBACK_API_URL = 'https://guardiavision-prod-gpu-473899915275.europe-west1.run.app';

interface DetectedObject {
  id: number;
  label: string;
  confidence: number;
  selected: boolean;
  color: string;
  bbox?: [number, number, number, number];
  prompt?: string; // Store which prompt detected this object
}

interface MediaFile {
  id: string | null;
  original_filename: string;
  file_path: string;
  file_type: string;
  file_size: number;
  mime_type?: string;
  metadata?: {
    originalUrl: string;
  };
  created_at: string;
  upload_date?: string;
  user_id?: string;
}

interface GuardiaVisionStudioProps {
  mediaFile: MediaFile;
  originalImageUrl: string;
  onBack: () => void;
}

export default function GuardiaVisionStudio({ mediaFile, originalImageUrl, onBack }: GuardiaVisionStudioProps) {
  // Check if this is a video file
  const isVideoFile = mediaFile.file_type?.includes('video') || mediaFile.mime_type?.startsWith('video/');

  // If it's a video file, render the video studio instead
  if (isVideoFile) {
    return <GuardiaVisionVideoStudio mediaFile={mediaFile} originalVideoUrl={originalImageUrl} onBack={onBack} />;
  }
  // User authentication and data
  const { user } = useUser();
  const { signOut } = useClerk();
  const supabase = useSupabaseClient();

  // RunPod Backend Processing Hook (NEW)
  const {
    detect,
    isDetecting: isDetecting,
    result: detectionResult,
    error: detectionError,
    reset: resetDetection,
  } = useImageDetection();

  // UI state
  const [showUserDropdown, setShowUserDropdown] = useState(false);
  const [showSaveDialog, setShowSaveDialog] = useState(false);

  // Multi-image navigation state
  const [allUserImages, setAllUserImages] = useState<MediaFile[]>([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [loadingImages, setLoadingImages] = useState(true);

  // Draggable panel state
  const [panelPosition, setPanelPosition] = useState({ x: 0, y: 0 }); // Start at bottom center
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

  // Image panning state (for actual size mode)
  const [isImagePanning, setIsImagePanning] = useState(false);
  const [imagePanStart, setImagePanStart] = useState({ x: 0, y: 0 });
  const imageContainerRef = useRef<HTMLDivElement>(null);

  // Image processing states
  const [imagePreview, setImagePreview] = useState<string>('');
  const [processedImageUrl, setProcessedImageUrl] = useState<string>('');
  const [detectedObjects, setDetectedObjects] = useState<DetectedObject[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [imageLoadError, setImageLoadError] = useState<string | null>(null);
  const [isLoadingImage, setIsLoadingImage] = useState(true);

  // Insufficient credits modal state
  const [showInsufficientCreditsModal, setShowInsufficientCreditsModal] = useState(false);
  const [insufficientCreditsData, setInsufficientCreditsData] = useState({ current: 0, required: 3 });


  // Detection categories (matching the screenshot)
  const [detectionCategories, setDetectionCategories] = useState({
    fullScreenBlur: false,
    customBlur: false,
    enableAIDetection: true,
    fullBody: false,
    face: true,
    vehicle: false,
    licensePlate: false
  });

  // Blur controls
  const [blurIntensity, setBlurIntensity] = useState(50); // Higher default for more visible effects
  const [pendingBlurIntensity, setPendingBlurIntensity] = useState(50); // For debounced intensity
  const [blurType, setBlurType] = useState<'blur' | 'pixelate'>('blur');
  const [customBlurAreas, setCustomBlurAreas] = useState<Array<{id: string, x: number, y: number, width: number, height: number, visible: boolean}>>([]);
  const [shouldAutoBlur, setShouldAutoBlur] = useState(false); // BUG FIX: Control auto-blur after detection
  const [textPrompts, setTextPrompts] = useState<string[]>(['']); // Support multiple prompts
  const [hasUserTyped, setHasUserTyped] = useState<boolean>(false); // Track if user has typed
  const [intensityDebounceTimer, setIntensityDebounceTimer] = useState<number | null>(null);

  // Confidence filtering
  const [confidenceThreshold, setConfidenceThreshold] = useState<number>(0.2); // Default 20%
  const [allDetectedObjects, setAllDetectedObjects] = useState<DetectedObject[]>([]); // Store all objects from API
  const [lastUsedPrompt, setLastUsedPrompt] = useState<string>(''); // Store the last prompt used for detection
  const [apiStatus, setApiStatus] = useState<'checking' | 'online' | 'offline'>('checking');

  // Image display controls
  const [imageZoomMode, setImageZoomMode] = useState<'fit' | 'actual'>('fit'); // Default to fit-to-container


  const imageRef = useRef<HTMLImageElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const autoSaveCounterRef = useRef<number>(0); // Track auto-save calls for deterministic DB saves

  // Fetch all user images for navigation
  const fetchAllUserImages = async () => {
    if (!user?.id) return;

    try {
      setLoadingImages(true);
      const response = await fetch('/api/user/files?type=image&limit=100');
      const result = await response.json();

      if (result.success && Array.isArray(result.files)) {
        // Filter out GIFs since they're handled as videos now
        const imageFiles = result.files.filter((file: any) =>
          file.file_type === 'image' &&
          !file.mime_type?.includes('gif') &&
          !file.original_filename?.toLowerCase().endsWith('.gif')
        );

        setAllUserImages(imageFiles);

        // Find current image index
        const currentIndex = imageFiles.findIndex((img: any) => img.id === mediaFile.id);
        setCurrentImageIndex(currentIndex >= 0 ? currentIndex : 0);
      }
    } catch (error) {
      console.error('Failed to fetch user images:', error);
    } finally {
      setLoadingImages(false);
    }
  };

  // Navigate to different image
  const navigateToImage = async (imageFile: MediaFile) => {
    if (imageFile.id === mediaFile.id) return; // Already viewing this image

    try {
      // Clear current state first
      setIsLoadingImage(true);
      setImageLoadError(null);
      setError(null);

      // Update URL without reload
      const newUrl = `/studio?file=${imageFile.id}`;
      window.history.pushState({}, '', newUrl);

      // Update current image data
      const currentIndex = allUserImages.findIndex(img => img.id === imageFile.id);
      setCurrentImageIndex(currentIndex >= 0 ? currentIndex : 0);

      // Load new image - use 'url' field which contains the signed URL from Supabase
      let imageUrl = '';
      if ((imageFile as any).url) {
        // Priority 1: Use the signed URL from the API response
        imageUrl = (imageFile as any).url;
        console.log('✅ Using signed URL for navigation:', imageUrl);
      } else if (imageFile.metadata?.originalUrl) {
        // Priority 2: Use metadata originalUrl
        imageUrl = imageFile.metadata.originalUrl;
        console.log('✅ Using metadata URL for navigation:', imageUrl);
      } else {
        // Priority 3: Fetch secure URL from API
        try {
          const response = await fetch(`/api/files/${imageFile.id}`, { cache: 'no-store' });
          if (response.ok) {
            const data = await response.json();
            if (data.success && data.file?.secureUrl) {
              imageUrl = data.file.secureUrl;
              console.log('✅ Got secure URL from API for navigation:', imageUrl);
            } else {
              throw new Error('No secure URL in API response');
            }
          } else {
            throw new Error('Failed to fetch secure URL');
          }
        } catch (apiError) {
          console.error('❌ Failed to load secure URL:', apiError);
          setError('Unable to load image');
          setIsLoadingImage(false);
          return;
        }
      }

      setImagePreview(imageUrl);

      // Clear previous processing state
      setProcessedImageUrl('');
      setDetectedObjects([]);
      setAllDetectedObjects([]);

      // Load saved state for new image
      loadSavedState();

    } catch (error) {
      console.error('Navigation error:', error);
      setError('Failed to load image');
    }
  };

  // Handle dragging for image navigation panel
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent text selection
    e.stopPropagation(); // Prevent event bubbling
    
    setIsDragging(true);
    setDragOffset({
      x: e.clientX - panelPosition.x,
      y: e.clientY - panelPosition.y
    });
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging) return;
    
    e.preventDefault(); // Prevent any default behavior
    
    setPanelPosition({
      x: e.clientX - dragOffset.x,
      y: e.clientY - dragOffset.y
    });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Add mouse event listeners for dragging
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, dragOffset]);

  // Handle file upload
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      console.log('📁 File uploaded:', file.name, file.size, 'bytes');

      // Convert to base64 for preview
      const reader = new FileReader();
      reader.onload = () => {
        const base64String = reader.result as string;
        setImagePreview(base64String);
        setImageLoadError(null);
        setIsLoadingImage(false);
        console.log('✅ File converted to base64 for preview');
      };
      reader.readAsDataURL(file);
    } else {
      setError('Please select a valid image file (JPG, PNG, GIF, WebP)');
    }
  };

  // User dropdown functions
  const handleWorkspaceClick = () => {
    window.location.href = '/dashboard';
  };

  const handleAccountSettings = () => {
    window.location.href = '/dashboard/user-profile';
  };

  const handleSignOut = async () => {
    await signOut();
    window.location.href = '/';
  };

  // Get user initials for avatar
  const getUserInitials = () => {
    if (!user) return 'U';

    const firstName = user.firstName || '';
    const lastName = user.lastName || '';

    if (firstName && lastName && firstName.length > 0 && lastName.length > 0) {
      return `${firstName[0] || ''}${lastName[0] || ''}`.toUpperCase();
    } else if (firstName && firstName.length > 0) {
      return (firstName[0] || '').toUpperCase() || 'U';
    } else if (user.emailAddresses?.[0]?.emailAddress) {
      const email = user.emailAddresses[0].emailAddress;
      return email && email.length > 0 ? (email[0] || '').toUpperCase() || 'U' : 'U';
    }

    return 'U';
  };

  // Save/Download functions
  const handleDoneClick = () => {
    setShowSaveDialog(true);
  };

  const handleDownloadOnly = () => {
    if (processedImageUrl) {
      // Create download link
      const link = document.createElement('a');
      link.href = processedImageUrl;
      link.download = `processed_${mediaFile.original_filename}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
    setShowSaveDialog(false);
  };

  const handleSaveAndDownload = async () => {
    try {
      // First download the image
      handleDownloadOnly();

      // Convert blob URL to base64 for persistent storage
      let processedImageBase64 = '';
      if (processedImageUrl && processedImageUrl.startsWith('blob:')) {
        try {
          const response = await fetch(processedImageUrl);
          const blob = await response.blob();
          const reader = new FileReader();

          processedImageBase64 = await new Promise((resolve) => {
            reader.onload = () => resolve(reader.result as string);
            reader.readAsDataURL(blob);
          });

          console.log('✅ Converted processed image to base64 for storage');
        } catch (error) {
          console.error('❌ Failed to convert processed image to base64:', error);
          processedImageBase64 = processedImageUrl; // Fallback to original URL
        }
      } else {
        processedImageBase64 = processedImageUrl;
      }

      // Get all unique prompts used
      const uniquePrompts = [...new Set(allDetectedObjects.map(obj => obj.prompt).filter(Boolean))];

      // Save comprehensive processing state with full history
      const saveData = {
        original_file_id: mediaFile.id,
        processed_image_url: processedImageBase64,
        processing_settings: {
          // Current settings
          blur_intensity: blurIntensity,
          blur_type: blurType,
          confidence_threshold: confidenceThreshold,

          // All detection history
          all_detected_objects: allDetectedObjects, // Complete detection history
          filtered_detected_objects: detectedObjects, // Currently visible objects
          unique_prompts_used: uniquePrompts, // All prompts used
          last_used_prompt: lastUsedPrompt,

          // Legacy fields for compatibility
          custom_blur_areas: customBlurAreas,
          detection_categories: detectionCategories,

          // Processing history metadata
          total_detections: allDetectedObjects.length,
          visible_detections: detectedObjects.length,
          processing_timestamp: new Date().toISOString()
        },
        file_name: `processed_${mediaFile.original_filename}`,
        created_at: new Date().toISOString()
      };

      console.log('💾 Saving comprehensive processing state:', {
        totalObjects: allDetectedObjects.length,
        visibleObjects: detectedObjects.length,
        uniquePrompts: uniquePrompts.length,
        settings: {
          blurIntensity,
          blurType,
          confidenceThreshold
        }
      });

      // Save to database
      const response = await fetch('/api/studio/save-processing', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(saveData)
      });

      if (response.ok) {
        console.log('✅ Comprehensive processing state saved successfully');
      } else {
        console.error('❌ Failed to save processing state');
      }

      // Mark file as completed after successful save
      await markFileAsCompleted();
    } catch (error) {
      console.error('❌ Error saving processing state:', error);
    }

    setShowSaveDialog(false);
  };

  // Mark file as completed when saved from studio
  const markFileAsCompleted = async () => {
    if (!mediaFile.id) return;

    try {
      console.log(`✅ Marking file ${mediaFile.id} as completed...`);

      const response = await fetch('/api/user/files/update-status', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fileId: mediaFile.id,
          status: 'completed'
        })
      });

      if (response.ok) {
        console.log('✅ File marked as completed successfully');
      } else {
        console.error('❌ Failed to mark file as completed:', await response.text());
      }
    } catch (error) {
      console.error('❌ Error marking file as completed:', error);
    }
  };



  // Detected objects management
  const toggleObjectVisibility = (objectId: number) => {
    console.log('👁️ Toggling visibility for object:', objectId);

    setDetectedObjects(prev =>
      prev.map(obj =>
        obj.id === objectId
          ? { ...obj, selected: !obj.selected }
          : obj
      )
    );

    // Auto-save and blur re-application handled by useEffect at line 1643
    console.log('✅ Object visibility toggled - useEffect will auto-apply blur');
  };

  const deleteDetectedObject = (objectId: number) => {
    console.log('🗑️ Deleting object:', objectId);

    // Remove from both arrays
    setAllDetectedObjects(prev => prev.filter(obj => obj.id !== objectId));
    setDetectedObjects(prev => prev.filter(obj => obj.id !== objectId));

    // Auto-save and blur re-application handled by useEffect at line 1643
    console.log('✅ Object deleted - useEffect will auto-apply blur to remaining objects');
  };

  // Toggle visibility of custom blur areas
  const toggleCustomAreaVisibility = (areaId: string) => {
    console.log('👁️ Toggling visibility for custom area:', areaId);

    setCustomBlurAreas(prev =>
      prev.map(area =>
        area.id === areaId
          ? { ...area, visible: !area.visible }
          : area
      )
    );

    // Auto-save handled by useEffect
  };

  // Check if there are any blurrable objects (detected objects OR custom areas)
  const hasBlurrableObjects = () => {
    const visibleDetectedObjects = detectedObjects.filter(obj => obj.selected);
    const visibleCustomAreas = customBlurAreas.filter(area => area.visible);
    return visibleDetectedObjects.length > 0 || visibleCustomAreas.length > 0;
  };

  // Helper functions for multiple prompts
  const getActivePrompts = () => textPrompts.filter(prompt => prompt.trim().length > 0);
  const getAllPromptsText = () => getActivePrompts().join(', ');

  // Check if detect button should be enabled (at least one prompt is not empty)
  const canDetect = () => {
    return textPrompts.some(prompt => prompt.trim().length > 0);
  };

  // Add new prompt
  const addNewPrompt = () => {
    setTextPrompts(prev => [...prev, '']);
  };

  // Remove prompt at index
  const removePrompt = (index: number) => {
    if (textPrompts.length > 1) {
      setTextPrompts(prev => prev.filter((_, i) => i !== index));
    }
  };

  // Update prompt at index
  const updatePrompt = (index: number, value: string) => {
    setTextPrompts(prev => prev.map((prompt, i) => i === index ? value : prompt));
    setHasUserTyped(true);
  };

  // Debounced intensity change handler
  const handleIntensityChange = (newIntensity: number) => {
    setPendingBlurIntensity(newIntensity);

    // BUG FIX: Enable auto-blur when user adjusts intensity slider
    // This ensures intensity changes are applied immediately
    setShouldAutoBlur(true);

    // Clear existing timer
    if (intensityDebounceTimer) {
      clearTimeout(intensityDebounceTimer);
    }

    // Set new timer for 300ms for near-real-time updates
    const timer = window.setTimeout(() => {
      setBlurIntensity(newIntensity);
      console.log(`🎚️ Intensity changed to ${newIntensity} - auto-applying blur`);

      // Check if full screen blur is enabled - if so, apply full screen blur
      if (detectionCategories.fullScreenBlur) {
        console.log('🌀 Full screen blur is enabled - applying full screen blur');
        applyFullScreenBlur();
        return;
      }

      // Otherwise, auto-apply blur to selected objects/areas
      const hasDetectedObjects = detectedObjects.filter(obj => obj.selected).length > 0;
      const hasCustomAreas = customBlurAreas.filter(area => area.visible).length > 0;

      if (hasDetectedObjects || hasCustomAreas) {
        console.log(`✅ Auto-applying blur: ${hasDetectedObjects ? detectedObjects.length + ' detected objects' : ''} ${hasCustomAreas ? customBlurAreas.length + ' custom areas' : ''}`);
        applyBlurToAllObjects();
      }
    }, 300); // Reduced from 2000ms to 300ms for real-time feel

    setIntensityDebounceTimer(timer);
  };

  // Immediate blur/pixelate toggle effect with full screen blur support
  const handleBlurTypeToggle = (newType: 'blur' | 'pixelate') => {
    console.log(`🔄 Switching blur type from ${blurType} to ${newType}`);

    // Update blur type state
    setBlurType(newType);

    // BUG FIX: Enable auto-blur when user explicitly clicks Blur/Pixelate button
    setShouldAutoBlur(true);

    // Check if full screen blur is enabled - if so, apply full screen blur with new type
    if (detectionCategories.fullScreenBlur) {
      console.log('🌀 Full screen blur is enabled - applying full screen blur with new type immediately');
      // Pass the new type directly to avoid waiting for state update
      applyFullScreenBlur(newType);
      return;
    }

    // Otherwise, apply to selected objects and custom areas
    const selectedDetectedObjects = detectedObjects.filter(obj => obj.selected === true);
    const visibleCustomAreas = customBlurAreas.filter(area => area.visible === true);

    console.log(`🎯 Effect will apply to:`);
    console.log(`   - Selected detected objects: ${selectedDetectedObjects.length}`);
    console.log(`   - Visible custom areas: ${visibleCustomAreas.length}`);
    console.log(`   - Selected objects:`, selectedDetectedObjects.map(obj => `${obj.label} (ID: ${obj.id})`));

    // Only apply if there are explicitly selected/visible objects
    if (selectedDetectedObjects.length > 0 || visibleCustomAreas.length > 0) {
      console.log(`✅ Applying ${newType} effect to ${selectedDetectedObjects.length + visibleCustomAreas.length} explicitly selected objects/areas`);
      // Pass the new type directly to apply it immediately
      blurAllDetectedObjects(newType);
    } else {
      console.log(`⚠️ No explicitly selected objects or visible custom areas - no effect applied`);
    }
  };

  // Apply blur to ONLY explicitly selected objects (prevents bleeding bug)
  const applyBlurToSelectedObjects = async () => {
    if (!imagePreview) return;

    // Get ONLY explicitly selected objects and visible custom areas
    const selectedDetectedObjects = detectedObjects.filter(obj => obj.selected === true);
    const visibleCustomAreas = customBlurAreas.filter(area => area.visible === true);

    if (selectedDetectedObjects.length === 0 && visibleCustomAreas.length === 0) {
      console.log('🧹 No selected objects or visible areas - clearing processed image');
      setProcessedImageUrl('');
      return;
    }

    console.log(`🎯 Applying blur to ONLY selected objects:`);
    console.log(`   - Selected detected objects: ${selectedDetectedObjects.map(obj => `${obj.label} (ID: ${obj.id})`)}`);
    console.log(`   - Visible custom areas: ${visibleCustomAreas.map(area => `Area ${area.id}`)}`);

    setIsProcessing(true);
    try {
      await blurAllDetectedObjects(); // This function already filters for selected objects
    } catch (error) {
      console.error('Error applying blur to selected objects:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  // Apply blur to all objects (both detected and custom) - FIXED: Only selected objects
  const applyBlurToAllObjects = async () => {
    console.log('🎯 applyBlurToAllObjects called - delegating to applyBlurToSelectedObjects');
    await applyBlurToSelectedObjects();
  };

  // Filter detected objects based on confidence threshold
  const filterObjectsByConfidence = (threshold: number) => {
    const filteredObjects = allDetectedObjects.filter(obj => obj.confidence >= threshold * 100);
    console.log(`🎯 Filtering: ${allDetectedObjects.length} total objects, threshold: ${threshold * 100}%`);
    console.log(`🎯 Objects passing filter: ${filteredObjects.length}`);
    console.log(`🎯 Filtered objects:`, filteredObjects.map(obj => `${obj.label} (${obj.confidence}%)`));

    setDetectedObjects(filteredObjects);

    // Force a re-render (auto-save handled by useEffect)
    setTimeout(() => {
      console.log(`🔄 State updated: detectedObjects now has ${filteredObjects.length} objects`);
    }, 100);
  };

  // Update confidence threshold and filter objects
  const updateConfidenceThreshold = (newThreshold: number) => {
    setConfidenceThreshold(newThreshold);
    console.log('🎚️ Confidence threshold updated to:', newThreshold);

    // Filter objects immediately
    if (allDetectedObjects.length > 0) {
      filterObjectsByConfidence(newThreshold);
    }

    // Auto-save and blur re-application handled by useEffect at line 1643
    console.log('✅ Confidence threshold updated - useEffect will auto-apply blur');
  };

  // Real-time blur intensity update with optimized debouncing (REMOVED processedImageUrl requirement)
  const updateBlurIntensity = (newIntensity: number) => {
    console.log('🎚️ INTENSITY SLIDER MOVED!');
    console.log('📊 Slider state:', {
      oldIntensity: blurIntensity,
      newIntensity,
      detectedObjects: detectedObjects.length,
      customAreas: customBlurAreas.length
    });

    setBlurIntensity(newIntensity);

    // Auto-save handled by useEffect

    // Re-apply blur if there are detected objects OR custom areas (no processedImageUrl requirement!)
    const hasDetectedObjects = detectedObjects.filter(obj => obj.selected).length > 0;
    const hasCustomAreas = customBlurAreas.filter(area => area.visible).length > 0;
    const isFullScreenBlur = detectionCategories.fullScreenBlur;
    const shouldReapplyBlur = hasDetectedObjects || hasCustomAreas || isFullScreenBlur;

    if (shouldReapplyBlur) {
      console.log('✅ Auto-applying blur with new intensity:', {
        hasDetectedObjects,
        hasCustomAreas,
        isFullScreenBlur
      });

      // Clear any existing timeout
      if ((window as any).blurIntensityTimeout) {
        clearTimeout((window as any).blurIntensityTimeout);
      }

      // PERFORMANCE FIX: Use 20ms for full screen blur (near-instant), 300ms for objects (smooth dragging)
      const delay = isFullScreenBlur ? 20 : 300;
      (window as any).blurIntensityTimeout = setTimeout(() => {
        console.log(`🔄 Re-applying blur with intensity: ${newIntensity} (delay: ${delay}ms)`);

        if (isFullScreenBlur) {
          console.log('🌀 Re-applying FULL SCREEN blur with new intensity');
          applyFullScreenBlur();
        } else {
          console.log('🎯 Re-applying blur with new intensity');
          blurAllDetectedObjects();
        }
      }, delay);
    } else {
      console.log('⚠️ No objects to blur - skipping auto-apply');
    }
  };

  // Auto-save current state to localStorage and database
  const autoSaveState = async () => {
    if (!mediaFile.id) return;

    try {
      const studioState = {
        mediaFileId: mediaFile.id,
        detectedObjects: detectedObjects,
        allDetectedObjects: allDetectedObjects,
        customBlurAreas: customBlurAreas,
        textPrompts: textPrompts,
        lastUsedPrompt: lastUsedPrompt,
        blurIntensity: blurIntensity,
        blurType: blurType,
        confidenceThreshold: confidenceThreshold,
        detectionCategories: detectionCategories,
        processedImageUrl: processedImageUrl,
        hasUserTyped: hasUserTyped,
        timestamp: Date.now()
      };

      // Save to localStorage for immediate persistence
      const storageKey = `studio_state_${mediaFile.id}`;
      localStorage.setItem(storageKey, JSON.stringify(studioState));
      console.log('💾 Studio state saved to localStorage:', storageKey);
      console.log('💾 Saved state contains:', {
        detectedObjects: studioState.detectedObjects.length,
        allDetectedObjects: studioState.allDetectedObjects.length,
        customBlurAreas: studioState.customBlurAreas.length,
        textPrompts: studioState.textPrompts,
        hasUserTyped: studioState.hasUserTyped
      });

      // Also save to database (non-blocking)
      try {
        // PERFORMANCE FIX: Use deterministic logic instead of random
        // Save to database every 5th auto-save call to reduce API calls while ensuring persistence
        autoSaveCounterRef.current += 1;
        const shouldSaveToDb = autoSaveCounterRef.current % 5 === 0; // Every 5th save

        if (shouldSaveToDb) {
          const response = await fetch('/api/studio/save-state', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(studioState),
          });

          if (response.ok) {
            const result = await response.json();
            console.log(`💾 Studio state saved (save #${autoSaveCounterRef.current}):`, result.database_saved ? 'database + localStorage' : 'localStorage only');
          } else {
            console.log(`💾 Database save failed (save #${autoSaveCounterRef.current}) - localStorage saved`);
          }
        } else {
          console.log(`💾 Database save skipped (save #${autoSaveCounterRef.current}, next DB save in ${5 - (autoSaveCounterRef.current % 5)}) - localStorage saved`);
        }
      } catch (dbError) {
        console.log(`💾 Database save failed (save #${autoSaveCounterRef.current}) - localStorage saved`);
      }
    } catch (error) {
      console.error('❌ Auto-save failed:', error);
    }
  };

  // Load saved state from localStorage
  const loadSavedState = () => {
    if (!mediaFile.id) return;

    try {
      const storageKey = `studio_state_${mediaFile.id}`;
      const savedState = localStorage.getItem(storageKey);

      if (savedState) {
        const state = JSON.parse(savedState);
        console.log('📂 Loading saved studio state:', {
          detectedObjects: state.detectedObjects?.length || 0,
          allDetectedObjects: state.allDetectedObjects?.length || 0,
          customBlurAreas: state.customBlurAreas?.length || 0,
          textPrompts: state.textPrompts,
          hasUserTyped: state.hasUserTyped
        });

        // Restore state with proper checks
        if (state.detectedObjects && Array.isArray(state.detectedObjects)) {
          setDetectedObjects(state.detectedObjects);
          console.log('📂 Restored detectedObjects:', state.detectedObjects.length);
        }
        if (state.allDetectedObjects && Array.isArray(state.allDetectedObjects)) {
          setAllDetectedObjects(state.allDetectedObjects);
          console.log('📂 Restored allDetectedObjects:', state.allDetectedObjects.length);
        }
        if (state.customBlurAreas && Array.isArray(state.customBlurAreas)) {
          setCustomBlurAreas(state.customBlurAreas);
          console.log('📂 Restored customBlurAreas:', state.customBlurAreas.length);
        }
        if (state.textPrompts && Array.isArray(state.textPrompts)) setTextPrompts(state.textPrompts);
        if (state.lastUsedPrompt) setLastUsedPrompt(state.lastUsedPrompt);
        if (state.blurIntensity) setBlurIntensity(state.blurIntensity);
        if (state.blurType) setBlurType(state.blurType);
        if (state.confidenceThreshold) setConfidenceThreshold(state.confidenceThreshold);
        if (state.detectionCategories) setDetectionCategories(state.detectionCategories);
        if (state.processedImageUrl) setProcessedImageUrl(state.processedImageUrl);
        if (state.hasUserTyped !== undefined) setHasUserTyped(state.hasUserTyped);

        console.log('✅ Studio state restored successfully');
      } else {
        console.log('📂 No saved state found for this media file');
      }
    } catch (error) {
      console.error('❌ Failed to load saved state:', error);
    }
  };

  // Mark file as draft when studio opens
  const markFileAsDraft = async () => {
    if (!mediaFile.id) return;

    try {
      console.log(`📋 Marking file ${mediaFile.id} as draft...`);

      const response = await fetch('/api/user/files/update-status', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fileId: mediaFile.id,
          status: 'draft'
        })
      });

      if (response.ok) {
        console.log('✅ File marked as draft successfully');
      } else {
        console.error('❌ Failed to mark file as draft:', await response.text());
      }
    } catch (error) {
      console.error('❌ Error marking file as draft:', error);
    }
  };

  // Load saved state when component mounts or media file changes
  useEffect(() => {
    if (mediaFile.id) {
      loadSavedState();
      // Mark file as draft when opened in studio
      markFileAsDraft();
    }
  }, [mediaFile.id]);

  // Auto-save with reduced frequency to prevent API spam
  useEffect(() => {
    if (mediaFile.id) {
      console.log('🔄 State changed - scheduling auto-save');
      const timeoutId = setTimeout(() => {
        autoSaveState();
      }, 2000); // Increased delay to reduce API calls

      return () => clearTimeout(timeoutId);
    }
  }, [
    detectedObjects,
    allDetectedObjects,
    customBlurAreas,
    textPrompts,
    lastUsedPrompt,
    blurIntensity,
    blurType,
    confidenceThreshold,
    detectionCategories,
    processedImageUrl,
    hasUserTyped,
    mediaFile.id
  ]);

  // Check and deduct credits using dashboard approach
  const checkAndDeductCredits = async (creditsNeeded: number = 3): Promise<boolean> => {
    if (!user?.id) {
      setError('User not authenticated');
      return false;
    }

    try {
      // Check current credits using same method as dashboard
      const { data: userData, error: fetchError } = await supabase
        .from('users')
        .select('credits, subscription_type')
        .eq('id', user.id)
        .single();

      if (fetchError || !userData) {
        console.error('❌ Failed to fetch user credits:', fetchError);
        setError('Failed to check credits. Please try again.');
        return false;
      }

      const currentCredits = userData.credits || 0;
      const requiredCredits = creditsNeeded;

      console.log(`💰 User has ${currentCredits} credits, needs ${requiredCredits}`);

      // Check if user has enough credits
      if (currentCredits < requiredCredits) {
        console.log(`❌ Insufficient credits: has ${currentCredits}, needs ${requiredCredits}`);
        setInsufficientCreditsData({ current: currentCredits, required: requiredCredits });
        setShowInsufficientCreditsModal(true);
        return false;
      }

      // Deduct credits directly in Supabase
      const newCredits = currentCredits - requiredCredits;
      const { error: updateError } = await supabase
        .from('users')
        .update({
          credits: newCredits,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id);

      if (updateError) {
        console.error('❌ Failed to deduct credits:', updateError);
        setError('Failed to deduct credits. Please try again.');
        return false;
      }

      console.log(`✅ Successfully deducted ${requiredCredits} credits. Remaining: ${newCredits}`);
      return true;

    } catch (error) {
      console.error('❌ Error in credit check/deduction:', error);
      setError('Error processing credits. Please try again.');
      return false;
    }
  };

  // Clear all detections
  const clearAllDetections = () => {
    setAllDetectedObjects([]);
    setDetectedObjects([]);
    setProcessedImageUrl('');
    setLastUsedPrompt('');
    console.log('🧹 Cleared all detections');

    // Auto-save handled by useEffect
  };

  // Add custom blur area with 4-digit random ID
  const addCustomBlurArea = () => {
    if (!imageRef.current) return;

    const img = imageRef.current;

    // Generate 4-digit random ID
    const randomId = Math.floor(1000 + Math.random() * 9000);

    // Create a new custom blur area in the center of the image
    const centerX = img.naturalWidth * 0.4;
    const centerY = img.naturalHeight * 0.4;
    const width = img.naturalWidth * 0.2;
    const height = img.naturalHeight * 0.2;

    const newArea = {
      id: randomId.toString(),
      x: centerX,
      y: centerY,
      width: width,
      height: height,
      visible: true // Default to visible
    };

    setCustomBlurAreas(prev => [...prev, newArea]);
    console.log('➕ Added custom blur area with ID:', randomId);

    // Auto-save handled by useEffect
  };

  // Handle dragging custom blur areas
  const handleCustomAreaDrag = (areaId: string, startX: number, startY: number, e: React.MouseEvent) => {
    e.preventDefault();

    const img = imageRef.current;
    if (!img) return;

    const imgRect = img.getBoundingClientRect();
    const scaleX = img.naturalWidth / imgRect.width;
    const scaleY = img.naturalHeight / imgRect.height;

    const startMouseX = e.clientX;
    const startMouseY = e.clientY;

    const handleMouseMove = (moveEvent: MouseEvent) => {
      const deltaX = (moveEvent.clientX - startMouseX) * scaleX;
      const deltaY = (moveEvent.clientY - startMouseY) * scaleY;

      setCustomBlurAreas(prev => prev.map(area =>
        area.id === areaId
          ? {
              ...area,
              x: Math.max(0, Math.min(img.naturalWidth - area.width, startX + deltaX)),
              y: Math.max(0, Math.min(img.naturalHeight - area.height, startY + deltaY))
            }
          : area
      ));
    };

    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      console.log('✅ Finished dragging area:', areaId);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // Check API health and AI detection readiness
  const checkApiHealth = async (baseUrl: string): Promise<boolean> => {
    try {
      const response = await fetch(`${baseUrl}/health`, {
        method: 'GET',
        signal: AbortSignal.timeout(5000) // 5 second timeout
      });

      if (!response.ok) return false;

      const healthData = await response.json();
      console.log('🏥 Health check response:', healthData);

      // Check if AI detection service is ready
      const aiReady = healthData.services?.ai_detection === 'ready';
      const status = healthData.status;

      console.log(`🤖 AI Detection: ${aiReady ? 'Ready' : 'Not Ready'} (Status: ${status})`);

      return aiReady;
    } catch (error) {
      console.error('❌ Health check failed:', error);
      return false;
    }
  };

  // Check API status on component mount
  useEffect(() => {
    const checkApiStatus = async () => {
      console.log('🔍 Checking API status...');
      setApiStatus('checking');

      // NEW: Check RunPod backend health
      if (USE_RUNPOD_BACKEND) {
        console.log('🚀 Checking RunPod backend health...');

        try {
          const response = await fetch('/api/detection/health', {
            method: 'GET',
            signal: AbortSignal.timeout(5000) // 5 second timeout
          });

          if (response.ok) {
            const healthData = await response.json();
            console.log('✅ RunPod backend health check:', healthData);
            setApiStatus('online');
          } else {
            console.error('❌ RunPod backend health check failed:', response.status);
            setApiStatus('offline');
          }
        } catch (error) {
          console.error('❌ RunPod backend health check error:', error);
          setApiStatus('offline');
        }
        return;
      }

      // OLD: Check Google Cloud Run endpoints (only when not using RunPod)
      console.log('🔍 Testing primary API:', API_BASE_URL);
      const primaryHealthy = await checkApiHealth(API_BASE_URL);

      if (primaryHealthy) {
        setApiStatus('online');
        console.log('✅ Primary API service is online and AI detection is ready');
        return;
      }

      console.log('🔍 Testing fallback API:', FALLBACK_API_URL);
      const fallbackHealthy = await checkApiHealth(FALLBACK_API_URL);

      if (fallbackHealthy) {
        setApiStatus('online');
        console.log('✅ Fallback API service is online and AI detection is ready');
      } else {
        setApiStatus('offline');
        console.error('❌ Both API services are offline or AI detection is not ready');
        console.error('💡 This could mean:');
        console.error('   - The AI model is still loading');
        console.error('   - The GPU service is not running');
        console.error('   - Network connectivity issues');
      }
    };

    checkApiStatus();
  }, []);

  // Enhanced API call with detailed debugging
  const tryApiCall = async (formData: FormData, operation: string): Promise<Response> => {
    const urls = [API_BASE_URL, FALLBACK_API_URL];

    // Debug: Log all FormData parameters
    console.log(`🔍 ${operation} - FormData parameters:`);
    for (let [key, value] of formData.entries()) {
      if (key !== 'image') {
        console.log(`   ${key}: "${value}"`);
      } else {
        console.log(`   ${key}: [Blob ${(value as Blob).size} bytes]`);
      }
    }

    for (let i = 0; i < urls.length; i++) {
      const baseUrl = urls[i]!;
      const url = `${baseUrl}/process_image`;

      console.log(`📤 ${operation} request to:`, url);

      try {
        const response = await fetch(url, {
          method: 'POST',
          body: formData,
          headers: {
            'Accept': 'application/json, image/*',
          }
        });

        console.log(`📊 ${operation} response status:`, response.status);
        console.log(`📊 ${operation} response headers:`, Object.fromEntries(response.headers.entries()));

        if (response.ok) {
          console.log(`✅ ${operation} successful with URL:`, url);
          return response;
        } else {
          console.warn(`⚠️ ${operation} failed with status ${response.status} from:`, url);

          // Try to get error details
          const contentType = response.headers.get('content-type');
          let errorDetails = '';

          if (contentType?.includes('application/json')) {
            try {
              const errorJson = await response.json();
              errorDetails = JSON.stringify(errorJson, null, 2);
              console.error(`❌ ${operation} JSON error response:`, errorJson);
            } catch (e) {
              errorDetails = 'Failed to parse JSON error response';
            }
          } else {
            errorDetails = await response.text();
            console.error(`❌ ${operation} text error response:`, errorDetails);
          }

          if (i === urls.length - 1) {
            throw new Error(`${operation} failed: ${response.status} - ${errorDetails}`);
          }
        }
      } catch (error) {
        console.error(`❌ ${operation} error with URL ${url}:`, error);
        if (i === urls.length - 1) {
          throw new Error(`API service unavailable: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
        console.log(`🔄 Trying fallback URL...`);
      }
    }

    throw new Error(`${operation} failed: All API endpoints unavailable`);
  };

  // Enhanced debug function to test API endpoints
  const testBlurAPI = async () => {
    if (!imagePreview) {
      console.error('❌ No image available for testing');
      return;
    }

    console.log('🧪 Testing blur API endpoints...');
    setIsProcessing(true);

    try {
      // Create a simple test image blob
      let imageBlob: Blob;
      if (imagePreview.startsWith('data:')) {
        const response = await fetch(imagePreview);
        imageBlob = await response.blob();
      } else {
        const imageResponse = await fetch(imagePreview);
        imageBlob = await imageResponse.blob();
      }

      console.log('📷 Test image blob size:', imageBlob.size, 'bytes');

      // Test 1: Minimal blur request (most likely to work)
      console.log('🧪 Test 1: Minimal blur request');
      const minimalFormData = new FormData();
      minimalFormData.append('image', imageBlob, 'image.jpg');
      minimalFormData.append('text_prompt', 'test');
      minimalFormData.append('blur_detections', 'false');
      minimalFormData.append('blur_strength', '50');
      minimalFormData.append('blur_type', 'blur');

      try {
        const minimalResponse = await tryApiCall(minimalFormData, 'Test Minimal Blur');
        const contentType = minimalResponse.headers.get('content-type');
        console.log('✅ Minimal blur test passed, content-type:', contentType);

        if (contentType?.startsWith('image/')) {
          const blob = await minimalResponse.blob();
          console.log('📷 Received image blob size:', blob.size, 'bytes');
          const testUrl = URL.createObjectURL(blob);
          setProcessedImageUrl(testUrl);
          console.log('✅ Test image displayed successfully!');
        }
      } catch (error) {
        console.error('❌ Minimal blur test failed:', error);
      }

      // Test 2: Full screen blur
      console.log('🧪 Test 2: Full screen blur');
      const fullScreenFormData = new FormData();
      fullScreenFormData.append('image', imageBlob, 'image.jpg');
      fullScreenFormData.append('text_prompt', 'full_screen_blur');
      fullScreenFormData.append('blur_detections', 'true');
      fullScreenFormData.append('blur_strength', '50');
      fullScreenFormData.append('blur_type', 'blur');
      fullScreenFormData.append('full_screen', 'true');

      try {
        const fullScreenResponse = await tryApiCall(fullScreenFormData, 'Test Full Screen Blur');
        console.log('✅ Full screen blur test passed');
      } catch (error) {
        console.error('❌ Full screen blur test failed:', error);
      }

    } catch (error) {
      console.error('❌ API test failed:', error);
      setError(`API test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsProcessing(false);
    }
  };



  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowUserDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);





  // Load image - use signed URL directly instead of converting to base64
  const loadImageAsBase64 = async () => {
    setIsLoadingImage(true);
    setImageLoadError(null);
    console.log('🖼️ Loading image');
    console.log('📁 Media file info:', mediaFile);
    console.log('📁 Original image URL:', originalImageUrl);

    // Check if we have a valid image URL (not a placeholder)
    if (!originalImageUrl ||
        originalImageUrl.includes('placeholder') ||
        originalImageUrl.includes('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==')) {
      console.log('📷 No valid image URL provided, showing upload interface');
      setImageLoadError('No image provided. Please upload an image to get started.');
      setIsLoadingImage(false);
      return;
    }

    try {
      // If it's already a base64 string, use it directly
      if (originalImageUrl.startsWith('data:')) {
        console.log('✅ Using base64 image directly');
        setImagePreview(originalImageUrl);
        setImageLoadError(null);
        setIsLoadingImage(false);
        return;
      }

      // For Supabase signed URLs, use them directly without conversion
      // The browser can load images from signed URLs without issues
      console.log('✅ Using signed URL directly:', originalImageUrl);
      setImagePreview(originalImageUrl);
      setImageLoadError(null);
      setIsLoadingImage(false);
    } catch (error) {
      console.error('❌ Error loading image:', error);
      setImageLoadError('Failed to load image. Please try uploading again.');
      setIsLoadingImage(false);
    }
  };

  // Load saved processing state
  const loadSavedProcessingState = async () => {
    if (!mediaFile.id) return;

    try {
      console.log('🔍 Loading saved processing state for file:', mediaFile.id);
      const response = await fetch(`/api/studio/load-processing?fileId=${mediaFile.id}`);

      if (response.ok) {
        const result = await response.json();

        if (result.found && result.processing_state) {
          const state = result.processing_state;
          console.log('✅ Found saved processing state:', state);

          // Restore comprehensive processing settings
          if (state.processing_settings) {
            const settings = state.processing_settings;

            // Restore current settings
            if (settings.blur_intensity !== undefined) {
              setBlurIntensity(settings.blur_intensity);
            }
            if (settings.blur_type) {
              setBlurType(settings.blur_type);
            }
            if (settings.confidence_threshold !== undefined) {
              setConfidenceThreshold(settings.confidence_threshold);
            }

            // Restore detection history
            if (settings.all_detected_objects && Array.isArray(settings.all_detected_objects)) {
              setAllDetectedObjects(settings.all_detected_objects);
              console.log('✅ Restored all detected objects:', settings.all_detected_objects.length);
            }
            if (settings.filtered_detected_objects && Array.isArray(settings.filtered_detected_objects)) {
              setDetectedObjects(settings.filtered_detected_objects);
              console.log('✅ Restored filtered objects:', settings.filtered_detected_objects.length);
            }
            if (settings.last_used_prompt) {
              setLastUsedPrompt(settings.last_used_prompt);
            }

            // Legacy compatibility
            if (settings.detected_objects && Array.isArray(settings.detected_objects) && !settings.all_detected_objects) {
              // Old format - treat as both all and filtered
              setAllDetectedObjects(settings.detected_objects);
              setDetectedObjects(settings.detected_objects);
            }
            if (settings.detection_categories) {
              setDetectionCategories(settings.detection_categories);
            }
            if (settings.custom_blur_areas && Array.isArray(settings.custom_blur_areas)) {
              setCustomBlurAreas(settings.custom_blur_areas);
            }

            console.log('🔄 Restored processing settings:', {
              totalObjects: settings.all_detected_objects?.length || 0,
              visibleObjects: settings.filtered_detected_objects?.length || 0,
              uniquePrompts: settings.unique_prompts_used?.length || 0,
              confidenceThreshold: settings.confidence_threshold
            });
          }

          // Restore processed image if available
          if (state.processed_image_url) {
            // If it's a base64 string, use it directly
            // If it's a URL, try to load it
            if (state.processed_image_url.startsWith('data:')) {
              setProcessedImageUrl(state.processed_image_url);
              console.log('✅ Restored processed image from base64');
            } else {
              // Try to load the URL, if it fails, clear it
              try {
                const img = new Image();
                img.onload = () => {
                  setProcessedImageUrl(state.processed_image_url);
                  console.log('✅ Restored processed image from URL');
                };
                img.onerror = () => {
                  console.warn('⚠️ Saved processed image URL is no longer valid');
                  setProcessedImageUrl('');
                };
                img.src = state.processed_image_url;
              } catch (error) {
                console.warn('⚠️ Failed to load saved processed image:', error);
                setProcessedImageUrl('');
              }
            }
          }

          console.log('🎯 Processing state restored successfully');
        } else {
          console.log('ℹ️ No saved processing state found');
        }
      }
    } catch (error) {
      // CRITICAL FIX Issue 5: Silent error handling - no user-facing errors
      console.log('⚠️ Failed to load processing state (silent handling):', error);
      // Continue without showing errors to user - this prevents "File Not Found" messages
    }
  };

  // Load image and saved state on component mount
  useEffect(() => {
    loadImageAsBase64();
    loadSavedProcessingState();
  }, [originalImageUrl, mediaFile]);



  // Initialize with media file
  useEffect(() => {
    console.log('🎬 Initializing GuardiaVision Studio with media file:', mediaFile.original_filename);
    setImagePreview(originalImageUrl);
  }, [mediaFile, originalImageUrl]);

  // Fetch all user images for navigation
  useEffect(() => {
    if (user?.id) {
      fetchAllUserImages();
    }
  }, [user?.id]);

  // NEW: Convert detection results to DetectedObject format
  useEffect(() => {
    if (detectionResult && detectionResult.metadata) {
      console.log('🔄 Converting detection results to DetectedObject format');

      const { detection_all } = detectionResult.metadata;
      const newDetectedObjects: DetectedObject[] = detection_all.boxes.map((box, index) => {
        // Generate color based on index (golden angle for distinct colors)
        const hue = (index * 137.5) % 360;
        const color = `hsl(${hue}, 70%, 50%)`;

        return {
          id: Date.now() + index, // Unique ID
          label: detection_all.labels[index],
          confidence: Math.round(detection_all.scores[index] * 100), // Convert to percentage
          selected: true, // Show by default
          color: color,
          bbox: box as [number, number, number, number],
          prompt: lastUsedPrompt || 'person' // Store the prompt used
        };
      });

      console.log('✅ Converted detections:', {
        count: newDetectedObjects.length,
        objects: newDetectedObjects.map(obj => `${obj.label} (${obj.confidence}%)`),
      });

      // Set all detected objects
      setAllDetectedObjects(newDetectedObjects);

      // Apply confidence threshold filter
      filterObjectsByConfidence(confidenceThreshold);
    }
  }, [detectionResult, lastUsedPrompt, confidenceThreshold]);

  // Auto-apply blur when detections or custom areas change (with 1-second delay for custom boxes)
  // BUG FIX: Only auto-blur for custom areas, full screen blur, or when explicitly enabled
  useEffect(() => {
    // Skip if no image loaded
    if (!imagePreview) return;

    const selectedDetections = detectedObjects.filter(obj => obj.selected);
    const visibleCustomAreas = customBlurAreas.filter(area => area.visible);
    const isFullScreenBlurEnabled = detectionCategories.fullScreenBlur;

    // BUG FIX: Only auto-apply blur for:
    // 1. Custom blur areas (user is actively editing)
    // 2. Full screen blur (explicit toggle)
    // 3. When shouldAutoBlur is true (user clicked Blur/Pixelate button)
    const shouldApplyBlur = (visibleCustomAreas.length > 0 || isFullScreenBlurEnabled || shouldAutoBlur)
                           && (selectedDetections.length > 0 || visibleCustomAreas.length > 0 || isFullScreenBlurEnabled);

    if (shouldApplyBlur) {
      console.log(`🔄 Auto-applying blur: detections=${selectedDetections.length}, custom areas=${visibleCustomAreas.length}, fullscreen=${isFullScreenBlurEnabled}, autoBlur=${shouldAutoBlur}`);

      // Debounce: 1000ms for custom boxes (user may still be editing), 200ms for others
      if ((window as any).autoBlurTimeout) {
        clearTimeout((window as any).autoBlurTimeout);
      }

      // Use longer delay (1 sec) if custom areas are involved to prevent flickering while user edits
      const delay = visibleCustomAreas.length > 0 ? 1000 : 200;
      console.log(`⏱️ Setting blur delay: ${delay}ms`);

      (window as any).autoBlurTimeout = setTimeout(() => {
        // If full screen blur is enabled, apply full screen blur
        if (isFullScreenBlurEnabled) {
          console.log('🌀 Full screen blur enabled - applying full screen blur');
          applyFullScreenBlur();
        } else {
          // Otherwise apply blur to selected objects/areas
          applyBlurToAllObjects();
        }
      }, delay);
    } else if (processedImageUrl && !isFullScreenBlurEnabled) {
      // CRITICAL FIX: Only clear processed image if no objects AND full screen blur is NOT enabled
      console.log('🧹 No objects selected and full screen blur disabled - clearing processed image');
      setProcessedImageUrl('');
    }
  }, [detectedObjects, customBlurAreas, imagePreview, detectionCategories.fullScreenBlur, shouldAutoBlur]);

  // OLD: Handle RunPod backend processing results (DISABLED - now using new detection endpoint)
  // useEffect(() => {
  //   const handleRunPodResult = async () => {
  //     if (runpodDownloadUrl) {
  //       console.log('🎉 RunPod processing complete! Download URL ready:', runpodDownloadUrl);

  //       try {
  //         // Fetch the processed image from the download URL
  //         const response = await fetch(runpodDownloadUrl);
  //         if (!response.ok) {
  //           throw new Error(`Failed to download result: ${response.status}`);
  //         }

  //         const blob = await response.blob();
  //         const imageUrl = URL.createObjectURL(blob);

  //         console.log('✅ Processed image downloaded and ready to display');
  //         setProcessedImageUrl(imageUrl);
  //         setIsProcessing(false);
  //       } catch (error) {
  //         console.error('❌ Failed to download processed image:', error);
  //         setError('Failed to download processed result');
  //         setIsProcessing(false);
  //       }
  //     }
  //   };

  //   handleRunPodResult();
  // }, [runpodDownloadUrl]);

  // OLD: Update processing state from RunPod hook (DISABLED - now using new detection endpoint)
  // useEffect(() => {
  //   if (runpodIsProcessing) {
  //     setIsProcessing(true);
  //   }
  // }, [runpodIsProcessing]);

  // OLD: Handle RunPod errors (DISABLED - now using new detection endpoint)
  // useEffect(() => {
  //   if (runpodError) {
  //     console.error('❌ RunPod processing error:', runpodError);
  //     setError(`AI processing failed: ${runpodError}`);
  //     setIsProcessing(false);
  //   }
  // }, [runpodError]);

  // Professional Tooltip component (Apple/SpaceX inspired)
  const Tooltip = ({ text }: { text: string }) => (
    <div className="relative group inline-block ml-1">
      <div className="w-3.5 h-3.5 rounded-full bg-gray-600/50 hover:bg-gray-500/70 flex items-center justify-center cursor-help transition-all duration-200">
        <span className="text-[10px] text-gray-300 font-semibold">?</span>
      </div>
      <div className="absolute left-1/2 -translate-x-1/2 bottom-full mb-2 hidden group-hover:block z-50 w-48">
        <div className="bg-gray-900/95 backdrop-blur-sm text-white text-xs px-3 py-2 rounded-lg shadow-2xl border border-gray-700/50">
          {text}
          <div className="absolute left-1/2 -translate-x-1/2 top-full w-0 h-0 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-gray-900/95"></div>
        </div>
      </div>
    </div>
  );

  // Toggle switch component matching the screenshot
  const ToggleSwitch = ({
    enabled,
    onChange,
    label,
    hasWarning = false
  }: {
    enabled: boolean;
    onChange: (enabled: boolean) => void;
    label: string;
    hasWarning?: boolean;
  }) => (
    <div className="flex items-center justify-between py-3">
      <div className="flex items-center gap-2">
        <span className="text-white text-sm font-medium">{label}</span>
        {hasWarning && (
          <div className="w-4 h-4 bg-orange-500 rounded-full flex items-center justify-center">
            <span className="text-white text-xs font-bold">!</span>
          </div>
        )}
      </div>
      <button
        onClick={() => onChange(!enabled)}
        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
          enabled ? 'bg-purple-600' : 'bg-gray-600'
        }`}
      >
        <span
          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
            enabled ? 'translate-x-6' : 'translate-x-1'
          }`}
        />
      </button>
    </div>
  );



  // Step 1: Sequential prompt processing - each prompt as separate API call
  const detectOnly = async () => {
    if (!imagePreview) {
      setError('No image available for processing');
      return;
    }

    const activePrompts = getActivePrompts();
    if (activePrompts.length === 0) {
      setError('Please enter what you want to detect in at least one prompt box');
      return;
    }

    if (apiStatus === 'offline') {
      setError('AI service is currently offline. Please check your connection or try again later.');
      return;
    }

    // 💰 CREDIT SYSTEM: Check credits for ALL prompts (3 credits per prompt)
    const totalCreditsNeeded = activePrompts.length * 3;
    console.log(`💰 Checking credits for ${activePrompts.length} prompts (${totalCreditsNeeded} credits required)...`);
    const hasCredits = await checkAndDeductCredits(totalCreditsNeeded);
    if (!hasCredits) {
      console.log('❌ Detection cancelled due to insufficient credits');
      return; // Error message already set by checkAndDeductCredits
    }

    setIsProcessing(true);
    setError(null);

    try {
      const startTime = performance.now();
      console.log(`🔍 Starting sequential AI detection with ${activePrompts.length} prompts:`, activePrompts);

      // Store the prompts for future use
      setLastUsedPrompt(activePrompts.join(', '));

      // ============================================================
      // NEW: Use RunPod Backend for Detection (Async Job Processing)
      // ============================================================
      if (USE_RUNPOD_BACKEND) {
        console.log('🚀 Using RunPod backend for detection (async processing)...');

        // Use actual file_path from mediaFile (includes timestamp prefix)
        const filePath = mediaFile.file_path;

        console.log('📁 File path for backend:', filePath);
        console.log('🔍 Detection prompts:', activePrompts);

        // Combine all prompts for detection (comma-separated)
        const combinedPrompt = activePrompts.join(', ');
        console.log('🎯 Combined prompt for detection:', combinedPrompt);

        // Submit detection job to new backend endpoint (returns JSON metadata)
        try {
          setIsProcessing(true);
          await detect(filePath, combinedPrompt, 0.1);
          console.log('✅ Detection completed with new endpoint');
        } catch (err) {
          console.error('❌ Detection failed:', err);
          setError('Detection failed. Please try again.');
        } finally {
          setIsProcessing(false);
        }

        console.log('✅ Detection job completed');
        console.log('📊 Results available in detectionResult state');

        // Exit early - the useEffect hooks will handle the result
        // When runpodDownloadUrl is set, the image will be automatically displayed
        return;
      }

      // ============================================================
      // OLD: Use Google Cloud Run Backend (kept for fallback)
      // ============================================================
      console.log('📤 Using old Google Cloud Run backend for detection...');

      // Prepare image blob once for all API calls
      let imageBlob: Blob;
      if (imagePreview.startsWith('data:')) {
        const response = await fetch(imagePreview);
        imageBlob = await response.blob();
        console.log('📷 Fast base64 to blob conversion, size:', imageBlob.size, 'bytes');
      } else {
        const imageResponse = await fetch(imagePreview);
        imageBlob = await imageResponse.blob();
        console.log('📷 Fetched image blob from URL, size:', imageBlob.size, 'bytes');
      }

      // Get the highest existing ID to continue numbering across all prompts
      let maxExistingId = allDetectedObjects.length > 0
        ? Math.max(...allDetectedObjects.map(obj => obj.id))
        : 0;

      let allNewObjects: DetectedObject[] = [];
      let successfulPrompts = 0;
      let failedPrompts = 0;

      // Process each prompt sequentially
      for (let promptIndex = 0; promptIndex < activePrompts.length; promptIndex++) {
        const currentPrompt = activePrompts[promptIndex]?.trim() || '';

        try {
          console.log(`🔍 Processing prompt ${promptIndex + 1}/${activePrompts.length}: "${currentPrompt}"`);

          // Create FormData for this specific prompt
          const formData = new FormData();
          formData.append('image', imageBlob, 'image.jpg');
          formData.append('text_prompt', currentPrompt);
          formData.append('score_threshold', '0.1'); // Low threshold to get all detections
          formData.append('visualize', 'false'); // FALSE = JSON response with bboxes
          formData.append('blur_detections', 'false'); // No blurring
          formData.append('blur_strength', '25'); // Required parameter
          formData.append('blur_type', 'blur'); // Required parameter
          // BUG FIX: Add deterministic seed for consistent detection results
          formData.append('seed', '42'); // Fixed seed for reproducibility

          console.log(`📤 API Call ${promptIndex + 1}: Sending prompt "${currentPrompt}" with seed=42 for deterministic results`);

          const response = await tryApiCall(formData, `Detection-Prompt-${promptIndex + 1}`);
          const result = await response.json();

          console.log(`✅ Prompt ${promptIndex + 1} API Response:`, result);
          console.log(`📊 Detected objects count for "${currentPrompt}":`, result.bboxes?.length || 0);

          // BUG FIX: Enhanced logging for detection consistency debugging
          if (result.bboxes && result.bboxes.length > 0) {
            console.log(`🔍 DETECTION DEBUG - Prompt: "${currentPrompt}"`);
            console.log(`   📦 Bbox count: ${result.bboxes.length}`);
            console.log(`   🎯 Bboxes:`, result.bboxes);
            console.log(`   📊 Scores:`, result.scores);
            console.log(`   🔑 Detection hash:`, JSON.stringify(result.bboxes).substring(0, 50));
          }

          // Process detection results for this prompt
          if (result.bboxes && result.bboxes.length > 0) {
            const promptObjects = result.bboxes.map((bbox: number[], index: number) => ({
              id: maxExistingId + index + 1,
              label: `${currentPrompt} #${index + 1}`, // Individual prompt labeling
              confidence: Math.round((result.scores?.[index] || 0.8) * 100),
              bbox: bbox as [number, number, number, number],
              color: `hsl(${((maxExistingId + index) * 60) % 360}, 70%, 50%)`,
              selected: true,
              prompt: currentPrompt // Store the specific prompt that detected this object
            }));

            allNewObjects = [...allNewObjects, ...promptObjects];
            maxExistingId += promptObjects.length; // Update ID counter for next prompt
            successfulPrompts++;

            console.log(`🎯 Prompt "${currentPrompt}" found ${promptObjects.length} objects`);
            console.log(`📊 Objects:`, promptObjects.map((obj: DetectedObject) => `${obj.label} (${obj.confidence}%)`));
          } else {
            console.log(`🔍 Prompt "${currentPrompt}" found no objects`);
            successfulPrompts++; // Still count as successful even if no objects found
          }

        } catch (error) {
          console.error(`❌ Prompt "${currentPrompt}" failed:`, error);
          failedPrompts++;
          // Continue processing remaining prompts
        }
      }

      // Accumulate all results
      if (allNewObjects.length > 0) {
        const updatedAllObjects = [...allDetectedObjects, ...allNewObjects];
        setAllDetectedObjects(updatedAllObjects);

        console.log(`🎯 Sequential detection complete:`);
        console.log(`   - Successful prompts: ${successfulPrompts}/${activePrompts.length}`);
        console.log(`   - Failed prompts: ${failedPrompts}/${activePrompts.length}`);
        console.log(`   - Total new objects: ${allNewObjects.length}`);
        console.log(`   - Total objects now: ${updatedAllObjects.length}`);

        // Filter all objects by confidence threshold
        const filteredObjects = updatedAllObjects.filter(obj => obj.confidence >= confidenceThreshold * 100);
        console.log(`🎯 Filtering: ${filteredObjects.length}/${updatedAllObjects.length} objects pass ${confidenceThreshold * 100}% threshold`);
        setDetectedObjects(filteredObjects);

        // Performance monitoring
        const endTime = performance.now();
        console.log(`⚡ Sequential detection completed in ${Math.round(endTime - startTime)}ms`);

      } else {
        console.log('🔍 No objects detected from any prompts');
        if (failedPrompts === activePrompts.length) {
          setError('All detection prompts failed. Please try again.');
        }
      }

      // Show summary to user
      if (failedPrompts > 0) {
        setError(`${failedPrompts} out of ${activePrompts.length} prompts failed. ${successfulPrompts} prompts processed successfully.`);
      }

    } catch (error) {
      console.error('❌ Sequential detection failed:', error);
      setError(`Detection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsProcessing(false);
      // BUG FIX: Reset auto-blur flag after detection completes
      setShouldAutoBlur(false);
    }
  };

  // Simplified blur function for testing new API
  const applySimpleBlur = async () => {
    if (!imagePreview) {
      setError('No image available for processing');
      return;
    }

    console.log('🚀 STARTING SIMPLE BLUR PROCESS');
    setIsProcessing(true);
    setError(null);

    try {
      // Convert image to blob
      let imageBlob: Blob;
      if (imagePreview.startsWith('data:')) {
        const response = await fetch(imagePreview);
        imageBlob = await response.blob();
      } else {
        const imageResponse = await fetch(imagePreview);
        imageBlob = await imageResponse.blob();
      }

      console.log('📷 Image blob size:', imageBlob.size, 'bytes');

      // Create minimal FormData with only essential parameters
      const formData = new FormData();
      formData.append('image', imageBlob, 'image.jpg');
      formData.append('blur_strength', blurIntensity.toString());
      formData.append('blur_type', blurType);

      // Try different parameter combinations
      const parameterSets = [
        // Set 1: Minimal parameters
        {
          text_prompt: 'blur_image',
          blur_detections: 'false'
        },
        // Set 2: Full screen blur
        {
          text_prompt: 'full_screen_blur',
          blur_detections: 'true',
          full_screen: 'true'
        },
        // Set 3: Traditional parameters
        {
          text_prompt: 'test',
          score_threshold: '0.5',
          visualize: 'false',
          blur_detections: 'false'
        }
      ];

      for (let i = 0; i < parameterSets.length; i++) {
        console.log(`🧪 Trying parameter set ${i + 1}:`, parameterSets[i]);

        const testFormData = new FormData();
        testFormData.append('image', imageBlob, 'image.jpg');
        testFormData.append('blur_strength', blurIntensity.toString());
        testFormData.append('blur_type', blurType);

        // Add parameters from current set
        const currentSet = parameterSets[i];
        if (currentSet) {
          Object.entries(currentSet).forEach(([key, value]) => {
            testFormData.append(key, value);
          });
        }

        try {
          const response = await tryApiCall(testFormData, `Simple Blur Set ${i + 1}`);
          const contentType = response.headers.get('content-type');

          if (contentType?.startsWith('image/')) {
            const blob = await response.blob();
            if (blob.size > 0) {
              const imageUrl = URL.createObjectURL(blob);
              setProcessedImageUrl(imageUrl);
              console.log(`✅ Simple blur successful with parameter set ${i + 1}!`);
              return; // Success, exit the loop
            }
          }
        } catch (error) {
          console.error(`❌ Parameter set ${i + 1} failed:`, error);
          if (i === parameterSets.length - 1) {
            throw error; // Re-throw if this was the last attempt
          }
        }
      }

    } catch (error) {
      console.error('❌ Simple blur failed:', error);
      setError(`Simple blur failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsProcessing(false);
    }
  };

  // Simple blur function that actually works
  const applyBlur = async () => {
    console.log('🚀 STARTING BLUR PROCESS');
    console.log('🔍 Current API URL:', API_BASE_URL);

    if (!imagePreview) {
      console.error('❌ No image available');
      setError('No image available');
      return;
    }

    const visibleDetectedObjects = detectedObjects.filter(obj => obj.selected);
    const visibleCustomAreas = customBlurAreas.filter(area => area.visible);

    if (visibleDetectedObjects.length === 0 && visibleCustomAreas.length === 0) {
      console.error('❌ No visible objects or custom areas');
      setError('No visible objects or custom areas to blur. Please detect objects or create custom areas first.');
      return;
    }

    console.log(`🎯 Will blur ${visibleDetectedObjects.length} detected objects + ${visibleCustomAreas.length} custom areas`);

    // 💰 CREDIT SYSTEM: Check and deduct 2 credits for blur operation
    console.log('💰 Checking credits for blur operation (2 credits required)...');
    const hasCredits = await checkAndDeductCredits(2);
    if (!hasCredits) {
      console.log('❌ Blur operation cancelled due to insufficient credits');
      return; // Error message already set by checkAndDeductCredits
    }

    // Get the last used prompt (simple approach)
    const promptToUse = lastUsedPrompt || getAllPromptsText();

    if (!promptToUse) {
      console.error('❌ No prompt available');
      setError('No detection prompt available');
      return;
    }

    console.log('✅ BLUR STARTING:', {
      prompt: promptToUse,
      intensity: blurIntensity,
      type: blurType,
      objects: detectedObjects.length,
      apiUrl: API_BASE_URL
    });

    console.log('🎯 Blur parameters:', {
      prompt: promptToUse,
      intensity: blurIntensity,
      type: blurType
    });

    setIsProcessing(true);
    setError(null);

    try {
      const startTime = performance.now();

      // ============================================================
      // NEW: Use RunPod Backend (Async Job Processing)
      // ============================================================
      if (USE_RUNPOD_BACKEND) {
        console.log('🚀 Using RunPod backend for async processing...');

        // Use actual file_path from mediaFile (includes timestamp prefix)
        const filePath = mediaFile.file_path;

        console.log('📁 File path for backend:', filePath);

        // Submit detection job to new backend endpoint
        try {
          setIsProcessing(true);
          await detect(filePath, promptToUse, confidenceThreshold);
          console.log('✅ Detection completed with new endpoint');
        } catch (err) {
          console.error('❌ Detection failed:', err);
          setError('Detection failed. Please try again.');
        } finally {
          setIsProcessing(false);
        }

        console.log('✅ Detection job completed');
        return; // Exit early - processing state handled by hook
      }

      // ============================================================
      // OLD: Use Google Cloud Run Backend (kept for fallback)
      // ============================================================
      console.log('📤 Using old Google Cloud Run backend...');

      const formData = new FormData();

      // Convert image to blob
      const imageResponse = await fetch(imagePreview);
      const imageBlob = await imageResponse.blob();

      formData.append('image', imageBlob, 'image.jpg');
      formData.append('text_prompt', promptToUse);
      formData.append('score_threshold', confidenceThreshold.toString()); // Use user's confidence setting
      formData.append('visualize', 'false');
      formData.append('blur_detections', 'true');
      formData.append('blur_strength', blurIntensity.toString());
      formData.append('blur_type', blurType);

      // Add custom blur areas if any exist
      if (visibleCustomAreas.length > 0) {
        const customAreasData = visibleCustomAreas.map(area => ({
          x: Math.round(area.x),
          y: Math.round(area.y),
          width: Math.round(area.width),
          height: Math.round(area.height)
        }));
        formData.append('custom_blur_areas', JSON.stringify(customAreasData));
        console.log('📦 Added custom blur areas to applyBlur request:', customAreasData);
      }

      console.log('� API Parameters:', {
        prompt: promptToUse,
        strength: blurIntensity,
        type: blurType
      });

      // Debug: Log all form data
      console.log('📋 Complete FormData being sent:');
      for (let [key, value] of formData.entries()) {
        if (key === 'image') {
          console.log(`   ${key}: [Blob ${(value as Blob).size} bytes]`);
        } else {
          console.log(`   ${key}: "${value}"`);
        }
      }

      console.log('📤 Calling API with URL:', `${API_BASE_URL}/process_image`);

      const response = await tryApiCall(formData, 'Blur');

      // The API returns a FileResponse (image file)
      const contentType = response.headers.get('content-type');
      console.log('✅ API Response received');
      console.log('📄 Response content type:', contentType);
      console.log('📋 Response headers:', Object.fromEntries(response.headers.entries()));

      // The response should be an image file when blur_detections=true
      const responseBlob = await response.blob();
      console.log('Response blob size:', responseBlob.size, 'bytes');
      console.log('Response blob type:', responseBlob.type);

      if (responseBlob.size > 0) {
        const imageUrl = URL.createObjectURL(responseBlob);
        console.log('✅ Blur applied successfully! Image URL:', imageUrl);
        console.log('🔄 Setting processedImageUrl state...');
        setProcessedImageUrl(imageUrl);

        // Performance monitoring
        const endTime = performance.now();
        console.log(`⚡ Blur completed in ${Math.round(endTime - startTime)}ms`);

        // Log immediately and auto-save (non-blocking)
        console.log('🖼️ Current processedImageUrl state:', imageUrl);
        console.log('🖼️ Image element src should be:', imageUrl);

        // Auto-save handled by useEffect
      } else {
        throw new Error('Received empty response from blur API');
      }

    } catch (error) {
      console.error('❌ Blur application failed:', error);
      setError(`Blur application failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsProcessing(false);
    }
  };

  // UNIFIED BLUR FUNCTION - Handles both detected objects AND custom areas
  // CLIENT-SIDE BLUR/PIXELATE - Process image in browser using Canvas API
  const applyClientSideEffect = async (
    imageUrl: string,
    regions: Array<{ x: number; y: number; width: number; height: number }>,
    effectType: 'blur' | 'pixelate',
    intensity: number
  ): Promise<string> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.crossOrigin = 'anonymous';

      img.onload = () => {
        // Create canvas with image dimensions
        const canvas = document.createElement('canvas');
        canvas.width = img.naturalWidth;
        canvas.height = img.naturalHeight;
        const ctx = canvas.getContext('2d', { willReadFrequently: true });

        if (!ctx) {
          reject(new Error('Failed to get canvas context'));
          return;
        }

        // Draw original image
        ctx.drawImage(img, 0, 0);

        // Map 0-100 intensity scale to 0-60 actual blur radius (0 = clear, 100 = max)
        const actualBlurRadius = Math.round((intensity / 100) * 60);

        // Apply effect to each region
        regions.forEach(region => {
          const x = Math.max(0, Math.floor(region.x));
          const y = Math.max(0, Math.floor(region.y));
          const width = Math.min(canvas.width - x, Math.ceil(region.width));
          const height = Math.min(canvas.height - y, Math.ceil(region.height));

          if (width <= 0 || height <= 0) return;

          if (effectType === 'blur') {
            // Skip blur for intensity 0 (clear image)
            if (actualBlurRadius === 0) return;

            // Skip very small regions for performance
            if (width < 10 || height < 10) return;

            // Use the mapped blur radius
            const radius = Math.floor(actualBlurRadius);

            // Get image data for the region
            const imageData = ctx.getImageData(x, y, width, height);
            const pixels = imageData.data;

            // Create temporary array for horizontal pass
            const tempData = new Uint8ClampedArray(pixels.length);

            // Horizontal blur pass - optimized
            for (let py = 0; py < height; py++) {
              const rowStart = py * width * 4;

              for (let px = 0; px < width; px++) {
                let r = 0, g = 0, b = 0, a = 0;

                // Sample pixels in horizontal kernel
                const startX = Math.max(0, px - radius);
                const endX = Math.min(width - 1, px + radius);

                for (let kx = startX; kx <= endX; kx++) {
                  const idx = rowStart + kx * 4;
                  r += pixels[idx] ?? 0;
                  g += pixels[idx + 1] ?? 0;
                  b += pixels[idx + 2] ?? 0;
                  a += pixels[idx + 3] ?? 0;
                }

                const outIdx = rowStart + px * 4;
                const actualCount = endX - startX + 1;
                const invCount = 1 / actualCount;
                tempData[outIdx] = r * invCount;
                tempData[outIdx + 1] = g * invCount;
                tempData[outIdx + 2] = b * invCount;
                tempData[outIdx + 3] = a * invCount;
              }
            }

            // Vertical blur pass - optimized
            for (let py = 0; py < height; py++) {
              for (let px = 0; px < width; px++) {
                let r = 0, g = 0, b = 0, a = 0;

                // Sample pixels in vertical kernel
                const startY = Math.max(0, py - radius);
                const endY = Math.min(height - 1, py + radius);

                for (let ky = startY; ky <= endY; ky++) {
                  const idx = (ky * width + px) * 4;
                  r += tempData[idx] ?? 0;
                  g += tempData[idx + 1] ?? 0;
                  b += tempData[idx + 2] ?? 0;
                  a += tempData[idx + 3] ?? 0;
                }

                const outIdx = (py * width + px) * 4;
                const actualCount = endY - startY + 1;
                const invCount = 1 / actualCount;
                pixels[outIdx] = r * invCount;
                pixels[outIdx + 1] = g * invCount;
                pixels[outIdx + 2] = b * invCount;
                pixels[outIdx + 3] = a * invCount;
              }
            }

            // Put blurred data back to canvas
            ctx.putImageData(imageData, x, y);
          } else {
            // Apply pixelation - skip for intensity 0
            if (actualBlurRadius === 0) return;

            const pixelSize = Math.max(1, Math.floor(actualBlurRadius / 3)); // Convert blur radius to pixel size

            // Get image data
            const imageData = ctx.getImageData(x, y, width, height);
            const pixels = imageData.data;

            // Pixelate by sampling and filling blocks
            for (let py = 0; py < height; py += pixelSize) {
              for (let px = 0; px < width; px += pixelSize) {
                // Get color of pixel at block start
                const index = (py * width + px) * 4;
                const r = pixels[index];
                const g = pixels[index + 1];
                const b = pixels[index + 2];
                const a = pixels[index + 3];

                // Fill entire block with this color
                for (let by = 0; by < pixelSize && py + by < height; by++) {
                  for (let bx = 0; bx < pixelSize && px + bx < width; bx++) {
                    const blockIndex = ((py + by) * width + (px + bx)) * 4;
                    pixels[blockIndex] = r ?? 0;
                    pixels[blockIndex + 1] = g ?? 0;
                    pixels[blockIndex + 2] = b ?? 0;
                    pixels[blockIndex + 3] = a ?? 255;
                  }
                }
              }
            }

            ctx.putImageData(imageData, x, y);
          }
        });

        // Convert canvas to blob
        canvas.toBlob((blob) => {
          if (blob) {
            const url = URL.createObjectURL(blob);
            resolve(url);
          } else {
            reject(new Error('Failed to create blob from canvas'));
          }
        }, 'image/png');
      };

      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = imageUrl;
    });
  };

  const blurAllDetectedObjects = async (effectType?: 'blur' | 'pixelate', intensity?: number) => {
    console.log('🎯 CLIENT-SIDE: Processing ALL content locally (detected objects + custom areas)');

    if (!imagePreview) {
      setError('No image available for processing');
      return;
    }

    // Use provided values or fall back to state values
    const currentEffectType = effectType || blurType;
    const currentIntensity = intensity || blurIntensity;

    const visibleDetectedObjects = detectedObjects.filter(obj => obj.selected && obj.bbox);
    const visibleCustomAreas = customBlurAreas.filter(area => area.visible);

    if (visibleDetectedObjects.length === 0 && visibleCustomAreas.length === 0) {
      setError('No visible objects or custom areas to blur. Please detect objects or create custom areas first.');
      return;
    }

    console.log(`🎯 CLIENT-SIDE: ${visibleDetectedObjects.length} detected objects + ${visibleCustomAreas.length} custom areas`);
    console.log(`🎨 Effect type: ${currentEffectType}, Intensity: ${currentIntensity}`);

    setIsProcessing(true);
    setError(null);

    try {
      // Prepare regions for processing
      const regions = [
        // AI detected object regions (bounding boxes)
        ...visibleDetectedObjects.map(obj => ({
          x: obj.bbox![0],
          y: obj.bbox![1],
          width: obj.bbox![2] - obj.bbox![0],
          height: obj.bbox![3] - obj.bbox![1]
        })),
        // Custom blur area regions
        ...visibleCustomAreas.map(area => ({
          x: area.x,
          y: area.y,
          width: area.width,
          height: area.height
        }))
      ];

      console.log('📦 Processing regions:', regions);

      // Apply effect client-side with explicit effect type
      const processedUrl = await applyClientSideEffect(
        imagePreview,
        regions,
        currentEffectType,
        currentIntensity
      );

      setProcessedImageUrl(processedUrl);
      console.log('✅ Client-side processing complete!');

    } catch (error) {
      console.error('❌ Failed to process image:', error);
      setError(`Failed to process image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsProcessing(false);
    }
  };

  // Apply full screen blur CLIENT-SIDE (no backend call)
  const applyFullScreenBlur = async (effectType?: 'blur' | 'pixelate', intensity?: number) => {
    if (!imagePreview) {
      setError('No image available for processing');
      return;
    }

    // Use provided values or fall back to state values
    const currentEffectType = effectType || blurType;
    const currentIntensity = intensity || blurIntensity;

    setIsProcessing(true);
    setError(null);

    try {
      console.log('🌀 Applying FULL SCREEN effect client-side...');
      console.log('📊 Full screen parameters:', {
        effectType: currentEffectType,
        intensity: currentIntensity
      });

      // Apply blur/pixelate to entire image by creating one region covering the whole image
      const img = new Image();
      img.crossOrigin = 'anonymous';

      img.onload = async () => {
        // Create a single region covering the entire image
        const fullScreenRegion = [{
          x: 0,
          y: 0,
          width: img.naturalWidth,
          height: img.naturalHeight
        }];

        console.log(`📐 Full screen region: ${img.naturalWidth}x${img.naturalHeight}`);

        // Use the same client-side effect function with explicit effect type
        const processedUrl = await applyClientSideEffect(
          imagePreview,
          fullScreenRegion,
          currentEffectType,
          currentIntensity
        );

        setProcessedImageUrl(processedUrl);
        console.log('✅ Full screen blur applied successfully!');
        setIsProcessing(false);
      };

      img.onerror = () => {
        console.error('❌ Failed to load image for full screen blur');
        setError('Failed to load image for full screen blur');
        setIsProcessing(false);
      };

      img.src = imagePreview;

    } catch (error) {
      console.error('❌ Full screen blur failed:', error);
      setError(`Full screen blur failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setIsProcessing(false);
    }
  };

  // Handle click on image (disabled custom blur for now)
  const handleImageClick = (event: React.MouseEvent<HTMLImageElement>) => {
    // Custom blur areas not supported by current API
    console.log('Image clicked at:', event.clientX, event.clientY);
  };

  // Handle image panning in actual size mode
  const handleImagePanStart = (e: React.MouseEvent) => {
    if (imageZoomMode !== 'actual') return;

    setIsImagePanning(true);
    setImagePanStart({
      x: e.clientX,
      y: e.clientY
    });
    e.preventDefault();
  };

  const handleImagePanMove = (e: MouseEvent) => {
    if (!isImagePanning || !imageContainerRef.current) return;

    const deltaX = imagePanStart.x - e.clientX;
    const deltaY = imagePanStart.y - e.clientY;

    imageContainerRef.current.scrollLeft += deltaX;
    imageContainerRef.current.scrollTop += deltaY;

    setImagePanStart({
      x: e.clientX,
      y: e.clientY
    });
  };

  const handleImagePanEnd = () => {
    setIsImagePanning(false);
  };

  // Add/remove pan event listeners
  useEffect(() => {
    if (isImagePanning) {
      document.addEventListener('mousemove', handleImagePanMove);
      document.addEventListener('mouseup', handleImagePanEnd);
      return () => {
        document.removeEventListener('mousemove', handleImagePanMove);
        document.removeEventListener('mouseup', handleImagePanEnd);
      };
    }
  }, [isImagePanning, imagePanStart]);

  // Handle custom area resizing - FIXED VERSION
  const handleCustomAreaResize = (areaId: string, handle: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    console.log(`🔧 Starting resize for area ${areaId} from handle ${handle}`);

    const img = imageRef.current;
    if (!img) {
      console.error('❌ No image element found for resize');
      return;
    }

    const startMouseX = e.clientX;
    const startMouseY = e.clientY;

    // Find the area being resized
    const startArea = customBlurAreas.find(area => area.id === areaId);
    if (!startArea) return;

    // Get image display dimensions and natural dimensions for proper scaling
    const imgRect = img.getBoundingClientRect();
    const scaleX = img.naturalWidth / imgRect.width;
    const scaleY = img.naturalHeight / imgRect.height;

    console.log(`🔧 Image scaling: scaleX=${scaleX.toFixed(2)}, scaleY=${scaleY.toFixed(2)}`);
    console.log(`🔧 Start area:`, startArea);

    const handleMouseMove = (moveEvent: MouseEvent) => {
      // Calculate mouse movement in display pixels, then convert to natural image pixels
      const displayDeltaX = moveEvent.clientX - startMouseX;
      const displayDeltaY = moveEvent.clientY - startMouseY;
      const deltaX = displayDeltaX * scaleX;
      const deltaY = displayDeltaY * scaleY;

      // Start with the original area
      let newArea = { ...startArea };

      // Update area based on handle being dragged (with proper minimum size handling)
      const minSize = 50; // Minimum 50px to prevent position jumping

      switch (handle) {
        // Corner handles
        case 'se': // Bottom-right corner
          newArea.width = Math.max(minSize, startArea.width + deltaX);
          newArea.height = Math.max(minSize, startArea.height + deltaY);
          break;
        case 'sw': // Bottom-left corner
          {
            const newWidth = Math.max(minSize, startArea.width - deltaX);
            const widthChange = newWidth - startArea.width;
            newArea.x = startArea.x - widthChange; // Adjust x based on actual width change
            newArea.width = newWidth;
            newArea.height = Math.max(minSize, startArea.height + deltaY);
          }
          break;
        case 'ne': // Top-right corner
          {
            const newHeight = Math.max(minSize, startArea.height - deltaY);
            const heightChange = newHeight - startArea.height;
            newArea.y = startArea.y - heightChange; // Adjust y based on actual height change
            newArea.width = Math.max(minSize, startArea.width + deltaX);
            newArea.height = newHeight;
          }
          break;
        case 'nw': // Top-left corner
          {
            const newWidth = Math.max(minSize, startArea.width - deltaX);
            const newHeight = Math.max(minSize, startArea.height - deltaY);
            const widthChange = newWidth - startArea.width;
            const heightChange = newHeight - startArea.height;
            newArea.x = startArea.x - widthChange;
            newArea.y = startArea.y - heightChange;
            newArea.width = newWidth;
            newArea.height = newHeight;
          }
          break;

        // Edge handles
        case 'n': // Top edge
          {
            const newHeight = Math.max(minSize, startArea.height - deltaY);
            const heightChange = newHeight - startArea.height;
            newArea.y = startArea.y - heightChange;
            newArea.height = newHeight;
          }
          break;
        case 's': // Bottom edge
          newArea.height = Math.max(minSize, startArea.height + deltaY);
          break;
        case 'w': // Left edge
          {
            const newWidth = Math.max(minSize, startArea.width - deltaX);
            const widthChange = newWidth - startArea.width;
            newArea.x = startArea.x - widthChange;
            newArea.width = newWidth;
          }
          break;
        case 'e': // Right edge
          newArea.width = Math.max(minSize, startArea.width + deltaX);
          break;
      }

      // Ensure area stays within natural image bounds
      newArea.x = Math.max(0, Math.min(newArea.x, img.naturalWidth - minSize));
      newArea.y = Math.max(0, Math.min(newArea.y, img.naturalHeight - minSize));
      newArea.width = Math.max(minSize, Math.min(newArea.width, img.naturalWidth - newArea.x));
      newArea.height = Math.max(minSize, Math.min(newArea.height, img.naturalHeight - newArea.y));

      // Update the area (preserve visibility)
      setCustomBlurAreas(prev =>
        prev.map(area => area.id === areaId ? { ...newArea, visible: area.visible || true } : area)
      );
    };

    const handleMouseUp = () => {
      console.log(`🔧 Finished resizing area ${areaId}`);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      // Auto-save handled by useEffect
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };







  return (
    <div className="min-h-screen bg-[#0a192f] text-white font-sans antialiased">
      {/* Header Bar - Professional & Minimal */}
      <div className="bg-[#0a192f]/95 backdrop-blur-xl border-b border-[#1e3a6f]/50 px-6 lg:px-8 py-4 sticky top-0 z-50">
        <div className="relative flex items-center max-w-[2000px] mx-auto">
          {/* Left Side - Back Button */}
          <button
            onClick={onBack}
            className="group flex items-center gap-2 text-gray-400 hover:text-white transition-all duration-200 px-3 py-2 rounded-lg hover:bg-white/5 active:scale-95"
          >
            <ArrowLeft className="h-4 w-4 transition-transform group-hover:-translate-x-0.5" />
            <span className="text-sm font-medium hidden sm:inline tracking-tight">Dashboard</span>
            <span className="text-sm font-medium sm:hidden">Back</span>
          </button>

          {/* Center - Logo and Title - Refined Typography */}
          <div className="absolute left-1/2 -translate-x-1/2 flex items-center gap-3 cursor-pointer group" onClick={() => window.location.href = '/dashboard'}>
            <img
              src="/photo-de-profil-linkedin.png"
              alt="Guardiavision Logo"
              className="h-9 w-auto opacity-95 group-hover:opacity-100 transition-opacity"
            />
            <h1 className="text-lg sm:text-2xl font-semibold text-white tracking-tight">
              <span className="hidden sm:inline">Guardia<span className="text-emerald-400">Vision</span> <span className="font-light text-gray-400">Studio</span></span>
              <span className="sm:hidden font-light">Studio</span>
            </h1>
          </div>

          {/* Right Side - User Dropdown */}
          <div className="ml-auto relative" ref={dropdownRef}>
            <button
              onClick={() => setShowUserDropdown(!showUserDropdown)}
              className="w-9 h-9 bg-gradient-to-br from-purple-500 to-purple-700 rounded-full flex items-center justify-center text-white font-semibold text-sm hover:shadow-lg hover:shadow-purple-500/50 transition-all duration-200 active:scale-95 ring-2 ring-purple-500/20"
            >
              {getUserInitials()}
            </button>

            {/* Dropdown Menu - Professional */}
            {showUserDropdown && (
              <div className="absolute right-0 mt-3 w-56 bg-[#0f1629]/95 backdrop-blur-xl border border-[#1e3a6f]/50 rounded-xl shadow-2xl shadow-black/50 z-50 overflow-hidden">
                <div className="py-2">
                  <button
                    onClick={handleWorkspaceClick}
                    className="flex items-center w-full px-4 py-2.5 text-sm font-medium text-gray-300 hover:text-white hover:bg-white/5 transition-all duration-150"
                  >
                    <LayoutGrid className="h-4 w-4 mr-3 opacity-70" />
                    Workspace
                  </button>
                  <button
                    onClick={handleAccountSettings}
                    className="flex items-center w-full px-4 py-2.5 text-sm font-medium text-gray-300 hover:text-white hover:bg-white/5 transition-all duration-150"
                  >
                    <Settings className="h-4 w-4 mr-3 opacity-70" />
                    Account Settings
                  </button>
                  <div className="border-t border-[#1e3a6f]/50 my-2"></div>
                  <button
                    onClick={handleSignOut}
                    className="flex items-center w-full px-4 py-2.5 text-sm font-medium text-red-400 hover:text-red-300 hover:bg-red-500/10 transition-all duration-150"
                  >
                    <LogOut className="h-4 w-4 mr-3 opacity-70" />
                    Sign Out
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Layout - Professional Spacing */}
      <div className="flex flex-col lg:flex-row h-[calc(100vh-73px)]">
        {/* Left Sidebar - Control Panel with Refined Design */}
        <div className="w-full lg:w-[340px] bg-[#0f1629]/50 backdrop-blur-sm border-b lg:border-b-0 lg:border-r border-[#1e3a6f]/40 flex flex-col max-h-[40vh] lg:max-h-none overflow-y-auto lg:overflow-visible">
          <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-700/50 scrollbar-track-transparent">
            {/* Multiple Detection Prompts - Mobile Responsive */}
            <div className="p-2 lg:p-3 border-b border-[#1e3a6f] relative">
              <div className="mb-1 lg:mb-2 flex items-center justify-between">
                <label className="text-white text-xs font-medium flex items-center">
                  <span className="text-purple-400 mr-1 text-xs">●</span>
                  <span className="hidden sm:inline">Detection Prompts</span>
                  <span className="sm:hidden">Prompts</span>
                  <span className="text-xs text-purple-400 ml-1 font-normal hidden lg:inline">Start here</span>
                </label>
                {textPrompts.length < 5 && (
                  <button
                    onClick={addNewPrompt}
                    className="text-xs text-purple-400 hover:text-purple-300 transition-colors flex items-center"
                  >
                    <span className="mr-1">+</span>Add Prompt
                  </button>
                )}
              </div>

              {/* Multiple Prompt Inputs */}
              <div className="space-y-2">
                {textPrompts.map((prompt, index) => (
                  <div key={index} className="relative">
                    {/* Laser border animation - continuous traveling light */}
                    <div className="prompt-laser-border"></div>
                    <div className="flex items-center space-x-2">
                      <input
                        type="text"
                        value={prompt}
                        onChange={(e) => updatePrompt(index, e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && canDetect()) {
                            detectOnly();
                          }
                        }}
                        placeholder={`Enter what to detect ${index + 1} (e.g., person, face, car)...`}
                        className={`relative flex-1 px-3 py-2 rounded-lg text-white placeholder-gray-400 focus:outline-none text-sm transition-all duration-300 ${
                          hasUserTyped
                            ? 'bg-[#1e3a6f]/30 border border-[#1e3a6f] focus:border-purple-500'
                            : 'bg-[#1e3a6f]/30 border-2 border-transparent focus:border-blue-400/50 z-10'
                        }`}
                      />
                      {textPrompts.length > 1 && (
                        <button
                          onClick={() => removePrompt(index)}
                          className="text-red-400 hover:text-red-300 transition-colors p-1"
                          title="Remove prompt"
                        >
                          ×
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              <p className={`text-xs mt-2 flex items-center transition-all duration-300 ${
                hasUserTyped ? 'text-gray-400' : 'text-blue-400'
              }`}>
                <span className="mr-1">⚡</span>
                Press Enter to detect objects from all prompts
              </p>
            </div>

            {/* Detection Controls - Compressed */}
            <div className="p-3 border-b border-[#1e3a6f]">
              <div className="space-y-2">
                <ToggleSwitch
                  enabled={detectionCategories.fullScreenBlur}
                  onChange={(enabled) => {
                    setDetectionCategories(prev => ({ ...prev, fullScreenBlur: enabled }));
                    if (enabled && imagePreview) {
                      applyFullScreenBlur();
                    } else if (!enabled) {
                      // Reset to original image when disabled
                      setProcessedImageUrl('');
                    }
                  }}
                  label="Full screen blur"
                  hasWarning={true}
                />
              </div>
            </div>

            {/* Custom Blur Areas - Compressed */}
            <div className="p-2 border-b border-[#1e3a6f]">
              <div className="space-y-2">
                <button
                  onClick={addCustomBlurArea}
                  disabled={!imagePreview}
                  className="w-full flex items-center justify-center space-x-1 px-2 py-1.5 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white rounded text-xs font-medium transition-colors"
                >
                  <Plus className="h-3 w-3" />
                  <span>Add Custom Area</span>
                </button>
              </div>
            </div>

            {/* Blur Intensity - Professional 0-100 Scale */}
            <div className="p-3 border-b border-[#1e3a6f]">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  <label className="text-white text-sm font-medium">
                    Blur Intensity
                  </label>
                  <Tooltip text="0 = Clear image, 100 = Maximum blur" />
                </div>
                <input
                  type="number"
                  min="0"
                  max="100"
                  value={pendingBlurIntensity}
                  onChange={(e) => {
                    const val = Math.max(0, Math.min(100, Number(e.target.value) || 0));
                    handleIntensityChange(val);
                  }}
                  className="w-14 px-2 py-1 bg-gray-800/50 border border-gray-600/50 rounded text-white text-xs text-center focus:outline-none focus:ring-1 focus:ring-purple-500 focus:border-purple-500 transition-all"
                />
              </div>
              <div className="space-y-2">
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={pendingBlurIntensity}
                  onChange={(e) => handleIntensityChange(Number(e.target.value))}
                  className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                  style={{
                    background: `linear-gradient(to right, #8B5CF6 0%, #8B5CF6 ${pendingBlurIntensity}%, #374151 ${pendingBlurIntensity}%, #374151 100%)`
                  }}
                />
                <div className="flex justify-between text-[10px] text-gray-500 font-medium">
                  <span>Clear (0)</span>
                  <span>Maximum (100)</span>
                </div>
              </div>
            </div>

            {/* Confidence Threshold - Enhanced with manual input */}
            <div className="p-3 border-b border-[#1e3a6f]">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  <label className="text-white text-sm font-medium">
                    Detection Confidence
                  </label>
                  <Tooltip text="Filters detections by AI confidence level" />
                </div>
                <input
                  type="number"
                  min="0"
                  max="100"
                  value={Math.round(confidenceThreshold * 100)}
                  onChange={(e) => {
                    const val = Math.max(0, Math.min(100, Number(e.target.value) || 0));
                    updateConfidenceThreshold(val / 100);
                  }}
                  className="w-14 px-2 py-1 bg-gray-800/50 border border-gray-600/50 rounded text-white text-xs text-center focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500 transition-all"
                />
              </div>
              <div className="space-y-2">
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.01"
                  value={confidenceThreshold}
                  onChange={(e) => updateConfidenceThreshold(Number(e.target.value))}
                  className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                  style={{
                    background: `linear-gradient(to right, #10B981 0%, #10B981 ${confidenceThreshold * 100}%, #374151 ${confidenceThreshold * 100}%, #374151 100%)`
                  }}
                />
                <div className="flex justify-between text-[10px] text-gray-500 font-medium">
                  <span>Low (0%)</span>
                  <span>High (100%)</span>
                </div>
                <p className="text-[10px] text-gray-400 mt-1">
                  Filtered: {allDetectedObjects.length - detectedObjects.length} objects
                </p>
              </div>
            </div>

            {/* Blur effect - Compressed */}
            <div className="p-2 border-b border-[#1e3a6f]">
              <div className="mb-2">
                <label className="text-white text-xs font-medium">Blur/pixilate effect</label>
              </div>
              <div className="flex gap-2">
                <button
                  onClick={() => {
                    console.log('🔄 Switching to BLUR mode with immediate effect');
                    handleBlurTypeToggle('blur');
                  }}
                  disabled={!hasBlurrableObjects() || isProcessing}
                  className={`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    !hasBlurrableObjects() || isProcessing
                      ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                      : blurType === 'blur'
                      ? 'bg-purple-600 text-white hover:bg-purple-700'
                      : 'bg-[#1e3a6f]/30 text-gray-300 hover:bg-[#1e3a6f]'
                  }`}
                >
                  {isProcessing ? 'Processing...' : 'Blur'}
                </button>
                <button
                  onClick={() => {
                    console.log('🔄 Switching to PIXELATE mode with immediate effect');
                    handleBlurTypeToggle('pixelate');
                  }}
                  disabled={!hasBlurrableObjects() || isProcessing}
                  className={`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    !hasBlurrableObjects() || isProcessing
                      ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                      : blurType === 'pixelate'
                      ? 'bg-purple-600 text-white hover:bg-purple-700'
                      : 'bg-[#1e3a6f]/30 text-gray-300 hover:bg-[#1e3a6f]'
                  }`}
                >
                  {isProcessing ? 'Processing...' : 'Pixelate'}
                </button>
              </div>
            </div>



            {/* API Status - Professional Design */}
            <div className="p-3 border-b border-[#1e3a6f]/40 bg-gradient-to-r from-transparent to-[#1e3a6f]/10">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2.5">
                  <div className="relative">
                    <div className={`w-2 h-2 rounded-full ${
                      apiStatus === 'online' ? 'bg-emerald-400 shadow-lg shadow-emerald-500/50' :
                      apiStatus === 'offline' ? 'bg-red-400 shadow-lg shadow-red-500/50' :
                      'bg-amber-400 animate-pulse shadow-lg shadow-amber-500/50'
                    }`} />
                    {apiStatus === 'online' && (
                      <div className="absolute inset-0 w-2 h-2 rounded-full bg-emerald-400 animate-ping opacity-75" />
                    )}
                  </div>
                  <div className="flex items-center gap-1">
                    <span className="text-sm font-medium text-gray-300">
                      {
                        apiStatus === 'online' ? 'AI Online' :
                        apiStatus === 'offline' ? 'AI Offline' :
                        'Checking AI...'
                      }
                    </span>
                    <Tooltip text="Real-time AI detection service availability" />
                  </div>
                </div>
                {apiStatus === 'offline' && (
                  <button
                    onClick={() => window.location.reload()}
                    className="text-xs font-medium text-blue-400 hover:text-blue-300 transition-all duration-200 px-2 py-1 rounded hover:bg-blue-500/10"
                    title="Refresh to check API status"
                  >
                    Retry
                  </button>
                )}
              </div>
            </div>





            {/* Error Display */}
            {error && (
              <div className="p-4">
                <div className="p-3 bg-red-900/20 border border-red-500/30 rounded-lg">
                  <p className="text-red-400 text-xs mb-2">{error}</p>
                  {/* Show upgrade button for credit-related errors */}
                  {error.includes('Insufficient credits') && (
                    <div className="mt-3 flex gap-2">
                      <button
                        onClick={() => window.open('/dashboard/billing', '_blank')}
                        className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded-lg transition-colors"
                      >
                        Upgrade Plan
                      </button>
                      <button
                        onClick={() => window.open('/dashboard', '_blank')}
                        className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-xs rounded-lg transition-colors"
                      >
                        View Dashboard
                      </button>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Main Content Area - Mobile Responsive */}
        <div className="flex-1 bg-[#0a192f] flex flex-col min-h-[60vh] lg:min-h-0">
          {/* Loading State */}
          {isLoadingImage && (
            <div className="flex-1 flex items-center justify-center bg-[#0f1629] p-4">
              <div className="text-center text-gray-300">
                <div className="text-6xl mb-4 animate-pulse">🖼️</div>
                <h3 className="text-xl font-semibold mb-2">Loading Image...</h3>
                <p className="text-gray-400">Validating image URL and accessibility</p>
              </div>
            </div>
          )}

          {/* Error Display / Upload Interface */}
          {!isLoadingImage && imageLoadError && (
            <div className="flex-1 flex items-center justify-center bg-[#0f1629] p-4">
              <div className="text-center text-gray-300">
                <div className="text-6xl mb-4">🖼️</div>
                <h3 className="text-xl font-semibold mb-2">Upload an Image</h3>
                <p className="text-gray-400 mb-6">Get started by uploading an image to process</p>

                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  className="hidden"
                />

                <div className="space-y-4">
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    className="px-8 py-4 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors font-medium text-lg"
                  >
                    📁 Choose Image File
                  </button>

                  <p className="text-sm text-gray-400">
                    Supports JPG, PNG, GIF, WebP files
                  </p>

                  {originalImageUrl && !originalImageUrl.includes('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==') && (
                    <div className="mt-6 pt-4 border-t border-gray-600">
                      <p className="text-xs text-gray-500 mb-2">Or try loading the original image:</p>
                      <button
                        onClick={loadImageAsBase64}
                        className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors text-sm"
                      >
                        🔄 Load Original Image
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Image Display - Mobile Responsive */}
          {!isLoadingImage && imagePreview && !imageLoadError && (
            <div className="flex-1 bg-[#0f1629] relative">
              {/* Smart Scaling Container - Mobile Responsive */}
              <div
                ref={imageContainerRef}
                className="absolute inset-2 lg:inset-4 overflow-auto rounded-lg bg-[#0a192f]/50 border border-[#1e3a6f]/30 studio-image-container"
                style={{ cursor: imageZoomMode === 'actual' ? (isImagePanning ? 'grabbing' : 'grab') : 'default' }}
              >
                {/* Centered Image Wrapper with Smart Scaling - FIXED: Allow scrolling to edges in actual size mode */}
                <div className={`w-full h-full p-4 ${imageZoomMode === 'fit' ? 'flex items-center justify-center' : ''}`}>
                  <div className="relative inline-block">
                    {/* Processing Overlay - REMOVED to allow non-blocking user interaction */}
                    {/* Processing happens in background, results appear when ready */}

                    <img
                      ref={imageRef}
                      src={processedImageUrl || imagePreview}
                      alt={mediaFile.original_filename}
                      className="block rounded-lg shadow-2xl"
                      onMouseDown={handleImagePanStart}
                      style={{
                        cursor: imageZoomMode === 'actual' ? (isImagePanning ? 'grabbing' : 'grab') : 'default',
                        ...(imageZoomMode === 'fit' ? {
                          // Mobile responsive sizing
                          maxWidth: window.innerWidth < 1024 ? 'calc(100vw - 32px)' : 'calc(100vw - 680px)', // Account for sidebars on desktop
                          maxHeight: window.innerWidth < 1024 ? 'calc(60vh - 100px)' : 'calc(100vh - 200px)', // Account for header + padding
                          objectFit: 'contain', // Maintain aspect ratio while fitting container
                          width: 'auto',
                          height: 'auto'
                        } : {
                          maxWidth: 'none',
                          maxHeight: 'none',
                          width: 'auto',
                          height: 'auto'
                        }),
                        minWidth: window.innerWidth < 1024 ? '200px' : '300px',
                        minHeight: window.innerWidth < 1024 ? '150px' : '200px'
                      }}
                  onClick={handleImageClick}
                  onLoad={() => {
                    console.log('🖼️ Image loaded successfully. Current src:', processedImageUrl || imagePreview);
                    console.log('🖼️ processedImageUrl:', processedImageUrl);
                    console.log('🖼️ imagePreview:', imagePreview);
                  }}
                  onError={(e) => {
                    console.error('❌ Failed to load image. Current src:', e.currentTarget.src);
                    console.error('❌ processedImageUrl:', processedImageUrl);
                    console.error('❌ imagePreview:', imagePreview);

                    // If processed image fails to load, fallback to original
                    if (processedImageUrl && e.currentTarget.src === processedImageUrl) {
                      console.log('🔄 Processed image failed, falling back to original');
                      setProcessedImageUrl('');
                      e.currentTarget.src = imagePreview;
                    } else {
                      console.error('❌ Original image also failed to load');
                      setError('Image failed to display. Please try reloading or upload a new image.');
                    }
                  }}
                />

                {/* AI DETECTION OVERLAY - Shows filtered detections (respects confidence threshold and visibility) */}
                {(detectedObjects.length > 0 || customBlurAreas.length > 0) && imageRef.current && (() => {
                  const img = imageRef.current;
                  if (!img) return null;

                  // PERFORMANCE FIX: Calculate scale factors ONCE for all overlays
                  const imgRect = img.getBoundingClientRect();
                  const scaleX = imgRect.width / img.naturalWidth;
                  const scaleY = imgRect.height / img.naturalHeight;

                  return (
                    <div className="absolute inset-0 pointer-events-none">
                      {/* AI Detected Objects */}
                      {detectedObjects.filter(obj => obj.selected && obj.bbox).map((obj, index) => {
                        const containerRect = img.parentElement?.getBoundingClientRect();
                        if (!containerRect) return null;

                        // Convert bbox coordinates to display coordinates
                        // bbox format: [x1, y1, x2, y2] (top-left and bottom-right corners)
                        const [x1, y1, x2, y2] = obj.bbox;
                        const displayX = x1 * scaleX;
                        const displayY = y1 * scaleY;
                        const displayWidth = (x2 - x1) * scaleX;
                        const displayHeight = (y2 - y1) * scaleY;

                        return (
                          <div
                            key={obj.id}
                            className="absolute rounded-lg group hover:border-opacity-80 transition-opacity duration-150"
                            style={{
                              left: `${displayX}px`,
                              top: `${displayY}px`,
                              width: `${displayWidth}px`,
                              height: `${displayHeight}px`,
                              borderColor: obj.color,
                              borderWidth: '1.5px',
                              borderStyle: 'dashed',
                              backgroundColor: 'transparent',
                              zIndex: 10 + index,
                              pointerEvents: 'auto',
                            }}
                          >
                            {/* Instance name label - REMOVED for professional appearance */}
                            {/* Label is now hidden to reduce visual clutter */}

                            {/* Delete button - Visible on hover */}
                            <button
                              className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-sm font-bold opacity-0 group-hover:opacity-100 transition-all duration-200 border-2 border-white shadow-lg"
                              onClick={(e) => {
                                e.stopPropagation();
                                deleteDetectedObject(obj.id);
                              }}
                              title={`Delete ${obj.label} (${obj.confidence}%)`}
                              style={{ zIndex: 2 }}
                            >
                              ×
                            </button>
                          </div>
                        );
                      })}

                      {/* Custom blur areas */}
                      {customBlurAreas.filter(area => area.visible).map((area) => {
                        // Convert area coordinates to display coordinates
                        const displayX = area.x * scaleX;
                        const displayY = area.y * scaleY;
                        const displayWidth = area.width * scaleX;
                        const displayHeight = area.height * scaleY;

                        return (
                          <div
                            key={area.id}
                          className="absolute border-2 border-dashed rounded-lg pointer-events-auto cursor-move group hover:border-purple-400 select-none transition-opacity duration-150"
                          style={{
                            left: `${displayX}px`,
                            top: `${displayY}px`,
                            width: `${displayWidth}px`,
                            height: `${displayHeight}px`,
                            borderColor: '#8B5CF6',
                            backgroundColor: 'rgba(139, 92, 246, 0.1)',
                            minWidth: '20px',
                            minHeight: '20px',
                          }}
                          onMouseDown={(e) => {
                            e.stopPropagation();
                            console.log('🖱️ Starting drag for area:', area.id);
                            handleCustomAreaDrag(area.id, area.x, area.y, e);
                          }}
                        >
                          {/* Label with 4-digit ID */}
                          <div className="absolute -top-8 left-0 px-2 py-1 text-xs font-semibold text-white bg-purple-600 rounded shadow-lg z-10">
                            #{area.id}
                          </div>

                          {/* Delete button - Always visible with better styling */}
                          <button
                            className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-sm font-bold opacity-80 hover:opacity-100 transition-all duration-200 border-2 border-white shadow-lg z-20"
                            onClick={(e) => {
                              e.stopPropagation();
                              setCustomBlurAreas(prev => prev.filter(a => a.id !== area.id));
                              console.log('🗑️ Deleted custom area:', area.id);
                              // Auto-save handled by useEffect
                            }}
                            title="Delete custom area"
                          >
                            ×
                          </button>

                          {/* Corner Resize Handles - Professional & Subtle */}
                          <div
                            className="absolute -bottom-0.5 -right-0.5 w-2 h-2 bg-purple-500/80 rounded-full cursor-se-resize opacity-0 group-hover:opacity-60 transition-all duration-200 hover:!opacity-90 hover:scale-125 border border-white/30 z-10"
                            onMouseDown={(e) => {
                              e.stopPropagation();
                              console.log('🔧 SE resize handle clicked');
                              handleCustomAreaResize(area.id, 'se', e);
                            }}
                          />
                          <div
                            className="absolute -top-0.5 -right-0.5 w-2 h-2 bg-purple-500/80 rounded-full cursor-ne-resize opacity-0 group-hover:opacity-60 transition-all duration-200 hover:!opacity-90 hover:scale-125 border border-white/30 z-10"
                            onMouseDown={(e) => {
                              e.stopPropagation();
                              console.log('🔧 NE resize handle clicked');
                              handleCustomAreaResize(area.id, 'ne', e);
                            }}
                          />
                          <div
                            className="absolute -bottom-0.5 -left-0.5 w-2 h-2 bg-purple-500/80 rounded-full cursor-sw-resize opacity-0 group-hover:opacity-60 transition-all duration-200 hover:!opacity-90 hover:scale-125 border border-white/30 z-10"
                            onMouseDown={(e) => {
                              e.stopPropagation();
                              console.log('🔧 SW resize handle clicked');
                              handleCustomAreaResize(area.id, 'sw', e);
                            }}
                          />
                          <div
                            className="absolute -top-0.5 -left-0.5 w-2 h-2 bg-purple-500/80 rounded-full cursor-nw-resize opacity-0 group-hover:opacity-60 transition-all duration-200 hover:!opacity-90 hover:scale-125 border border-white/30 z-10"
                            onMouseDown={(e) => {
                              e.stopPropagation();
                              console.log('🔧 NW resize handle clicked');
                              handleCustomAreaResize(area.id, 'nw', e);
                            }}
                          />

                          {/* Edge Resize Handles - Professional & Subtle */}
                          {/* Top edge */}
                          <div
                            className="absolute -top-0.5 left-1/2 transform -translate-x-1/2 w-4 h-1.5 bg-purple-500/80 rounded-full cursor-n-resize opacity-0 group-hover:opacity-50 transition-all duration-200 hover:!opacity-80 hover:h-2 border border-white/20 z-10"
                            onMouseDown={(e) => {
                              e.stopPropagation();
                              console.log('🔧 N resize handle clicked');
                              handleCustomAreaResize(area.id, 'n', e);
                            }}
                          />
                          {/* Bottom edge */}
                          <div
                            className="absolute -bottom-0.5 left-1/2 transform -translate-x-1/2 w-4 h-1.5 bg-purple-500/80 rounded-full cursor-s-resize opacity-0 group-hover:opacity-50 transition-all duration-200 hover:!opacity-80 hover:h-2 border border-white/20 z-10"
                            onMouseDown={(e) => {
                              e.stopPropagation();
                              console.log('🔧 S resize handle clicked');
                              handleCustomAreaResize(area.id, 's', e);
                            }}
                          />
                          {/* Left edge */}
                          <div
                            className="absolute -left-0.5 top-1/2 transform -translate-y-1/2 w-1.5 h-4 bg-purple-500/80 rounded-full cursor-w-resize opacity-0 group-hover:opacity-50 transition-all duration-200 hover:!opacity-80 hover:w-2 border border-white/20 z-10"
                            onMouseDown={(e) => {
                              e.stopPropagation();
                              console.log('🔧 W resize handle clicked');
                              handleCustomAreaResize(area.id, 'w', e);
                            }}
                          />
                          {/* Right edge */}
                          <div
                            className="absolute -right-0.5 top-1/2 transform -translate-y-1/2 w-1.5 h-4 bg-purple-500/80 rounded-full cursor-e-resize opacity-0 group-hover:opacity-50 transition-all duration-200 hover:!opacity-80 hover:w-2 border border-white/20 z-10"
                            onMouseDown={(e) => {
                              e.stopPropagation();
                              console.log('🔧 E resize handle clicked');
                              handleCustomAreaResize(area.id, 'e', e);
                            }}
                          />

                          {/* Center text */}
                          <div className="absolute inset-0 flex items-center justify-center text-purple-600 text-xs font-semibold opacity-0 group-hover:opacity-100 transition-opacity">
                            Drag to move
                          </div>
                        </div>
                        );
                      })}
                    </div>
                  );
                })()}
                  </div>
                </div>

                {/* Draggable Multi-Image Navigation Panel */}
                {!loadingImages && allUserImages.length > 1 && (
                  <div
                    className="absolute z-20 bg-black/90 backdrop-blur-sm rounded-lg border border-white/20 shadow-2xl cursor-move"
                    style={{
                      left: `50%`,
                      bottom: `20px`,
                      transform: `translateX(-50%) translateX(${panelPosition.x}px) translateY(${panelPosition.y}px)`,
                      maxWidth: '400px',
                      zIndex: 30
                    }}
                    onMouseDown={(e) => {
                      e.stopPropagation();
                      handleMouseDown(e);
                    }}
                  >
                    {/* Drag Handle */}
                    <div className="flex items-center justify-center py-1 border-b border-white/10">
                      <div className="flex space-x-1">
                        <div className="w-1 h-1 bg-white/40 rounded-full"></div>
                        <div className="w-1 h-1 bg-white/40 rounded-full"></div>
                        <div className="w-1 h-1 bg-white/40 rounded-full"></div>
                        <div className="w-1 h-1 bg-white/40 rounded-full"></div>
                        <div className="w-1 h-1 bg-white/40 rounded-full"></div>
                      </div>
                    </div>

                    <div className="px-4 py-3">
                      <div className="flex items-center space-x-3">
                        <span className="text-white/70 text-xs font-medium whitespace-nowrap">
                          {currentImageIndex + 1} of {allUserImages.length}
                        </span>

                        {/* Navigation Arrows */}
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            const prevIndex = currentImageIndex > 0 ? currentImageIndex - 1 : allUserImages.length - 1;
                            const prevImage = allUserImages[prevIndex];
                            if (prevImage) navigateToImage(prevImage);
                          }}
                          className="p-1 rounded hover:bg-white/20 transition-colors"
                          title="Previous image"
                        >
                          <ChevronLeft className="h-4 w-4 text-white/70" />
                        </button>

                        <div className="flex space-x-1 max-w-xs overflow-x-auto scrollbar-hide">
                          {allUserImages.map((image, index) => (
                            <button
                              key={image.id}
                              onClick={(e) => {
                                e.stopPropagation();
                                navigateToImage(image);
                              }}
                              className={`relative flex-shrink-0 w-12 h-12 rounded-lg overflow-hidden border-2 transition-all duration-200 ${
                                image.id === mediaFile.id
                                  ? 'border-blue-400 ring-2 ring-blue-400/50 scale-110'
                                  : 'border-white/20 hover:border-white/40 opacity-60 hover:opacity-80 hover:scale-105'
                              }`}
                              title={image.original_filename}
                            >
                              <img
                                src={(image as any).url || image.metadata?.originalUrl || ''}
                                alt={image.original_filename}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  // Fallback to a placeholder if image fails to load
                                  e.currentTarget.style.display = 'none';
                                }}
                              />
                              {image.id === mediaFile.id && (
                                <div className="absolute inset-0 bg-blue-400/20 flex items-center justify-center">
                                  <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                                </div>
                              )}
                            </button>
                          ))}
                        </div>

                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            const nextIndex = currentImageIndex < allUserImages.length - 1 ? currentImageIndex + 1 : 0;
                            const nextImage = allUserImages[nextIndex];
                            if (nextImage) navigateToImage(nextImage);
                          }}
                          className="p-1 rounded hover:bg-white/20 transition-colors"
                          title="Next image"
                        >
                          <ChevronRight className="h-4 w-4 text-white/70" />
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Right Sidebar - Matching dashboard design */}
        <div className="w-80 bg-[#0f1629] border-l border-[#1e3a6f] flex flex-col">
          <div className="flex-1 overflow-y-auto">
            {/* Image Tools */}
            {imagePreview && (
              <div className="p-2 border-b border-[#1e3a6f]">
                <h3 className="text-xs font-semibold text-gray-300 mb-2">Image Tools</h3>

                {/* Zoom Control - Compressed */}
                <div className="mb-2">
                  <label className="text-xs font-medium text-gray-400 mb-1 block">Display Mode</label>
                  <div className="flex rounded-lg bg-[#1e3a6f]/30 p-1">
                    <button
                      onClick={() => {
                        console.log('🔄 Switching to fit mode - preserving all state');
                        setImageZoomMode('fit');
                        // State preservation: All detected objects, custom areas, and processed images are maintained
                      }}
                      className={`flex-1 px-3 py-1.5 text-xs font-medium rounded-md transition-colors ${
                        imageZoomMode === 'fit'
                          ? 'bg-purple-600 text-white'
                          : 'text-gray-400 hover:text-white hover:bg-[#1e3a6f]/50'
                      }`}
                    >
                      Fit to View
                    </button>
                    <button
                      onClick={() => {
                        console.log('🔄 Switching to actual size mode - preserving all state');
                        setImageZoomMode('actual');
                        // State preservation: All detected objects, custom areas, and processed images are maintained
                      }}
                      className={`flex-1 px-3 py-1.5 text-xs font-medium rounded-md transition-colors ${
                        imageZoomMode === 'actual'
                          ? 'bg-purple-600 text-white'
                          : 'text-gray-400 hover:text-white hover:bg-[#1e3a6f]/50'
                      }`}
                    >
                      Actual Size
                    </button>
                  </div>
                </div>

                <div className="space-y-2">
                  {/* Detect Button */}
                  <button
                    onClick={detectOnly}
                    disabled={!canDetect() || isProcessing}
                    className={`w-full flex items-center justify-center space-x-2 px-3 py-2 rounded text-sm font-medium transition-colors ${
                      !canDetect() || isProcessing
                        ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                        : 'bg-blue-600 hover:bg-blue-700 text-white'
                    }`}
                  >
                    {isProcessing ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Target className="h-4 w-4" />
                    )}
                    <span>{isProcessing ? 'Detecting...' : 'Detect'}</span>
                  </button>

                  {/* Single Blur Button - Blurs ALL (detected objects + custom areas) */}
                  <button
                    onClick={() => {
                      console.log('🎯 SINGLE BLUR BUTTON CLICKED!');
                      console.log('📊 Will blur ALL content:', {
                        detectedObjects: detectedObjects.filter(obj => obj.selected).length,
                        customAreas: customBlurAreas.filter(area => area.visible).length,
                        blurIntensity,
                        blurType
                      });
                      blurAllDetectedObjects(); // This function now handles both types
                    }}
                    disabled={!hasBlurrableObjects() || isProcessing}
                    className={`w-full flex items-center justify-center space-x-2 px-3 py-2 rounded text-sm font-medium transition-colors ${
                      !hasBlurrableObjects() || isProcessing
                        ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                        : 'bg-green-600 hover:bg-green-700 text-white'
                    }`}
                  >
                    {isProcessing ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Zap className="h-4 w-4" />
                    )}
                    <span>{isProcessing ? 'Blurring...' : 'Blur All'}</span>
                  </button>

                  {/* Reset to Original */}
                  {processedImageUrl && (
                    <button
                      onClick={() => {
                        console.log('🔄 Resetting to original image');
                        setProcessedImageUrl('');
                      }}
                      className="w-full flex items-center justify-center space-x-2 px-3 py-2 bg-[#1e3a6f] hover:bg-[#2563eb] text-white rounded text-sm font-medium transition-colors"
                    >
                      <span>🔄 Show Original</span>
                    </button>
                  )}
                </div>
              </div>
            )}

            {/* Studio Actions - Compressed */}
            <div className="p-2 border-b border-[#1e3a6f]">
              <div className="space-y-2">

                {/* Action Buttons */}
                <div className="flex gap-2">
                  <button
                    onClick={handleDoneClick}
                    disabled={!processedImageUrl}
                    className="w-full px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg text-sm font-medium transition-colors"
                  >
                    Done
                  </button>
                </div>
              </div>
            </div>

            {/* Custom Objects - Compressed */}
            <div className="p-2 border-b border-[#1e3a6f]">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white text-xs font-medium">Custom Objects</h3>
                <span className="text-xs text-gray-400">({customBlurAreas.length})</span>
              </div>

              {customBlurAreas.length > 0 ? (
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {customBlurAreas.map((area) => (
                    <div
                      key={area.id}
                      className="flex items-center justify-between p-2 bg-purple-600/20 rounded-lg border border-purple-600/30"
                    >
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 rounded-full bg-purple-500" />
                        <span className="text-white text-xs font-medium">
                          Custom #{area.id}
                        </span>
                      </div>

                      <div className="flex items-center space-x-1">
                        <button
                          onClick={() => toggleCustomAreaVisibility(area.id)}
                          className={`p-1 rounded transition-colors ${
                            area.visible
                              ? 'text-green-400 hover:text-green-300'
                              : 'text-gray-500 hover:text-gray-400'
                          }`}
                          title={area.visible ? 'Hide custom area' : 'Show custom area'}
                        >
                          {area.visible ? <Eye className="w-3 h-3" /> : <EyeOff className="w-3 h-3" />}
                        </button>
                        <button
                          onClick={() => {
                            setCustomBlurAreas(prev => prev.filter(a => a.id !== area.id));
                            console.log('🗑️ Deleted custom area from sidebar:', area.id);
                            // Auto-save handled by useEffect
                          }}
                          className="p-1 text-red-400 hover:text-red-300 transition-colors"
                          title="Delete custom area"
                        >
                          <Trash2 className="w-3 h-3" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-400 text-xs">No custom areas created</p>
              )}
            </div>

            {/* Detected Objects List - Compressed */}
            <div className="p-2 border-b border-[#1e3a6f]">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white text-xs font-medium">Detected Objects</h3>
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-gray-400">({detectedObjects.length})</span>
                  {allDetectedObjects.length > 0 && (
                    <button
                      onClick={clearAllDetections}
                      className="text-xs text-red-400 hover:text-red-300 transition-colors"
                      title="Clear all detections"
                    >
                      Clear All
                    </button>
                  )}

                </div>
              </div>

              {detectedObjects.length > 0 ? (
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {detectedObjects.map((obj) => (
                    <div
                      key={obj.id}
                      className="flex items-center justify-between p-2 bg-[#1e3a6f]/30 rounded-lg border border-[#1e3a6f]"
                    >
                      <div className="flex items-center space-x-2 flex-1">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: obj.color }}
                        />
                        <div className="flex-1 min-w-0">
                          <p className="text-white text-xs font-medium truncate">
                            {obj.label}
                          </p>
                          <p className="text-gray-400 text-xs">
                            {obj.confidence}% confidence
                            {obj.prompt && (
                              <span className="ml-1 text-blue-400">• {obj.prompt}</span>
                            )}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-1">
                        <button
                          onClick={() => toggleObjectVisibility(obj.id)}
                          className={`p-1 rounded transition-colors ${
                            obj.selected
                              ? 'text-green-400 hover:text-green-300'
                              : 'text-gray-500 hover:text-gray-400'
                          }`}
                          title={obj.selected ? 'Hide object' : 'Show object'}
                        >
                          {obj.selected ? <Eye className="w-3 h-3" /> : <EyeOff className="w-3 h-3" />}
                        </button>
                        <button
                          onClick={() => deleteDetectedObject(obj.id)}
                          className="p-1 text-red-400 hover:text-red-300 transition-colors"
                          title="Delete object"
                        >
                          <Trash2 className="w-3 h-3" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-400 text-xs">No objects detected yet</p>
              )}
            </div>

            {/* Image details */}
            <div className="p-4">
              <h3 className="text-white text-sm font-medium mb-3">Image details</h3>
              <div className="space-y-2 text-xs">
                <div className="flex justify-between">
                  <span className="text-gray-400">File name</span>
                  <span className="text-white">{mediaFile.original_filename || 'ps.17306369'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Size</span>
                  <span className="text-white">24.82 KB</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Duration</span>
                  <span className="text-white">-</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">File type</span>
                  <span className="text-white">JPEG</span>
                </div>
              </div>
            </div>


          </div>
        </div>
      </div>

      {/* Save/Download Dialog */}
      {showSaveDialog && processedImageUrl && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-[#0f1629] border border-[#1e3a6f] rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-white mb-4">Save Your Work</h3>

            {/* Image Preview */}
            <div className="mb-4">
              <div className="relative w-full h-32 bg-[#1e3a6f]/30 rounded-lg overflow-hidden">
                <img
                  src={processedImageUrl}
                  alt="Processed result"
                  className="w-full h-full object-contain"
                />
              </div>
            </div>

            {/* Image Info */}
            <div className="mb-6 text-sm text-[#94a3b8]">
              <p><strong className="text-white">File:</strong> processed_{mediaFile.original_filename}</p>
              <p><strong className="text-white">Original:</strong> {mediaFile.original_filename}</p>
              <p><strong className="text-white">Size:</strong> {(mediaFile.file_size / 1024 / 1024).toFixed(2)} MB</p>
              <p><strong className="text-white">Type:</strong> {mediaFile.mime_type || 'image/jpeg'}</p>
            </div>

            <p className="text-[#94a3b8] text-sm mb-6">
              Choose whether to download the image only or save your processing settings for future access.
            </p>

            {/* Action Buttons */}
            <div className="flex gap-3">
              <button
                onClick={() => setShowSaveDialog(false)}
                className="flex-1 px-4 py-2 bg-[#1e3a6f]/30 hover:bg-[#1e3a6f] text-white rounded-lg text-sm font-medium transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleDownloadOnly}
                className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-colors"
              >
                Download Only
              </button>
              <button
                onClick={handleSaveAndDownload}
                className="flex-1 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm font-medium transition-colors"
              >
                Save & Download
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Insufficient Credits Modal */}
      <InsufficientCreditsModal
        isOpen={showInsufficientCreditsModal}
        onClose={() => setShowInsufficientCreditsModal(false)}
        currentCredits={insufficientCreditsData.current}
        requiredCredits={insufficientCreditsData.required}
      />

      {/* Laser Border Animation CSS */}
      <style jsx>{`
        .prompt-laser-border {
          position: absolute;
          inset: 0;
          border-radius: 8px;
          padding: 2px;
          background: linear-gradient(
            45deg,
            transparent 30%,
            rgba(139, 92, 246, 0.3) 40%,
            rgba(139, 92, 246, 1) 50%,
            rgba(139, 92, 246, 0.3) 60%,
            transparent 70%
          );
          background-size: 300% 300%;
          animation: laser-glow 4s ease-in-out infinite;
          box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
          z-index: 0;
        }

        .prompt-laser-border::before {
          content: '';
          position: absolute;
          inset: 2px;
          background: #1e3a6f;
          border-radius: 6px;
          z-index: 1;
        }

        @keyframes laser-glow {
          0%, 100% {
            background-position: 0% 0%;
            box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
          }
          25% {
            background-position: 100% 0%;
            box-shadow: 0 0 30px rgba(139, 92, 246, 0.5);
          }
          50% {
            background-position: 100% 100%;
            box-shadow: 0 0 40px rgba(139, 92, 246, 0.7);
          }
          75% {
            background-position: 0% 100%;
            box-shadow: 0 0 30px rgba(139, 92, 246, 0.5);
          }
        }
      `}</style>
    </div>
  );
}