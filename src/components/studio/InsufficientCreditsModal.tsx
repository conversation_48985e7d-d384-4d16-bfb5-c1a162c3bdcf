'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Zap, Crown, ArrowRight, Sparkles } from 'lucide-react';

interface InsufficientCreditsModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentCredits: number;
  requiredCredits: number;
}

export function InsufficientCreditsModal({ 
  isOpen, 
  onClose, 
  currentCredits, 
  requiredCredits 
}: InsufficientCreditsModalProps) {
  const [isUpgrading, setIsUpgrading] = useState(false);

  const handleUpgrade = () => {
    setIsUpgrading(true);
    // Open billing page in new tab
    window.open('/dashboard/billing', '_blank');
    // Close modal after a short delay
    setTimeout(() => {
      setIsUpgrading(false);
      onClose();
    }, 1000);
  };

  const handleViewDashboard = () => {
    window.open('/dashboard', '_blank');
    onClose();
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={onClose}
          >
            {/* Modal */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              transition={{ type: "spring", duration: 0.5 }}
              className="relative bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden border border-gray-700"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Animated background elements */}
              <div className="absolute inset-0 overflow-hidden">
                <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full blur-xl"></div>
                <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-full blur-xl"></div>
              </div>

              {/* Close button */}
              <button
                onClick={onClose}
                className="absolute top-4 right-4 z-10 p-2 rounded-full bg-gray-800/50 hover:bg-gray-700/50 transition-colors"
              >
                <X className="h-4 w-4 text-gray-400 hover:text-white" />
              </button>

              {/* Content */}
              <div className="relative p-8 text-center">
                {/* Icon with animation */}
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: "spring", duration: 0.6 }}
                  className="mx-auto w-20 h-20 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center mb-6 shadow-lg"
                >
                  <motion.div
                    animate={{ rotate: [0, -10, 10, -10, 0] }}
                    transition={{ delay: 0.5, duration: 0.6 }}
                  >
                    <Zap className="h-10 w-10 text-white" />
                  </motion.div>
                </motion.div>

                {/* Title with sparkles */}
                <div className="flex items-center justify-center mb-4">
                  <Sparkles className="h-5 w-5 text-yellow-400 mr-2" />
                  <h2 className="text-2xl font-bold text-white">Oops! Out of Credits</h2>
                  <Sparkles className="h-5 w-5 text-yellow-400 ml-2" />
                </div>

                {/* Message */}
                <p className="text-gray-300 mb-2 leading-relaxed">
                  You need <span className="font-bold text-orange-400">{requiredCredits} credits</span> for detection
                </p>
                <p className="text-gray-400 text-sm mb-6">
                  You currently have <span className="font-bold text-red-400">{currentCredits} credits</span>
                </p>

                {/* Motivational message */}
                <div className="bg-gradient-to-r from-purple-900/30 to-blue-900/30 rounded-lg p-4 mb-6 border border-purple-500/20">
                  <div className="flex items-center justify-center mb-2">
                    <Crown className="h-5 w-5 text-yellow-400 mr-2" />
                    <span className="text-sm font-semibold text-yellow-400">Unlock Your Potential!</span>
                  </div>
                  <p className="text-xs text-gray-300 leading-relaxed">
                    Upgrade now and get <span className="font-bold text-green-400">hundreds of credits</span> to power your AI detection workflow!
                  </p>
                </div>

                {/* Action buttons */}
                <div className="space-y-3">
                  {/* Primary upgrade button */}
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={handleUpgrade}
                    disabled={isUpgrading}
                    className="w-full bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center group disabled:opacity-50"
                  >
                    {isUpgrading ? (
                      <div className="flex items-center">
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                        Opening Plans...
                      </div>
                    ) : (
                      <>
                        <Crown className="h-5 w-5 mr-2" />
                        Upgrade Plan Now
                        <ArrowRight className="h-5 w-5 ml-2 group-hover:translate-x-1 transition-transform" />
                      </>
                    )}
                  </motion.button>

                  {/* Secondary button */}
                  <button
                    onClick={handleViewDashboard}
                    className="w-full bg-gray-700/50 hover:bg-gray-600/50 text-gray-300 hover:text-white font-medium py-3 px-6 rounded-xl transition-all duration-300 border border-gray-600 hover:border-gray-500"
                  >
                    View Dashboard
                  </button>
                </div>

                {/* Footer note */}
                <p className="text-xs text-gray-500 mt-4">
                  💡 Pro tip: Upgrade to Pro and get <span className="text-green-400 font-semibold">3,500 credits</span> monthly!
                </p>
              </div>
            </motion.div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
