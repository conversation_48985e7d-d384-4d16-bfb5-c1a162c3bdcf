'use client';

import { useState, useRef, useEffect } from 'react';
import { 
  Play, 
  Pause, 
  Download, 
  Setting<PERSON>, 
  Eye, 
  EyeOff, 
  RotateCcw,
  Maximize,
  ArrowLeft,
  Loader2
} from 'lucide-react';

interface MediaFile {
  id: string;
  original_filename: string;
  file_path: string;
  file_type: 'image' | 'video';
  file_size: number;
  mime_type: string;
  metadata: any;
  created_at: string;
}

interface ImageStudioProps {
  mediaFile: MediaFile;
}

type BlurType = 'blur' | 'pixelation';
type DetectionMode = 'full_body' | 'face' | 'vehicle' | 'license_plate' | 'custom';

export function ImageStudio({ mediaFile }: ImageStudioProps) {
  // State management
  const [isProcessing, setIsProcessing] = useState(false);
  const [processedImageUrl, setProcessedImageUrl] = useState<string | null>(null);
  const [showOriginal, setShowOriginal] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // AI Processing settings
  const [textPrompt, setTextPrompt] = useState('person');
  const [scoreThreshold, setScoreThreshold] = useState(0.5);
  const [visualize, setVisualize] = useState(true);
  const [blurDetections, setBlurDetections] = useState(false);
  const [blurStrength, setBlurStrength] = useState(25);
  const [blurType, setBlurType] = useState<BlurType>('blur');
  const [detectionMode, setDetectionMode] = useState<DetectionMode>('face');

  // UI state
  const [showSettings, setShowSettings] = useState(true);
  const imageRef = useRef<HTMLImageElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Get original image URL
  const originalImageUrl = mediaFile.metadata?.originalUrl || mediaFile.file_path;

  // Process image with AI
  const processImage = async () => {
    if (!originalImageUrl) {
      setError('Original image not found');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      // Fetch the original image
      const imageResponse = await fetch(originalImageUrl);
      if (!imageResponse.ok) {
        throw new Error('Failed to fetch original image');
      }
      
      const imageBlob = await imageResponse.blob();
      
      // Create form data for AI processing
      const formData = new FormData();
      formData.append('image', imageBlob, mediaFile.original_filename);
      formData.append('text_prompt', getPromptForDetectionMode());
      formData.append('score_threshold', scoreThreshold.toString());
      formData.append('visualize', visualize.toString());
      formData.append('blur_detections', blurDetections.toString());
      formData.append('blur_strength', blurStrength.toString());

      console.log('🤖 Processing image with settings:', {
        textPrompt: getPromptForDetectionMode(),
        scoreThreshold,
        visualize,
        blurDetections,
        blurStrength,
        detectionMode
      });

      // Send to AI processing endpoint
      const response = await fetch('/api/ai/process-image', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Processing failed: ${response.status}`);
      }

      // Check if response is an image
      const contentType = response.headers.get('content-type');
      if (contentType?.startsWith('image/')) {
        const processedBlob = await response.blob();
        const processedUrl = URL.createObjectURL(processedBlob);
        setProcessedImageUrl(processedUrl);
        console.log('✅ Image processed successfully');
      } else {
        const result = await response.json();
        console.log('✅ Processing result:', result);
        // Handle JSON response if needed
      }

    } catch (error: any) {
      console.error('❌ Error processing image:', error);
      setError(`Processing failed: ${error.message}`);
    } finally {
      setIsProcessing(false);
    }
  };

  // Get prompt based on detection mode
  const getPromptForDetectionMode = () => {
    switch (detectionMode) {
      case 'face':
        return 'face';
      case 'full_body':
        return 'person';
      case 'vehicle':
        return 'car vehicle';
      case 'license_plate':
        return 'license plate';
      case 'custom':
        return textPrompt;
      default:
        return 'person';
    }
  };

  // Download processed image
  const downloadImage = () => {
    if (!processedImageUrl) return;

    const link = document.createElement('a');
    link.href = processedImageUrl;
    link.download = `processed_${mediaFile.original_filename}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Reset to original
  const resetToOriginal = () => {
    setProcessedImageUrl(null);
    setError(null);
  };

  // Toggle fullscreen
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    return (bytes / (1024 * 1024)).toFixed(2);
  };

  return (
    <div className="flex h-full bg-gray-900 text-white">
      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => window.history.back()}
              className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Back to Dashboard</span>
            </button>
            <div className="h-6 w-px bg-gray-600" />
            <h1 className="text-lg font-semibold truncate max-w-md" title={mediaFile.original_filename}>
              {mediaFile.original_filename}
            </h1>
          </div>

          <div className="flex items-center space-x-2">
            {processedImageUrl && (
              <>
                <button
                  onClick={() => setShowOriginal(!showOriginal)}
                  className="flex items-center space-x-2 px-3 py-1.5 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
                >
                  {showOriginal ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  <span>{showOriginal ? 'Show Processed' : 'Show Original'}</span>
                </button>
                <button
                  onClick={resetToOriginal}
                  className="flex items-center space-x-2 px-3 py-1.5 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
                >
                  <RotateCcw className="h-4 w-4" />
                  <span>Reset</span>
                </button>
                <button
                  onClick={downloadImage}
                  className="flex items-center space-x-2 px-3 py-1.5 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
                >
                  <Download className="h-4 w-4" />
                  <span>Download</span>
                </button>
              </>
            )}
            <button
              onClick={toggleFullscreen}
              className="p-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
            >
              <Maximize className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Image Display Area */}
        <div className="flex-1 flex items-center justify-center p-4 bg-gray-800">
          {error && (
            <div className="absolute top-20 left-1/2 transform -translate-x-1/2 bg-red-900/90 text-red-200 px-4 py-2 rounded-lg z-10">
              {error}
            </div>
          )}
          
          <div className={`relative ${isFullscreen ? 'w-full h-full' : 'max-w-4xl max-h-full'}`}>
            <img
              ref={imageRef}
              src={showOriginal ? originalImageUrl : (processedImageUrl || originalImageUrl)}
              alt={mediaFile.original_filename}
              className="w-full h-full object-contain rounded-lg shadow-2xl"
              onError={() => setError('Failed to load image')}
            />
            
            {isProcessing && (
              <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-lg">
                <div className="text-center">
                  <Loader2 className="h-12 w-12 animate-spin mx-auto mb-4" />
                  <p className="text-lg font-medium">Processing with AI...</p>
                  <p className="text-sm text-gray-400 mt-2">This may take a few moments</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="w-80 bg-gray-800 border-l border-gray-700 p-6 overflow-y-auto">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold">AI Blur Settings</h2>
            <button
              onClick={() => setShowSettings(false)}
              className="text-gray-400 hover:text-white"
            >
              <Settings className="h-5 w-5" />
            </button>
          </div>

          {/* Detection Mode */}
          <div className="mb-6">
            <label className="block text-sm font-medium mb-3">Detection Mode</label>
            <div className="space-y-2">
              {[
                { value: 'face', label: 'Face' },
                { value: 'full_body', label: 'Full Body' },
                { value: 'vehicle', label: 'Vehicle' },
                { value: 'license_plate', label: 'License Plate' },
                { value: 'custom', label: 'Custom' }
              ].map((mode) => (
                <label key={mode.value} className="flex items-center space-x-2">
                  <input
                    type="radio"
                    name="detectionMode"
                    value={mode.value}
                    checked={detectionMode === mode.value}
                    onChange={(e) => setDetectionMode(e.target.value as DetectionMode)}
                    className="text-blue-600"
                  />
                  <span className="text-sm">{mode.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Custom Prompt */}
          {detectionMode === 'custom' && (
            <div className="mb-6">
              <label className="block text-sm font-medium mb-2">Custom Detection Prompt</label>
              <textarea
                value={textPrompt}
                onChange={(e) => setTextPrompt(e.target.value)}
                placeholder="Describe what you want to detect and blur..."
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={3}
              />
              <p className="text-xs text-gray-400 mt-1">
                Example: "person holding phone", "red car", "text on sign"
              </p>
            </div>
          )}

          {/* Blur Settings */}
          <div className="mb-6">
            <label className="flex items-center space-x-2 mb-4">
              <input
                type="checkbox"
                checked={blurDetections}
                onChange={(e) => setBlurDetections(e.target.checked)}
                className="text-blue-600"
              />
              <span className="text-sm font-medium">Apply Blur to Detections</span>
            </label>

            {blurDetections && (
              <div className="space-y-4 ml-6">
                <div>
                  <label className="block text-sm font-medium mb-2">Blur Type</label>
                  <select
                    value={blurType}
                    onChange={(e) => setBlurType(e.target.value as BlurType)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="blur">Blur</option>
                    <option value="pixelation">Pixelation</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Blur Strength: {blurStrength}
                  </label>
                  <input
                    type="range"
                    min="1"
                    max="50"
                    value={blurStrength}
                    onChange={(e) => setBlurStrength(parseInt(e.target.value))}
                    className="w-full"
                  />
                </div>
              </div>
            )}
          </div>

          {/* Detection Settings */}
          <div className="mb-6">
            <label className="block text-sm font-medium mb-2">
              Detection Threshold: {scoreThreshold}
            </label>
            <input
              type="range"
              min="0.1"
              max="1"
              step="0.1"
              value={scoreThreshold}
              onChange={(e) => setScoreThreshold(parseFloat(e.target.value))}
              className="w-full"
            />
            <p className="text-xs text-gray-400 mt-1">
              Higher values = more confident detections only
            </p>
          </div>

          {/* Visualization */}
          <div className="mb-6">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={visualize}
                onChange={(e) => setVisualize(e.target.checked)}
                className="text-blue-600"
              />
              <span className="text-sm font-medium">Show Detection Boxes</span>
            </label>
            <p className="text-xs text-gray-400 mt-1">
              Display bounding boxes around detected objects
            </p>
          </div>

          {/* Process Button */}
          <button
            onClick={processImage}
            disabled={isProcessing}
            className="w-full py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
          >
            {isProcessing ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Processing...</span>
              </>
            ) : (
              <>
                <Play className="h-4 w-4" />
                <span>Process Image</span>
              </>
            )}
          </button>

          {/* File Details */}
          <div className="mt-8 pt-6 border-t border-gray-700">
            <h3 className="text-sm font-medium mb-3">File Details</h3>
            <div className="space-y-2 text-sm text-gray-400">
              <div className="flex justify-between">
                <span>Size:</span>
                <span>{formatFileSize(mediaFile.file_size)} MB</span>
              </div>
              <div className="flex justify-between">
                <span>Type:</span>
                <span>{mediaFile.mime_type}</span>
              </div>
              <div className="flex justify-between">
                <span>Uploaded:</span>
                <span>{new Date(mediaFile.created_at).toLocaleDateString()}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Settings Toggle (when panel is hidden) */}
      {!showSettings && (
        <button
          onClick={() => setShowSettings(true)}
          className="fixed top-20 right-4 p-3 bg-gray-800 hover:bg-gray-700 rounded-lg shadow-lg transition-colors z-10"
        >
          <Settings className="h-5 w-5" />
        </button>
      )}
    </div>
  );
}