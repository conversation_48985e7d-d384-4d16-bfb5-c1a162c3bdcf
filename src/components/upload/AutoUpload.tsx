'use client';

import { useRef, useState } from 'react';
import { useUser } from '@clerk/nextjs';

interface UploadedFile {
  id: string;
  filename: string;
  type: 'image' | 'video';
  size: number;
  url: string;
  created_at: string;
}

interface AutoUploadProps {
  onUploadComplete?: (file: UploadedFile) => void;
  onUploadStart?: () => void;
  onUploadError?: (error: string) => void;
  accept?: string;
  maxSizeMB?: number;
  className?: string;
}

export function AutoUpload({
  onUploadComplete,
  onUploadStart,
  onUploadError,
  accept = "image/*,video/*",
  maxSizeMB = 50,
  className = ""
}: AutoUploadProps) {
  const { user } = useUser();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !user) return;

    console.log('📁 File selected:', file.name);

    // Validate file size
    const fileSizeMB = file.size / (1024 * 1024);
    if (fileSizeMB > maxSizeMB) {
      const error = `File too large: ${fileSizeMB.toFixed(1)}MB. Max size: ${maxSizeMB}MB`;
      console.error('❌', error);
      onUploadError?.(error);
      return;
    }

    // Start upload immediately
    await uploadFile(file);
    
    // Reset input so same file can be selected again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const uploadFile = async (file: File) => {
    setUploading(true);
    setUploadProgress(0);
    onUploadStart?.();

    try {
      console.log('🚀 Starting direct upload for:', file.name);

      // Step 1: Get signed upload URL
      console.log('📝 Step 1: Requesting signed upload URL...');
      const urlResponse = await fetch('/api/upload-url', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fileName: file.name,
          fileType: file.type,
          fileSize: file.size
        })
      });

      if (!urlResponse.ok) {
        const errorData = await urlResponse.json();
        throw new Error(errorData.error || 'Failed to get upload URL');
      }

      const { uploadUrl, filePath } = await urlResponse.json();
      console.log('✅ Got signed upload URL');
      setUploadProgress(25);

      // Step 2: Upload directly to Supabase Storage
      console.log('📤 Step 2: Uploading directly to Supabase...');
      const uploadResponse = await fetch(uploadUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type,
        },
      });

      if (!uploadResponse.ok) {
        const responseText = await uploadResponse.text();
        console.error('❌ Direct upload failed:', responseText);
        throw new Error(`Upload failed: ${uploadResponse.status} ${uploadResponse.statusText}`);
      }

      console.log('✅ File uploaded directly to Supabase');
      setUploadProgress(75);

      // Step 3: Complete upload by saving to database
      console.log('💾 Step 3: Saving to database...');
      const completeResponse = await fetch('/api/upload-complete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          filePath,
          originalFileName: file.name,
          fileType: file.type.startsWith('video/') ? 'video' : 'image',
          fileSize: file.size,
          mimeType: file.type
        }),
      });

      if (!completeResponse.ok) {
        const errorData = await completeResponse.json();
        throw new Error(errorData.error || 'Failed to complete upload');
      }

      const result = await completeResponse.json();
      console.log('✅ Upload completed successfully');
      setUploadProgress(100);

      if (result.success) {
        // Transform the response to match expected format
        const fileData = {
          id: result.file.id,
          filename: result.file.filename,
          type: result.file.type as 'video' | 'image',
          size: result.file.size,
          url: result.file.url,
          created_at: result.file.created_at
        };

        onUploadComplete?.(fileData);
      } else {
        throw new Error(result.error || 'Upload failed');
      }

    } catch (error: any) {
      console.error('❌ Upload error:', error);
      onUploadError?.(error.message);
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  const triggerFileSelect = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={`relative ${className}`}>
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        onChange={handleFileSelect}
        className="hidden"
        disabled={uploading || !user}
      />

      <button
        onClick={triggerFileSelect}
        disabled={uploading || !user}
        className={`
          relative overflow-hidden transition-all duration-200
          ${uploading 
            ? 'bg-blue-600 cursor-not-allowed' 
            : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 cursor-pointer'
          }
          ${!user ? 'opacity-50 cursor-not-allowed' : ''}
          text-white font-medium py-3 px-6 rounded-lg shadow-lg
          flex items-center justify-center space-x-2
        `}
      >
        {uploading ? (
          <>
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
            <span>Uploading... {uploadProgress}%</span>
          </>
        ) : (
          <>
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            <span>Upload Media</span>
          </>
        )}

        {/* Progress bar */}
        {uploading && (
          <div className="absolute bottom-0 left-0 h-1 bg-white/30 w-full">
            <div 
              className="h-full bg-white transition-all duration-300 ease-out"
              style={{ width: `${uploadProgress}%` }}
            />
          </div>
        )}
      </button>

      {!user && (
        <p className="text-sm text-gray-400 mt-2">
          Please sign in to upload files
        </p>
      )}
    </div>
  );
}
