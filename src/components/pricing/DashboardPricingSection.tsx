"use client"

import { useState, useEffect } from 'react'
import { PricingToggle } from './PricingToggle'
import { PricingCard } from './PricingCard'
import { TrustedBySection } from './TrustedBySection'
import { FeaturesComparison } from './FeaturesComparison'
import { motion } from 'framer-motion'

interface PricingPlan {
  planId: string
  name: string
  description: string
  price: {
    monthly: number
    yearly: number
  }
  stripePriceId?: {
    monthly: string
    yearly: string
  }
  credits: number
  features: Array<{ text: string; included: boolean }>
  isPopular?: boolean
  buttonText: string
  buttonVariant: string
}

export function DashboardPricingSection() {
  const [isYearly, setIsYearly] = useState(true)
  const [plans, setPlans] = useState<PricingPlan[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchPricingPlans()
  }, [])

  const fetchPricingPlans = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/pricing/plans')
      const data = await response.json()

      if (data.success && data.plans) {
        setPlans(data.plans)
        setError(null)
      } else {
        console.error('Failed to fetch pricing plans:', data.error)
        setError('Failed to load pricing plans')
      }
    } catch (err: any) {
      console.error('Error fetching pricing plans:', err)
      setError('Failed to load pricing plans')
    } finally {
      setLoading(false)
    }
  }

  const handleToggle = (yearly: boolean) => {
    setIsYearly(yearly)
  }

  return (
    <div className="py-8 bg-navy">
      <div className="max-w-7xl mx-auto px-6 sm:px-8 lg:px-12">
        <div className="mx-auto max-w-3xl text-center">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-3xl font-bold tracking-tight text-white sm:text-4xl"
          >
            Choose Your Plan
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mt-4 text-lg text-gray-400"
          >
            Select the plan that best fits your needs
          </motion.p>
        </div>

        <PricingToggle onToggle={handleToggle} />

        {loading ? (
          <div className="mt-8 flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-4 border-purple-600/30 border-t-purple-600 mx-auto mb-3"></div>
              <p className="text-gray-400 text-sm">Loading pricing plans...</p>
            </div>
          </div>
        ) : error ? (
          <div className="mt-8 flex items-center justify-center py-12">
            <div className="text-center text-red-400">
              <p>{error}</p>
              <button
                onClick={fetchPricingPlans}
                className="mt-4 px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white text-sm"
              >
                Retry
              </button>
            </div>
          </div>
        ) : (
          <div className="mt-8 grid gap-6 md:grid-cols-4">
            {plans.map((plan) => (
              <PricingCard
                key={plan.planId}
                name={plan.name}
                description={plan.description}
                price={plan.price}
                credits={plan.credits}
                features={plan.features}
                buttonText={plan.buttonText}
                buttonLink="#"
                buttonVariant={plan.buttonVariant}
                isYearly={isYearly}
                isPopular={plan.isPopular}
                clerkPlanId={plan.planId}
                stripePriceId={
                  plan.stripePriceId
                    ? isYearly
                      ? plan.stripePriceId.yearly
                      : plan.stripePriceId.monthly
                    : undefined
                }
              />
            ))}
          </div>
        )}

        <div className="mt-16">
          <TrustedBySection />
        </div>

        <div className="mt-16">
          <FeaturesComparison />
        </div>
      </div>
    </div>
  )
}
