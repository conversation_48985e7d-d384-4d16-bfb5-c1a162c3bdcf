// src/components/DirectUpload.tsx
'use client';

import { useState } from 'react';

interface UploadProps {
  onUploadComplete: (file: any) => void;
  onUploadError: (error: string) => void;
}

export default function DirectUpload({ onUploadComplete, onUploadError }: UploadProps) {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    console.log('🚀 Starting direct upload for:', file.name);
    setUploading(true);
    setProgress(0);

    try {
      // Step 1: Get signed upload URL from your API
      console.log('📋 Step 1: Getting signed URL...');
      const urlResponse = await fetch('/api/upload-url', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fileName: file.name,
          fileType: file.type,
          fileSize: file.size,
        }),
      });

      if (!urlResponse.ok) {
        const errorData = await urlResponse.json();
        throw new Error(errorData.error || 'Failed to get upload URL');
      }

      const { uploadUrl, filePath, fileName, fileType } = await urlResponse.json();
      console.log('✅ Got signed URL:', uploadUrl);
      setProgress(25);

      // Step 2: Upload directly to Supabase using signed URL
      console.log('📤 Step 2: Uploading directly to Supabase...');
      const uploadResponse = await fetch(uploadUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type,
        },
      });

      console.log('📥 Upload response status:', uploadResponse.status);

      if (!uploadResponse.ok) {
        const responseText = await uploadResponse.text();
        console.error('❌ Upload failed:', responseText);
        throw new Error(`Upload failed: ${uploadResponse.status} ${uploadResponse.statusText}`);
      }

      console.log('✅ File uploaded successfully to Supabase');
      setProgress(75);

      // Step 3: Complete upload by saving to database
      console.log('💾 Step 3: Saving to database...');
      const completeResponse = await fetch('/api/upload-complete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          filePath,
          originalFileName: file.name,
          fileType,
          fileSize: file.size,
          mimeType: file.type,
        }),
      });

      if (!completeResponse.ok) {
        const errorData = await completeResponse.json();
        throw new Error(errorData.error || 'Failed to complete upload');
      }

      const result = await completeResponse.json();
      setProgress(100);
      
      console.log('✅ Upload completed successfully');
      onUploadComplete(result.file);

    } catch (error: any) {
      console.error('❌ Upload error:', error);
      onUploadError(error.message);
    } finally {
      setUploading(false);
      setProgress(0);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto p-4">
      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
        <input
          type="file"
          onChange={handleFileUpload}
          disabled={uploading}
          accept="image/*,video/*"
          className="hidden"
          id="direct-file-upload"
        />
        
        <label
          htmlFor="direct-file-upload"
          className={`cursor-pointer inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white ${
            uploading 
              ? 'bg-gray-400 cursor-not-allowed' 
              : 'bg-blue-600 hover:bg-blue-700'
          }`}
        >
          {uploading ? 'Uploading...' : 'Upload File (Direct to Supabase)'}
        </label>

        <p className="mt-2 text-sm text-gray-500">
          Images up to 10MB, Videos up to 50MB
        </p>
      </div>

      {uploading && (
        <div className="mt-4">
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div 
              className="bg-blue-600 h-2.5 rounded-full transition-all duration-300" 
              style={{ width: `${progress}%` }}
            />
          </div>
          <div className="text-xs text-gray-500 mt-1 text-center">
            {progress}% complete
          </div>
        </div>
      )}
    </div>
  );
}