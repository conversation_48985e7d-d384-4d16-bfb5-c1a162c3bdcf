'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { useUser } from '@clerk/nextjs';
import { Upload, X, FileImage, FileVideo, AlertCircle, CheckCircle, Loader2 } from 'lucide-react';
import { useUserData } from '@/contexts/UserDataContext';

interface DirectUploaderProps {
  onUploadComplete?: (result: any) => void;
  onUploadStart?: () => void;
  onUploadEnd?: () => void;
}

interface FileWithStatus extends File {
  id: string;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  progress: number;
  error?: string;
  result?: any;
}

export function DirectUploader({ onUploadComplete, onUploadStart, onUploadEnd }: DirectUploaderProps) {
  const { user } = useUser();
  const { refreshUserData } = useUserData();
  
  const [files, setFiles] = useState<FileWithStatus[]>([]);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const uploadFile = async (file: File): Promise<any> => {
    try {
      // Use the fallback API for reliable upload
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/upload-fallback', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Upload failed' }));
        throw new Error(errorData.error || 'Upload failed');
      }

      return await response.json();
    } catch (error: any) {
      throw new Error(error.message || 'Upload failed');
    }
  };

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (!user?.id) {
      setError('Please sign in to upload files');
      return;
    }

    if (acceptedFiles.length === 0) {
      setError('No valid files selected');
      return;
    }

    setError(null);
    setUploading(true);
    onUploadStart?.();

    try {
      const file = acceptedFiles[0]; // Handle one file at a time
      
      const fileWithStatus: FileWithStatus = {
        ...file,
        id: `${Date.now()}-${Math.random().toString(36).substring(2, 15)}`,
        status: 'uploading',
        progress: 0
      };

      setFiles([fileWithStatus]);

      console.log('🚀 Starting upload for:', file.name);

      const result = await uploadFile(file);

      if (result.success) {
        console.log('✅ Upload successful:', result);
        setFiles(prev => prev.map(f => 
          f.id === fileWithStatus.id 
            ? { ...f, status: 'completed' as const, result, progress: 100 }
            : f
        ));
        onUploadComplete?.(result);
        
        // Refresh user data
        setTimeout(() => {
          refreshUserData();
        }, 500);
      } else {
        throw new Error(result.error || 'Upload failed');
      }

    } catch (error: any) {
      console.error('❌ Upload error:', error);
      setError(error.message || 'Upload failed');
      setFiles(prev => prev.map(f => ({ ...f, status: 'error' as const, error: error.message })));
    } finally {
      setUploading(false);
      onUploadEnd?.();
    }

  }, [user, onUploadComplete, onUploadStart, onUploadEnd, refreshUserData]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
      'image/gif': ['.gif'],
      'image/webp': ['.webp'],
      'video/mp4': ['.mp4'],
      'video/quicktime': ['.mov'],
      'video/avi': ['.avi'],
      'video/webm': ['.webm']
    },
    maxSize: 50 * 1024 * 1024, // 50MB
    multiple: false,
    disabled: uploading
  });

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const getFileIcon = (file: FileWithStatus) => {
    if (file.type.startsWith('image/')) {
      return <FileImage className="h-4 w-4" />;
    } else if (file.type.startsWith('video/')) {
      return <FileVideo className="h-4 w-4" />;
    }
    return <FileImage className="h-4 w-4" />;
  };

  const getStatusIcon = (file: FileWithStatus) => {
    switch (file.status) {
      case 'uploading':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-400" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-400" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-400" />;
      default:
        return null;
    }
  };

  if (!user) {
    return (
      <div className="w-full p-4 text-center">
        <p className="text-gray-400">Please sign in to upload files</p>
      </div>
    );
  }

  return (
    <div className="w-full space-y-4">
      {/* Upload Area */}
      <div
        {...getRootProps()}
        className={`flex h-40 cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed ${
          isDragActive ? 'border-blue-500 bg-blue-500/10' : 'border-gray-600'
        } transition-colors ${uploading ? 'pointer-events-none opacity-50' : ''}`}
      >
        <input {...getInputProps()} />
        <div className="flex flex-col items-center justify-center space-y-2">
          <Upload className="h-6 w-6 text-gray-400" />
          <p className="text-center text-lg font-medium text-white">
            {uploading ? 'Uploading...' : isDragActive ? 'Drop your file here!' : 'Drop photo or video files here'}
          </p>
          <p className="mt-1 text-center text-sm text-gray-400">
            {uploading ? 'Uploading to cloud storage...' : 'Files upload to cloud storage • Max 50MB'}
          </p>
          <div className="mt-2 text-center text-xs text-gray-500">
            <div className="mb-1">
              <span className="text-green-400">✓</span> Images: JPEG, PNG, GIF, WebP (10MB max)
            </div>
            <div className="mb-2">
              <span className="text-purple-400">✓</span> Videos: MP4, MOV, AVI, WebM (50MB max)
            </div>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="rounded-lg bg-red-500/10 border border-red-500/20 p-3">
          <div className="flex items-center space-x-2">
            <AlertCircle className="h-4 w-4 text-red-400" />
            <p className="text-sm text-red-400">{error}</p>
          </div>
        </div>
      )}

      {/* File List */}
      {files.length > 0 && (
        <div className="space-y-2">
          {files.map((file) => (
            <div key={file.id} className="flex items-center justify-between rounded-lg bg-gray-800/50 p-3">
              <div className="flex items-center space-x-3">
                {getFileIcon(file)}
                <div className="flex-1">
                  <p className="text-sm font-medium text-white">{file.name}</p>
                  <p className="text-xs text-gray-400">
                    {(file.size / (1024 * 1024)).toFixed(2)} MB
                  </p>
                  {file.status === 'uploading' && (
                    <div className="mt-1">
                      <div className="h-1 w-full bg-gray-700 rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-blue-500 transition-all duration-300"
                          style={{ width: `${file.progress}%` }}
                        />
                      </div>
                    </div>
                  )}
                  {file.status === 'error' && file.error && (
                    <p className="text-xs text-red-400 mt-1">{file.error}</p>
                  )}
                  {file.status === 'completed' && (
                    <p className="text-xs text-green-400 mt-1">Upload completed successfully</p>
                  )}
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {getStatusIcon(file)}
                {file.status !== 'uploading' && (
                  <button
                    onClick={() => removeFile(file.id)}
                    className="text-gray-400 hover:text-red-400 transition-colors"
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
