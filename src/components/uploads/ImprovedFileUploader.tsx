'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { Upload, X, AlertCircle, CheckCircle } from 'lucide-react';

interface ImprovedFileUploaderProps {
  onUploadComplete?: (result: any) => void;
  onUploadStart?: () => void;
  onUploadEnd?: () => void;
}

export function ImprovedFileUploader({ onUploadComplete, onUploadStart, onUploadEnd }: ImprovedFileUploaderProps) {
  const { user } = useUser();
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [currentFileName, setCurrentFileName] = useState<string>('');

  // Prevent page unload during upload
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (uploading) {
        e.preventDefault();
        e.returnValue = 'Upload in progress. Are you sure you want to leave?';
        return 'Upload in progress. Are you sure you want to leave?';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [uploading]);

  const validateFile = (file: File): { isValid: boolean; error?: string } => {
    console.log('🔍 Validating file:', file.name, file.type, file.size);
    
    const allowedImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    const allowedVideoTypes = [
      'video/mp4', 'video/quicktime', 'video/mov', 'video/avi', 
      'video/webm', 'video/x-msvideo', 'video/3gpp', 'video/x-ms-wmv'
    ];
    const allAllowedTypes = [...allowedImageTypes, ...allowedVideoTypes];

    const fileType = file.type.toLowerCase();
    
    if (!allAllowedTypes.includes(fileType)) {
      return {
        isValid: false,
        error: `File type "${file.type}" is not supported. Please use: Images (JPEG, PNG, GIF, WebP) or Videos (MP4, MOV, AVI, WebM, WMV, 3GP).`
      };
    }

    const isVideo = allowedVideoTypes.includes(fileType);
    const maxSize = isVideo ? 100 * 1024 * 1024 : 10 * 1024 * 1024; // 100MB for videos, 10MB for images
    
    if (file.size > maxSize) {
      const fileSizeMB = (file.size / (1024 * 1024)).toFixed(1);
      const maxSizeMB = (maxSize / (1024 * 1024)).toFixed(0);
      return {
        isValid: false,
        error: `File is too large (${fileSizeMB}MB). Maximum size is ${maxSizeMB}MB for ${isVideo ? 'videos' : 'images'}.`
      };
    }

    return { isValid: true };
  };

  const uploadFile = async (file: File): Promise<any> => {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      const formData = new FormData();
      formData.append('file', file);

      const isVideo = file.type.startsWith('video/');
      let lastProgressTime = Date.now();

      // Progress tracking
      xhr.upload.addEventListener('progress', (e) => {
        if (e.lengthComputable) {
          const percentComplete = Math.round((e.loaded / e.total) * 100);
          setUploadProgress(percentComplete);
          lastProgressTime = Date.now();
          console.log(`📊 Upload progress: ${percentComplete}%`);
        }
      });

      // Success handler
      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            console.log('✅ Upload successful:', response);
            resolve(response);
          } catch (error) {
            reject(new Error('Invalid server response'));
          }
        } else {
          try {
            const errorResponse = JSON.parse(xhr.responseText);
            reject(new Error(errorResponse.error || `Server error: ${xhr.status}`));
          } catch {
            reject(new Error(`Upload failed with status: ${xhr.status}`));
          }
        }
      });

      // Error handlers
      xhr.addEventListener('error', () => {
        reject(new Error('Network error occurred during upload'));
      });

      xhr.addEventListener('timeout', () => {
        reject(new Error('Upload timed out. Please try a smaller file or check your connection.'));
      });

      xhr.addEventListener('abort', () => {
        reject(new Error('Upload was cancelled'));
      });

      // Configure and start upload
      xhr.open('POST', '/api/secure-upload');
      xhr.timeout = isVideo ? 15 * 60 * 1000 : 5 * 60 * 1000; // 15 min for videos, 5 min for images
      
      console.log(`🚀 Starting upload: ${file.name} (${isVideo ? 'video' : 'image'})`);
      xhr.send(formData);
    });
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    console.log('📁 File selected:', file.name, file.type, file.size);

    // Reset state
    setError(null);
    setSuccess(null);
    setUploadProgress(0);

    // Validate file
    const validation = validateFile(file);
    if (!validation.isValid) {
      setError(validation.error!);
      event.target.value = '';
      return;
    }

    // Check authentication
    if (!user) {
      setError('Please log in to upload files');
      event.target.value = '';
      return;
    }

    // Prevent multiple uploads
    if (uploading) {
      console.log('⚠️ Upload already in progress');
      event.target.value = '';
      return;
    }

    try {
      setUploading(true);
      setCurrentFileName(file.name);
      setUploadProgress(0);

      // Notify parent that upload started
      if (onUploadStart) {
        onUploadStart();
      }

      const result = await uploadFile(file);

      if (result && result.success) {
        setUploadProgress(100);
        setSuccess(`✅ "${file.name}" uploaded successfully!`);
        
        // Notify parent component
        if (onUploadComplete) {
          // Add delay to ensure database is updated
          setTimeout(() => {
            onUploadComplete(result);
          }, 1500);
        }

        // Auto-hide success message
        setTimeout(() => setSuccess(null), 8000);
      } else {
        throw new Error(result?.error || 'Upload failed');
      }
    } catch (error: any) {
      console.error('❌ Upload failed:', error);
      setError(error.message || 'Upload failed');
    } finally {
      // Reset upload state
      setTimeout(() => {
        setUploading(false);
        setUploadProgress(0);
        setCurrentFileName('');
        
        // Notify parent that upload ended
        if (onUploadEnd) {
          onUploadEnd();
        }
      }, 3000);
      
      // Reset file input
      event.target.value = '';
    }
  };

  return (
    <div className="w-full space-y-4">
      {/* Upload Area */}
      <div className="relative">
        <input
          type="file"
          accept="image/jpeg,image/png,image/gif,image/webp,video/mp4,video/quicktime,video/avi,video/webm,video/x-msvideo,video/3gpp,video/x-ms-wmv,.mp4,.mov,.avi,.webm,.wmv,.3gp"
          onChange={handleFileSelect}
          disabled={uploading}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer disabled:cursor-not-allowed"
          id="improved-file-upload"
        />
        <label
          htmlFor="improved-file-upload"
          className={`flex h-40 cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-600 transition-colors hover:border-blue-500 hover:bg-blue-500/10 ${
            uploading ? 'pointer-events-none opacity-50' : ''
          }`}
        >
          <div className="flex flex-col items-center justify-center space-y-2">
            <Upload className="h-6 w-6 text-gray-400" />
            <p className="text-center text-lg font-medium text-white">
              {uploading ? 'Uploading...' : 'Click to select photo or video'}
            </p>
            <p className="mt-1 text-center text-sm text-gray-400">
              {uploading ? 'Please wait...' : 'Images (10MB max) • Videos (100MB max)'}
            </p>
            <div className="mt-2 text-center text-xs text-gray-500">
              <div className="mb-1">
                <span className="text-green-400">✓</span> Images: JPEG, PNG, GIF, WebP (10MB max)
              </div>
              <div>
                <span className="text-purple-400">✓</span> Videos: MP4, MOV, AVI, WebM, WMV, 3GP (100MB max)
              </div>
            </div>
          </div>
        </label>
      </div>

      {/* Error Message */}
      {error && (
        <div className="rounded-lg border border-red-500/20 bg-red-900/20 p-4">
          <div className="flex items-start space-x-3">
            <AlertCircle className="h-5 w-5 text-red-400 flex-shrink-0 mt-0.5" />
            <div className="flex-1">
              <h3 className="text-sm font-medium text-red-300">Upload Error</h3>
              <p className="mt-1 text-sm text-red-200">{error}</p>
            </div>
            <button 
              onClick={() => setError(null)}
              className="flex-shrink-0 text-red-300 hover:text-red-100 transition-colors"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}

      {/* Success Message */}
      {success && (
        <div className="rounded-lg border border-green-500/20 bg-green-900/20 p-4">
          <div className="flex items-start space-x-3">
            <CheckCircle className="h-5 w-5 text-green-400 flex-shrink-0 mt-0.5" />
            <div className="flex-1">
              <h3 className="text-sm font-medium text-green-300">Upload Successful</h3>
              <p className="mt-1 text-sm text-green-200">{success}</p>
            </div>
            <button 
              onClick={() => setSuccess(null)}
              className="flex-shrink-0 text-green-300 hover:text-green-100 transition-colors"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}

      {/* Upload Progress */}
      {uploading && (
        <div className="rounded-lg bg-gray-800 p-4">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="animate-spin rounded-full h-5 w-5 border-2 border-blue-400 border-t-transparent" />
                <div>
                  <p className="text-sm font-medium text-white">Uploading "{currentFileName}"</p>
                  <p className="text-xs text-gray-400">
                    {currentFileName.toLowerCase().includes('.mp4') || 
                     currentFileName.toLowerCase().includes('.mov') || 
                     currentFileName.toLowerCase().includes('.avi') ? 
                     'Video uploads may take longer...' : 'Please don\'t close this page'}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium text-blue-400">{uploadProgress}%</p>
                <p className="text-xs text-gray-400">
                  {uploadProgress === 0 ? 'Starting...' :
                   uploadProgress < 99 ? 'Uploading...' :
                   uploadProgress === 99 ? 'Processing...' :
                   uploadProgress === 100 ? 'Complete!' : 'Uploading...'}
                </p>
              </div>
            </div>
            
            {/* Progress Bar */}
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div 
                className="bg-blue-500 h-2 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${uploadProgress}%` }}
              />
            </div>
            
            {uploadProgress >= 99 && (
              <p className="text-xs text-yellow-400 text-center">
                {uploadProgress === 99 ? 'Upload complete, processing file on server...' : 
                 uploadProgress === 100 ? '✅ File processed successfully!' : ''}
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
}