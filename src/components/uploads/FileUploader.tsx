'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { useUser } from '@clerk/nextjs';
import { Upload, X, FileImage, FileVideo, AlertCircle } from 'lucide-react';
import Link from 'next/link';
import { useUserData } from '@/contexts/UserDataContext';
import { ProPlanProtection } from '@/components/auth/PlanProtection';

interface FileUploaderProps {
  onUploadComplete?: (fileData: any) => void;
}

interface FileWithStatus extends File {
  id?: string;
  status?: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
  uploadProgress?: number;
  error?: string;
}

export function FileUploader({ onUploadComplete }: FileUploaderProps) {
  const { user } = useUser();
  const { refreshUserData } = useUserData();
  const [files, setFiles] = useState<FileWithStatus[]>([]);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    console.log('📁 Files dropped:', acceptedFiles.length);

    // Filter out unsupported file types and invalid files
    const supportedFiles = acceptedFiles.filter(file => {
      console.log('🔍 Checking file:', {
        name: file?.name,
        type: file?.type,
        size: file?.size,
        hasName: !!file?.name,
        hasType: !!file?.type,
        hasSize: !!file?.size
      });

      // Check if file object is valid
      if (!file || !file.name || !file.type) {
        console.log('❌ File rejected - missing properties');
        return false;
      }

      const isImage = file.type.startsWith('image/');
      const isVideo = file.type.startsWith('video/');
      const isSupported = isImage || isVideo;

      console.log('📋 File validation:', {
        isImage,
        isVideo,
        isSupported,
        type: file.type
      });

      return isSupported;
    });

    console.log('✅ Supported files:', supportedFiles.length, 'out of', acceptedFiles.length);

    if (supportedFiles.length !== acceptedFiles.length) {
      setError('Some files were rejected. Only valid images and videos are supported.');
    } else {
      setError(null);
    }

    // Add status to files
    const filesWithStatus: FileWithStatus[] = supportedFiles.map(file => ({
      ...file,
      id: `${Date.now()}-${Math.random().toString(36).substring(2, 15)}`,
      status: 'pending' as const,
      uploadProgress: 0,
    }));

    setFiles(prev => [...prev, ...filesWithStatus]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif'],
      'video/*': ['.mp4', '.mov', '.avi']
    },
    maxSize: 50 * 1024 * 1024, // 50MB
  });

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
  };

  const uploadFiles = async () => {
    if (!user || files.length === 0) {
      console.log('❌ Cannot upload: no user or no files');
      return;
    }

    console.log('🚀 Starting upload process with', files.length, 'files');
    console.log('👤 User ID:', user.id);
    console.log('📋 Files to upload:', files.map(f => ({
      name: f?.name,
      type: f?.type,
      size: f?.size,
      id: f?.id,
      status: f?.status
    })));

    // Filter out invalid files before upload
    const validFiles = files.filter(file => {
      const isValid = file && file.name && file.type;
      console.log('�� File validation:', {
        file: file?.name,
        hasFile: !!file,
        hasName: !!file?.name,
        hasType: !!file?.type,
        hasSize: !!file?.size,
        size: file?.size,
        isValid
      });
      return isValid;
    });

    console.log('✅ Valid files for upload:', validFiles.length, 'out of', files.length);

    if (validFiles.length === 0) {
      console.error('❌ No valid files to upload');
      setError('No valid files to upload');
      return;
    }

    setUploading(true);
    setError(null);

    const uploadPromises = validFiles.map(async (file) => {
      try {
        console.log('📤 Processing file:', file.name, file.type, file.size);

        // Upload via secure server-side API (same as working debug upload)
        const uploadFormData = new FormData();
        uploadFormData.append('file', file);

        console.log('📡 Sending upload request to /api/secure-upload...');

        // Update progress to uploading
        setFiles(prev => prev.map(f => {
          if (!f || !f.id || f.id !== (file as FileWithStatus).id) return f;
          return { ...f, status: 'uploading' as const, uploadProgress: 50 };
        }));

        const uploadResponse = await fetch('/api/secure-upload', {
          method: 'POST',
          body: uploadFormData
        });

        console.log('📥 Upload response status:', uploadResponse.status);

        const uploadResult = await uploadResponse.json();
        console.log('📥 Upload result:', uploadResult);

        if (!uploadResponse.ok || !uploadResult.success) {
          console.error('❌ Upload failed:', uploadResult.error);
          throw new Error(uploadResult.error || 'Upload failed');
        }

        console.log('✅ Upload successful:', {
          fileId: uploadResult.fileId,
          path: uploadResult.path,
          url: uploadResult.url
        });

        // Update progress to completed
        setFiles(prev => prev.map(f => {
          if (!f || !f.id || f.id !== (file as FileWithStatus).id) return f;
          return { ...f, status: 'completed' as const, uploadProgress: 100 };
        }));

        // Return upload result in expected format
        return {
          file,
          data: { path: uploadResult.path },
          recordData: {
            id: uploadResult.fileId,
            original_url: uploadResult.url,
            status: 'completed'
          }
        };
      } catch (err: any) {
        console.error('❌ Upload error for file', file?.name, ':', err);
        setError(`Error uploading ${file?.name || 'file'}: ${err.message}`);

        // Update file status to error
        setFiles(prev => prev.map(f => {
          if (!f || !f.id || f.id !== (file as FileWithStatus).id) return f;
          return { ...f, status: 'error' as const, error: err.message };
        }));

        return null;
      }
    });

    try {
      console.log('⏳ Waiting for all uploads to complete...');
      const results = await Promise.all(uploadPromises);
      const successfulUploads = results.filter(Boolean);

      console.log('📊 Upload results:', {
        total: results.length,
        successful: successfulUploads.length,
        failed: results.length - successfulUploads.length
      });

      if (successfulUploads.length > 0) {
        console.log('✅ Uploads completed successfully, calling onUploadComplete...');
        
        // Clear files after a short delay
        setTimeout(() => {
          setFiles([]);
        }, 2000);

        // Refresh user data to update credits
        refreshUserData();

        // Call the completion handler
        if (onUploadComplete) {
          onUploadComplete(successfulUploads);
        }
      } else {
        console.error('❌ No uploads were successful');
        setError('All uploads failed');
      }
    } catch (err: any) {
      console.error('❌ Error in upload process:', err);
      setError(`Upload process error: ${err.message}`);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="w-full space-y-4">
      <div
        {...getRootProps()}
        className={`flex h-40 cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed ${
          isDragActive ? 'border-blue-500 bg-blue-500/10' : 'border-gray-600'
        } transition-colors`}
      >
        <input {...getInputProps()} />
        <div className="flex flex-col items-center justify-center space-y-2">
          <Upload className="h-6 w-6 text-gray-400" />
          <p className="text-center text-lg font-medium text-white">Drop photo or video files here</p>
          <p className="mt-1 text-center text-sm text-gray-400">
            <Link href="/dashboard/billing" className="text-orange-500 hover:underline">
              Upgrade
            </Link>{' '}
            to upload multiple files at once
          </p>
        </div>
      </div>

      {error && (
        <div className="flex items-center space-x-2 rounded-md bg-red-900/50 p-3 text-white">
          <AlertCircle className="h-5 w-5" />
          <span>{error}</span>
        </div>
      )}

      {files.length > 0 && (
        <div className="space-y-3">
          <h3 className="text-lg font-medium text-white">Files to upload ({files.length})</h3>
          <div className="space-y-2">
            {files.filter(file => file && file.name).map((file, index) => {
              const isImage = file?.type?.startsWith('image/') || false;
              const progress = file?.uploadProgress || 0;
              const status = file?.status || 'pending';

              const getStatusColor = () => {
                switch (status) {
                  case 'pending': return 'text-gray-400';
                  case 'uploading': return 'text-blue-400';
                  case 'processing': return 'text-yellow-400';
                  case 'completed': return 'text-green-400';
                  case 'error': return 'text-red-400';
                  default: return 'text-gray-400';
                }
              };

              const getStatusText = () => {
                switch (status) {
                  case 'pending': return 'Ready';
                  case 'uploading': return `${progress}%`;
                  case 'processing': return 'Processing...';
                  case 'completed': return 'Done ✓';
                  case 'error': return 'Error ✗';
                  default: return 'Unknown';
                }
              };

              return (
                <div key={file.id || index} className="flex items-center justify-between rounded-md bg-navy-light p-3">
                  <div className="flex items-center space-x-3">
                    {isImage ? (
                      <FileImage className="h-5 w-5 text-blue-400" />
                    ) : (
                      <FileVideo className="h-5 w-5 text-purple-400" />
                    )}
                    <div className="min-w-0 flex-1">
                      <p className="truncate text-sm font-medium text-white">{file?.name || 'Unknown file'}</p>
                      <p className="text-xs text-gray-400">{file?.size ? (file.size / (1024 * 1024)).toFixed(2) : '0'} MB</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className={`text-xs font-medium ${getStatusColor()}`}>
                      {getStatusText()}
                    </div>
                    {status === 'uploading' && (
                      <div className="w-12 bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${progress}%` }}
                        ></div>
                      </div>
                    )}
                    {status === 'processing' && (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-400"></div>
                    )}
                    {(status === 'pending' || status === 'error') && (
                      <button
                        onClick={() => removeFile(index)}
                        className="text-gray-400 hover:text-red-500"
                        disabled={uploading}
                      >
                        <X className="h-5 w-5" />
                      </button>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          {files.length > 1 ? (
            <ProPlanProtection fallbackMessage="Multiple file uploads require the Pro plan or higher. Upgrade to upload multiple files at once.">
              <button
                onClick={() => {
                  console.log('🔘 Upload button clicked (multiple files)');
                  uploadFiles();
                }}
                disabled={uploading || files.length === 0}
                className="w-full rounded-md bg-orange-500 py-2 px-4 font-medium text-white transition-colors hover:bg-orange-600 disabled:cursor-not-allowed disabled:opacity-50"
              >
                {uploading ? 'Uploading...' : `Upload ${files.length} files`}
              </button>
            </ProPlanProtection>
          ) : (
            <button
              onClick={() => {
                console.log('🔘 Upload button clicked (single file)');
                uploadFiles();
              }}
              disabled={uploading || files.length === 0}
              className="w-full rounded-md bg-orange-500 py-2 px-4 font-medium text-white transition-colors hover:bg-orange-600 disabled:cursor-not-allowed disabled:opacity-50"
            >
              {uploading ? 'Uploading...' : `Upload ${files.length} file`}
            </button>
          )}
        </div>
      )}
    </div>
  );
}