'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { useUser } from '@clerk/nextjs';
import { Upload, X, FileImage, FileVideo, AlertCircle, CheckCircle } from 'lucide-react';
import Link from 'next/link';
import { useUserData } from '@/contexts/UserDataContext';

interface FileUploaderProps {
  onUploadComplete?: (fileData: any) => void;
}

interface FileWithStatus extends File {
  id?: string;
  status?: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
  uploadProgress?: number;
  error?: string;
}

export function FileUploaderFixed({ onUploadComplete }: FileUploaderProps) {
  const { user } = useUser();
  const { refreshUserData } = useUserData();
  const [files, setFiles] = useState<FileWithStatus[]>([]);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const uploadSingleFile = async (file: File): Promise<any> => {
    try {
      console.log('📤 Uploading file:', file.name, file.type, file.size);

      const formData = new FormData();
      formData.append('file', file);

      console.log('📡 Sending request to /api/secure-upload...');

      const response = await fetch('/api/secure-upload', {
        method: 'POST',
        body: formData
      });

      console.log('📥 Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Upload failed with status:', response.status, errorText);
        throw new Error(`Upload failed: ${response.status} ${errorText}`);
      }

      const result = await response.json();
      console.log('📥 Upload result:', result);

      if (!result.success) {
        throw new Error(result.error || 'Upload failed');
      }

      return result;
    } catch (error: any) {
      console.error('❌ Upload error:', error);
      throw error;
    }
  };

  // Enhanced file validation function
  const validateFile = (file: File): { isValid: boolean; error?: string } => {
    // Check file type
    const allowedImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    const allowedVideoTypes = ['video/mp4', 'video/quicktime', 'video/mov', 'video/avi', 'video/webm'];
    const allAllowedTypes = [...allowedImageTypes, ...allowedVideoTypes];

    if (!allAllowedTypes.includes(file.type.toLowerCase())) {
      return {
        isValid: false,
        error: `"${file.name}" is not a supported file type. Please upload images (JPEG, PNG, GIF, WebP) or videos (MP4, MOV, AVI, WebM) only.`
      };
    }

    // Check file size (50MB limit)
    const maxSize = 50 * 1024 * 1024;
    if (file.size > maxSize) {
      return {
        isValid: false,
        error: `"${file.name}" is too large (${(file.size / (1024 * 1024)).toFixed(1)}MB). Maximum file size is 50MB.`
      };
    }

    // Check minimum file size (1KB to avoid empty files)
    if (file.size < 1024) {
      return {
        isValid: false,
        error: `"${file.name}" is too small. File appears to be empty or corrupted.`
      };
    }

    return { isValid: true };
  };

  const onDrop = useCallback(async (acceptedFiles: File[], rejectedFiles: any[]) => {
    try {
      if (!user) {
        setError('Please log in to upload files');
        return;
      }

      // Prevent new uploads if one is already in progress
      if (uploading) {
        setError('⏳ Please wait for the current upload to complete before uploading new files');
        return;
      }

      console.log('📁 Files dropped:', acceptedFiles.length, 'accepted,', rejectedFiles.length, 'rejected');
      setError(null);

      // Handle rejected files from dropzone
      if (rejectedFiles.length > 0) {
        const rejectedFile = rejectedFiles[0];
        const fileName = rejectedFile.file?.name || 'Unknown file';
        
        if (rejectedFile.errors?.some((e: any) => e.code === 'file-too-large')) {
          setError(`"${fileName}" is too large. Maximum file size is 50MB.`);
        } else if (rejectedFile.errors?.some((e: any) => e.code === 'file-invalid-type')) {
          setError(`"${fileName}" is not a supported file type. Please upload images or videos only.`);
        } else {
          setError(`"${fileName}" was rejected. Please try again with a valid image or video file.`);
        }
        return;
      }

      if (acceptedFiles.length === 0) {
        setError('No valid files to upload. Please select images or videos only.');
        return;
      }

      setUploading(true);

    // Process each file with enhanced validation
    for (const file of acceptedFiles) {
      console.log('🔍 Processing file:', {
        name: file.name,
        type: file.type,
        size: file.size
      });

      // Additional validation beyond dropzone
      const validation = validateFile(file);
      if (!validation.isValid) {
        console.log('❌ File validation failed:', validation.error);
        setError(validation.error!);
        setUploading(false);
        return;
      }

      // Add file to state with uploading status
      const fileWithStatus: FileWithStatus = {
        ...file,
        id: `${Date.now()}-${Math.random().toString(36).substring(2, 15)}`,
        status: 'uploading',
        uploadProgress: 0,
      };

      setFiles(prev => [...prev, fileWithStatus]);

      try {
        // Update progress
        setFiles(prev => prev.map(f => 
          f.id === fileWithStatus.id 
            ? { ...f, uploadProgress: 50 }
            : f
        ));

        // Upload the file
        const result = await uploadSingleFile(file);

        // Update to completed
        setFiles(prev => prev.map(f => 
          f.id === fileWithStatus.id 
            ? { ...f, status: 'completed', uploadProgress: 100 }
            : f
        ));

        console.log('✅ File uploaded successfully:', result.fileId);

        // Refresh user data first
        await refreshUserData();

        // Call completion callback after a small delay
        setTimeout(() => {
          try {
            if (onUploadComplete) {
              onUploadComplete([{
                file,
                data: { path: result.path },
                recordData: {
                  id: result.fileId,
                  original_url: result.url,
                  status: 'completed'
                }
              }]);
            }
          } catch (callbackError) {
            console.error('❌ Error in upload completion callback:', callbackError);
          }
        }, 100);

        // Remove file from list after 3 seconds
        setTimeout(() => {
          setFiles(prev => prev.filter(f => f.id !== fileWithStatus.id));
        }, 3000);

      } catch (error: any) {
        console.error('❌ Upload failed for file:', file.name, error);
        
        // Update to error status
        setFiles(prev => prev.map(f => 
          f.id === fileWithStatus.id 
            ? { ...f, status: 'error', error: error.message }
            : f
        ));

        setError(`Failed to upload "${file.name}": ${error.message}`);
      }
    }

    setUploading(false);
    } catch (dropError) {
      console.error('❌ Critical error in file drop handler:', dropError);
      setError('An unexpected error occurred during file upload. Please refresh the page and try again.');
      setUploading(false);
    }
  }, [user, onUploadComplete, refreshUserData]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
      'image/gif': ['.gif'],
      'image/webp': ['.webp'],
      'video/mp4': ['.mp4'],
      'video/quicktime': ['.mov'],
      'video/avi': ['.avi'],
      'video/webm': ['.webm']
    },
    maxSize: 50 * 1024 * 1024, // 50MB
    minSize: 1024, // 1KB minimum
    multiple: false, // Only allow single file for now
    maxFiles: 1,
    preventDropOnDocument: true,
    noClick: false,
    noKeyboard: false,
    disabled: uploading
  });

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
  };

  return (
    <div className="w-full space-y-4">
      <div
        {...getRootProps()}
        className={`flex h-40 cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed ${
          isDragActive ? 'border-blue-500 bg-blue-500/10' : 'border-gray-600'
        } transition-colors ${uploading ? 'pointer-events-none opacity-50' : ''}`}
      >
        <input {...getInputProps()} />
        <div className="flex flex-col items-center justify-center space-y-2">
          <Upload className="h-6 w-6 text-gray-400" />
          <p className="text-center text-lg font-medium text-white">
            {uploading ? 'Uploading...' : isDragActive ? 'Drop your file here!' : 'Drop photo or video files here'}
          </p>
          <p className="mt-1 text-center text-sm text-gray-400">
            {uploading ? 'Processing your file...' : 'Files will upload automatically • Max 50MB'}
          </p>
          <div className="mt-2 text-center text-xs text-gray-500">
            <div className="mb-1">
              <span className="text-green-400">✓</span> Images: JPEG, PNG, GIF, WebP
            </div>
            <div className="mb-2">
              <span className="text-purple-400">✓</span> Videos: MP4, MOV, AVI, WebM
            </div>
            <p>
              <Link href="/dashboard/billing" className="text-orange-500 hover:underline">
                Upgrade
              </Link>{' '}
              to upload multiple files at once
            </p>
          </div>
        </div>
      </div>

      {error && (
        <div className="rounded-lg border border-red-500/20 bg-red-900/20 p-4 backdrop-blur-sm">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <AlertCircle className="h-5 w-5 text-red-400" />
            </div>
            <div className="flex-1">
              <h3 className="text-sm font-medium text-red-300">Upload Error</h3>
              <p className="mt-1 text-sm text-red-200">{error}</p>
              <div className="mt-2 text-xs text-red-300/80">
                Please check your file and try again. Only images and videos under 50MB are supported.
              </div>
            </div>
            <button 
              onClick={() => setError(null)}
              className="flex-shrink-0 text-red-300 hover:text-red-100 transition-colors"
              title="Dismiss error"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}

      {files.length > 0 && (
        <div className="space-y-3">
          <h3 className="text-lg font-medium text-white">Upload Progress</h3>
          <div className="space-y-2">
            {files.map((file) => {
              const isImage = file.type.startsWith('image/');
              const progress = file.uploadProgress || 0;
              const status = file.status || 'pending';

              const getStatusColor = () => {
                switch (status) {
                  case 'pending': return 'text-gray-400';
                  case 'uploading': return 'text-blue-400';
                  case 'processing': return 'text-yellow-400';
                  case 'completed': return 'text-green-400';
                  case 'error': return 'text-red-400';
                  default: return 'text-gray-400';
                }
              };

              const getStatusIcon = () => {
                switch (status) {
                  case 'completed': return <CheckCircle className="h-4 w-4 text-green-400" />;
                  case 'error': return <AlertCircle className="h-4 w-4 text-red-400" />;
                  case 'uploading': return (
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-400 border-t-transparent" />
                  );
                  default: return null;
                }
              };

              const getStatusText = () => {
                switch (status) {
                  case 'pending': return 'Ready';
                  case 'uploading': return `Uploading ${progress}%`;
                  case 'processing': return 'Processing...';
                  case 'completed': return 'Upload complete!';
                  case 'error': return `Error: ${file.error}`;
                  default: return 'Unknown';
                }
              };

              return (
                <div key={file.id} className="flex items-center justify-between rounded-md bg-gray-800 p-3">
                  <div className="flex items-center space-x-3">
                    {isImage ? (
                      <FileImage className="h-5 w-5 text-blue-400" />
                    ) : (
                      <FileVideo className="h-5 w-5 text-purple-400" />
                    )}
                    <div className="min-w-0 flex-1">
                      <p className="truncate text-sm font-medium text-white">{file.name}</p>
                      <p className="text-xs text-gray-400">
                        {(file.size / (1024 * 1024)).toFixed(2)} MB
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className={`text-xs font-medium ${getStatusColor()}`}>
                      {getStatusText()}
                    </div>
                    {status === 'uploading' && (
                      <div className="w-16 bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${progress}%` }}
                        />
                      </div>
                    )}
                    {getStatusIcon()}
                    {status === 'error' && (
                      <button
                        onClick={() => removeFile(file.id!)}
                        className="text-gray-400 hover:text-red-500"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}