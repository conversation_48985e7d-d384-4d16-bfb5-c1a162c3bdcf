'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { Play, Clock, AlertCircle, CheckCircle, XCircle, Loader2 } from 'lucide-react';
import Image from 'next/image';

type MediaType = 'all' | 'image' | 'video';

interface MediaItem {
  id: string;
  user_id: string;
  original_filename: string;
  file_path: string;
  processed_path: string | null;
  thumbnail_path: string | null;
  file_type: 'image' | 'video';
  file_size: number;
  mime_type: string;
  processing_status: 'pending' | 'processing' | 'completed' | 'failed';
  processing_progress: number;
  metadata: any;
  created_at: string;
  updated_at: string;
  processed_at: string | null;
}

export function MediaGallery() {
  const { user } = useUser();
  const [mediaType, setMediaType] = useState<MediaType>('all');
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState<'latest' | 'oldest'>('latest');

  useEffect(() => {
    if (!user) return;
    
    async function fetchMedia() {
      setLoading(true);
      setError(null);
      
      try {
        console.log('🔍 Fetching media for user:', user.id);
        
        // Use the same API endpoint as MediaDashboard for consistency
        const params = new URLSearchParams();
        if (mediaType === 'image') {
          params.set('type', 'image');
        } else if (mediaType === 'video') {
          params.set('type', 'video');
        }
        params.set('limit', '50');
        params.set('offset', '0');

        const response = await fetch(`/api/user/files?${params}`, {
          cache: 'no-store',
          headers: {
            'Cache-Control': 'no-cache'
          }
        });
        
        const result = await response.json();
        
        if (result.success) {
          let files = result.files || [];
          
          // Apply client-side sorting
          files.sort((a: any, b: any) => {
            const dateA = new Date(a.created_at).getTime();
            const dateB = new Date(b.created_at).getTime();
            return sortOrder === 'oldest' ? dateA - dateB : dateB - dateA;
          });
          
          console.log('✅ Fetched files via API:', files.length);
          setMediaItems(files);
        } else {
          throw new Error(result.error || 'Failed to fetch files');
        }
      } catch (err: any) {
        console.error('Error fetching media:', err);
        setError(`Failed to load media: ${err.message}`);
      } finally {
        setLoading(false);
      }
    }
    
    fetchMedia();
  }, [user, mediaType, sortOrder]);

  // Format the time elapsed since upload
  const formatTimeElapsed = (dateString: string) => {
    const uploadDate = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - uploadDate.getTime();
    
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffMonths = Math.floor(diffDays / 30);
    
    if (diffMonths > 0) {
      return `${diffMonths} month${diffMonths !== 1 ? 's' : ''} ago`;
    } else if (diffDays > 0) {
      return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
    } else if (diffHours > 0) {
      return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
    } else {
      return `${diffMins} minute${diffMins !== 1 ? 's' : ''} ago`;
    }
  };

  // Format video duration
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Render status icon
  const renderStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'processing':
        return <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Filter tabs */}
      <div className="flex border-b border-gray-700">
        <button
          className={`px-4 py-2 font-medium ${mediaType === 'all' ? 'text-white border-b-2 border-blue-500' : 'text-gray-400 hover:text-white'}`}
          onClick={() => setMediaType('all')}
        >
          All
        </button>
        <button
          className={`px-4 py-2 font-medium ${mediaType === 'video' ? 'text-white border-b-2 border-blue-500' : 'text-gray-400 hover:text-white'}`}
          onClick={() => setMediaType('video')}
        >
          Video
        </button>
        <button
          className={`px-4 py-2 font-medium ${mediaType === 'image' ? 'text-white border-b-2 border-blue-500' : 'text-gray-400 hover:text-white'}`}
          onClick={() => setMediaType('image')}
        >
          Photo
        </button>
        
        <div className="ml-auto">
          <select
            value={sortOrder}
            onChange={(e) => setSortOrder(e.target.value as 'latest' | 'oldest')}
            className="bg-navy-light text-white border border-gray-700 rounded-md px-2 py-1 text-sm"
          >
            <option value="latest">Latest</option>
            <option value="oldest">Oldest</option>
          </select>
        </div>
      </div>
      
      {/* Error message */}
      {error && (
        <div className="bg-red-900/50 text-white p-3 rounded-md flex items-center space-x-2">
          <AlertCircle className="h-5 w-5" />
          <span>{error}</span>
        </div>
      )}
      
      {/* Loading state */}
      {loading && (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 text-blue-500 animate-spin" />
          <span className="ml-2 text-gray-300">Loading your media...</span>
        </div>
      )}
      
      {/* Empty state */}
      {!loading && mediaItems.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-400 mb-2">No media found</p>
          <p className="text-gray-500 text-sm">Upload some files to get started</p>
        </div>
      )}
      
      {/* Media grid */}
      {!loading && mediaItems.length > 0 && (
        <div className="grid grid-cols-2 gap-3 md:grid-cols-4 lg:grid-cols-6">
          {mediaItems.map((item) => {
            const isVideo = item.file_type === 'video';
            const fileName = item.original_filename;
            const fileUrl = item.metadata?.originalUrl || item.processed_path;
            
            return (
              <div key={`${item.file_type}-${item.id}`} className="group cursor-pointer overflow-hidden rounded-lg bg-navy-light/50 transition-all duration-200 hover:bg-navy-light hover:scale-105">
                {/* Thumbnail */}
                <div className="relative aspect-square bg-navy-dark">
                  {fileUrl ? (
                    <>
                      {isVideo ? (
                        // For videos, show the video element with proper error handling
                        <div className="relative h-full w-full">
                          <video
                            className="h-full w-full object-cover"
                            preload="metadata"
                            muted
                            onMouseEnter={(e) => {
                              const video = e.target as HTMLVideoElement;
                              video.currentTime = 1; // Jump to 1 second to get a better frame
                            }}
                            onError={(e) => {
                              console.error('Video failed to load:', fileUrl);
                              // Hide video and show fallback
                              const video = e.target as HTMLVideoElement;
                              video.style.display = 'none';
                              const fallback = video.nextElementSibling as HTMLElement;
                              if (fallback) fallback.style.display = 'flex';
                            }}
                            onLoadedData={(e) => {
                              // Video loaded successfully, hide fallback
                              const video = e.target as HTMLVideoElement;
                              const fallback = video.nextElementSibling as HTMLElement;
                              if (fallback) fallback.style.display = 'none';
                            }}
                          >
                            <source src={fileUrl} type={item.mime_type} />
                          </video>
                          {/* Fallback if video fails to load */}
                          <div className="absolute inset-0 flex items-center justify-center bg-navy-dark" style={{ display: 'none' }}>
                            <div className="text-center">
                              <div className="mx-auto mb-2 h-8 w-8 rounded bg-purple-500/20 flex items-center justify-center">
                                <Play className="h-4 w-4 text-purple-400" />
                              </div>
                              <p className="text-xs text-gray-400">Video</p>
                              <p className="text-xs text-gray-500 mt-1">Failed to load</p>
                            </div>
                          </div>
                        </div>
                      ) : (
                        // For images, show the actual image with error handling
                        <div className="relative h-full w-full">
                          <Image
                            src={fileUrl}
                            alt={fileName}
                            fill
                            className="object-cover transition-transform duration-200 group-hover:scale-110"
                            sizes="(max-width: 768px) 50vw, (max-width: 1024px) 25vw, 16vw"
                            onError={(e) => {
                              console.error('Image failed to load:', fileUrl);
                            }}
                          />
                        </div>
                      )}
                      
                      {/* Video play overlay */}
                      {isVideo && (
                        <div className="absolute inset-0 flex items-center justify-center bg-black/20 opacity-0 transition-opacity duration-200 group-hover:opacity-100">
                          <div className="rounded-full bg-white/90 p-2">
                            <Play className="h-6 w-6 text-gray-800" />
                          </div>
                        </div>
                      )}

                      {/* Video duration badge */}
                      {isVideo && item.metadata?.duration && (
                        <div className="absolute bottom-1 right-1 bg-black/80 text-white text-xs px-1.5 py-0.5 rounded flex items-center">
                          <Play className="h-2 w-2 mr-1" />
                          {formatDuration(item.metadata.duration)}
                        </div>
                      )}

                      {/* File type badge */}
                      <div className="absolute bottom-8 right-1">
                        <div className={`rounded px-1.5 py-0.5 text-xs font-medium ${
                          isVideo ? 'bg-purple-500/80 text-white' : 'bg-blue-500/80 text-white'
                        }`}>
                          {isVideo ? 'VIDEO' : 'IMAGE'}
                        </div>
                      </div>

                      {/* Status indicator */}
                      {item.processing_status !== 'completed' && (
                        <div className="absolute top-1 right-1">
                          <div className={`rounded px-1.5 py-0.5 text-xs font-medium ${
                            item.processing_status === 'pending' ? 'bg-yellow-500/80 text-white' :
                            item.processing_status === 'processing' ? 'bg-blue-500/80 text-white' :
                            'bg-red-500/80 text-white'
                          }`}>
                            {renderStatusIcon(item.processing_status)}
                          </div>
                        </div>
                      )}
                    </>
                  ) : (
                    // Loading state
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-center">
                        <Loader2 className="mx-auto h-6 w-6 text-gray-500 animate-spin" />
                        <p className="mt-1 text-xs text-gray-500">Loading...</p>
                      </div>
                    </div>
                  )}
                </div>
                
                {/* File info - compact */}
                <div className="p-2">
                  <p className="truncate text-xs font-medium text-white" title={fileName}>
                    {fileName}
                  </p>
                  <div className="mt-1 flex items-center justify-between text-xs text-gray-400">
                    <span>{formatTimeElapsed(item.created_at)}</span>
                    <span>{(item.file_size / (1024 * 1024)).toFixed(1)}MB</span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}
