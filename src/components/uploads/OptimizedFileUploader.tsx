'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { useUser } from '@clerk/nextjs';
import { Upload, X, FileImage, FileVideo, AlertCircle, CheckCircle } from 'lucide-react';
import Link from 'next/link';
import { useUserData } from '@/contexts/UserDataContext';

interface FileUploaderProps {
  onUploadComplete?: (fileData: any) => void;
}

interface FileWithStatus {
  file: File;
  id: string;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
  uploadProgress: number;
  error?: string;
}

export function OptimizedFileUploader({ onUploadComplete }: FileUploaderProps) {
  const { user } = useUser();
  const { refreshUserData } = useUserData();
  const [files, setFiles] = useState<FileWithStatus[]>([]);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const uploadSingleFile = async (fileWithStatus: FileWithStatus): Promise<any> => {
    const file = fileWithStatus.file;

    try {
      console.log('📤 Starting direct upload:', file.name, file.type, file.size);

      // Step 1: Get signed upload URL
      console.log('📝 Step 1: Requesting signed URL...');
      const urlResponse = await fetch('/api/upload-url', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fileName: file.name,
          fileType: file.type,
          fileSize: file.size
        })
      });

      if (!urlResponse.ok) {
        const errorData = await urlResponse.json();
        throw new Error(errorData.error || 'Failed to get upload URL');
      }

      const { uploadUrl, filePath } = await urlResponse.json();
      console.log('✅ Got signed URL');

      // Update progress
      setFiles(prev => prev.map(f =>
        f.id === fileWithStatus.id ? { ...f, uploadProgress: 25 } : f
      ));

      // Step 2: Upload directly to Supabase Storage (bypasses API route size limit)
      console.log('📤 Step 2: Uploading directly to Supabase Storage...');
      const uploadResponse = await fetch(uploadUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type,
          'Content-Length': file.size.toString(),
        },
      });

      if (!uploadResponse.ok) {
        const responseText = await uploadResponse.text();
        console.error('❌ Direct upload failed:', responseText);
        throw new Error(`Upload failed: ${uploadResponse.status} ${uploadResponse.statusText}`);
      }

      console.log('✅ File uploaded to Supabase Storage');

      // Update progress
      setFiles(prev => prev.map(f =>
        f.id === fileWithStatus.id ? { ...f, uploadProgress: 75 } : f
      ));

      // Step 3: Complete upload by creating database record
      console.log('💾 Step 3: Creating database record...');
      const completeResponse = await fetch('/api/upload-complete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          filePath,
          originalFileName: file.name,
          fileType: file.type.startsWith('video/') ? 'video' : 'image',
          fileSize: file.size,
          mimeType: file.type
        })
      });

      if (!completeResponse.ok) {
        const errorData = await completeResponse.json();
        throw new Error(errorData.error || 'Failed to complete upload');
      }

      const result = await completeResponse.json();
      console.log('✅ Upload completed:', result);

      // Update progress to 100%
      setFiles(prev => prev.map(f =>
        f.id === fileWithStatus.id ? { ...f, status: 'completed', uploadProgress: 100 } : f
      ));

      return result;

    } catch (error: any) {
      console.error('❌ Upload error:', error);
      throw error;
    }
  };

  // Enhanced file validation function
  const validateFile = (file: File): { isValid: boolean; error?: string } => {
    // Check file type
    const allowedImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    const allowedVideoTypes = ['video/mp4', 'video/quicktime', 'video/mov', 'video/avi', 'video/webm', 'video/x-msvideo'];
    const allAllowedTypes = [...allowedImageTypes, ...allowedVideoTypes];

    if (!allAllowedTypes.includes(file.type.toLowerCase())) {
      return {
        isValid: false,
        error: `"${file.name}" is not a supported file type. Please upload images (JPEG, PNG, GIF, WebP) or videos (MP4, MOV, AVI, WebM) only.`
      };
    }

    // Check file size (50MB limit for direct upload)
    const maxSize = 50 * 1024 * 1024;
    if (file.size > maxSize) {
      return {
        isValid: false,
        error: `"${file.name}" is too large (${(file.size / (1024 * 1024)).toFixed(1)}MB). Maximum file size is 50MB.`
      };
    }

    // Check minimum file size (1KB to avoid empty files)
    if (file.size < 1024) {
      return {
        isValid: false,
        error: `"${file.name}" is too small. File appears to be empty or corrupted.`
      };
    }

    return { isValid: true };
  };

  const onDrop = useCallback(async (acceptedFiles: File[], rejectedFiles: any[]) => {
    try {
      if (!user) {
        setError('Please log in to upload files');
        return;
      }

      // Prevent new uploads if one is already in progress
      if (uploading) {
        setError('⏳ Please wait for the current upload to complete before uploading new files');
        return;
      }

      console.log('📁 Files dropped:', acceptedFiles.length, 'accepted,', rejectedFiles.length, 'rejected');
      setError(null);

      // Handle rejected files from dropzone
      if (rejectedFiles.length > 0) {
        const rejectedFile = rejectedFiles[0];
        const fileName = rejectedFile.file?.name || 'Unknown file';

        if (rejectedFile.errors?.some((e: any) => e.code === 'file-too-large')) {
          setError(`"${fileName}" is too large. Maximum file size is 50MB.`);
        } else if (rejectedFile.errors?.some((e: any) => e.code === 'file-invalid-type')) {
          setError(`"${fileName}" is not a supported file type. Please upload images or videos only.`);
        } else {
          setError(`"${fileName}" was rejected. Please try again with a valid image or video file.`);
        }
        return;
      }

      if (acceptedFiles.length === 0) {
        setError('No valid files to upload. Please select images or videos only.');
        return;
      }

      setUploading(true);

      // Process each file with enhanced validation
      for (const file of acceptedFiles) {
        console.log('🔍 Processing file:', {
          name: file.name,
          type: file.type,
          size: file.size
        });

        // Additional validation beyond dropzone
        const validation = validateFile(file);
        if (!validation.isValid) {
          console.log('❌ File validation failed:', validation.error);
          setError(validation.error!);
          setUploading(false);
          return;
        }

        // Add file to state with uploading status
        const fileWithStatus: FileWithStatus = {
          file,
          id: `${Date.now()}-${Math.random().toString(36).substring(2, 15)}`,
          status: 'uploading',
          uploadProgress: 0,
        };

        setFiles(prev => [...prev, fileWithStatus]);

        try {
          // Upload the file using direct upload
          const result = await uploadSingleFile(fileWithStatus);

          console.log('✅ File uploaded successfully:', result.file?.id);

          // Refresh user data first
          await refreshUserData();

          // Call completion callback after a small delay
          setTimeout(() => {
            try {
              if (onUploadComplete) {
                onUploadComplete(result);
              }
            } catch (callbackError) {
              console.error('❌ Error in upload completion callback:', callbackError);
            }
          }, 100);

          // Remove file from list after 3 seconds
          setTimeout(() => {
            setFiles(prev => prev.filter(f => f.id !== fileWithStatus.id));
          }, 3000);

        } catch (error: any) {
          console.error('❌ Upload failed for file:', file.name, error);

          // Update to error status
          setFiles(prev => prev.map(f =>
            f.id === fileWithStatus.id
              ? { ...f, status: 'error', error: error.message }
              : f
          ));

          setError(`Failed to upload "${file.name}": ${error.message}`);
        }
      }

      setUploading(false);
    } catch (dropError) {
      console.error('❌ Critical error in file drop handler:', dropError);
      setError('An unexpected error occurred during file upload. Please refresh the page and try again.');
      setUploading(false);
    }
  }, [user, onUploadComplete, refreshUserData, uploading]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
      'image/gif': ['.gif'],
      'image/webp': ['.webp'],
      'video/mp4': ['.mp4'],
      'video/quicktime': ['.mov'],
      'video/avi': ['.avi'],
      'video/webm': ['.webm']
    },
    maxSize: 50 * 1024 * 1024, // 50MB
    minSize: 1024, // 1KB minimum
    multiple: false, // Only allow single file
    maxFiles: 1,
    preventDropOnDocument: true,
    noClick: false,
    noKeyboard: false,
    disabled: uploading
  });

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
  };

  return (
    <div className="w-full space-y-4">
      <div
        {...getRootProps()}
        className={`relative overflow-hidden flex h-44 cursor-pointer flex-col items-center justify-center rounded-xl border-2 border-dashed transition-all duration-300 ${
          isDragActive
            ? 'border-purple-500 bg-purple-500/10 scale-[1.02]'
            : 'border-gray-700 hover:border-gray-600 bg-gray-900/50'
        } ${uploading ? 'pointer-events-none opacity-50' : ''}`}
      >
        {/* Gradient background effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-purple-600/5 via-transparent to-blue-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

        <input {...getInputProps()} />
        <div className="relative z-10 flex flex-col items-center justify-center space-y-2 px-4">
          <div className={`p-2.5 rounded-full bg-gradient-to-br from-purple-500/20 to-blue-500/20 transition-transform duration-300 ${isDragActive ? 'scale-110' : ''}`}>
            <Upload className={`h-6 w-6 ${isDragActive ? 'text-purple-400' : 'text-gray-400'} transition-colors duration-300`} />
          </div>
          <p className="text-center text-base font-medium text-white">
            {uploading ? 'Uploading...' : isDragActive ? 'Drop your file here!' : 'Drop files here or click to browse'}
          </p>
          <p className="text-center text-xs text-gray-400">
            {uploading ? 'Processing your file...' : 'Large files up to 50MB supported'}
          </p>
          <div className="mt-1 text-center text-[11px] text-gray-500 space-y-0.5">
            <div className="flex items-center justify-center space-x-3">
              <span><span className="text-green-400">✓</span> JPEG, PNG, GIF, WebP</span>
              <span><span className="text-purple-400">✓</span> MP4, MOV, AVI, WebM</span>
            </div>
            <p className="mt-1.5">
              <Link href="/dashboard/billing" className="text-purple-400 hover:text-purple-300 transition-colors underline">
                Upgrade
              </Link>{' '}
              <span className="text-gray-600">for multi-file uploads</span>
            </p>
          </div>
        </div>
      </div>

      {error && (
        <div className="relative rounded-xl border border-red-500/30 bg-red-950/20 p-4 backdrop-blur-sm">
          <div className="absolute inset-0 bg-red-600/10 rounded-xl blur-xl" />
          <div className="relative flex items-start space-x-3">
            <div className="flex-shrink-0">
              <AlertCircle className="h-5 w-5 text-red-400" />
            </div>
            <div className="flex-1">
              <h3 className="text-sm font-medium text-red-300">Upload Error</h3>
              <p className="mt-1 text-sm text-red-200 whitespace-pre-wrap">{error}</p>
              <div className="mt-2 text-xs text-red-300/80">
                Please check your file and try again. Only images and videos under 50MB are supported.
              </div>
            </div>
            <button
              onClick={() => setError(null)}
              className="flex-shrink-0 text-red-300 hover:text-red-100 transition-colors"
              title="Dismiss error"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}

      {files.length > 0 && (
        <div className="space-y-3">
          <h3 className="text-lg font-medium text-white">Upload Progress</h3>
          <div className="space-y-2">
            {files.map((fileWithStatus) => {
              const file = fileWithStatus.file;
              const isImage = file.type.startsWith('image/');
              const progress = fileWithStatus.uploadProgress || 0;
              const status = fileWithStatus.status || 'pending';

              const getStatusColor = () => {
                switch (status) {
                  case 'pending': return 'text-gray-400';
                  case 'uploading': return 'text-blue-400';
                  case 'processing': return 'text-yellow-400';
                  case 'completed': return 'text-green-400';
                  case 'error': return 'text-red-400';
                  default: return 'text-gray-400';
                }
              };

              const getStatusIcon = () => {
                switch (status) {
                  case 'completed': return <CheckCircle className="h-4 w-4 text-green-400" />;
                  case 'error': return <AlertCircle className="h-4 w-4 text-red-400" />;
                  case 'uploading': return (
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-400 border-t-transparent" />
                  );
                  default: return null;
                }
              };

              const getStatusText = () => {
                switch (status) {
                  case 'pending': return 'Ready';
                  case 'uploading': return `Uploading ${progress}%`;
                  case 'processing': return 'Processing...';
                  case 'completed': return 'Upload complete!';
                  case 'error': return `Error: ${fileWithStatus.error}`;
                  default: return 'Unknown';
                }
              };

              return (
                <div key={fileWithStatus.id} className="flex items-center justify-between rounded-lg bg-gray-800/50 border border-gray-700/50 p-3 backdrop-blur-sm">
                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    {isImage ? (
                      <FileImage className="h-5 w-5 text-blue-400 flex-shrink-0" />
                    ) : (
                      <FileVideo className="h-5 w-5 text-purple-400 flex-shrink-0" />
                    )}
                    <div className="min-w-0 flex-1">
                      <p className="truncate text-sm font-medium text-white">{file.name}</p>
                      <p className="text-xs text-gray-400">
                        {(file.size / (1024 * 1024)).toFixed(2)} MB
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className={`text-xs font-medium whitespace-nowrap ${getStatusColor()}`}>
                      {getStatusText()}
                    </div>
                    {status === 'uploading' && (
                      <div className="w-16 bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${progress}%` }}
                        />
                      </div>
                    )}
                    {getStatusIcon()}
                    {status === 'error' && (
                      <button
                        onClick={() => removeFile(fileWithStatus.id)}
                        className="text-gray-400 hover:text-red-500 transition-colors"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}
