'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { useUser } from '@clerk/nextjs';
import { Upload, AlertCircle, Play } from 'lucide-react';
import { useUserData } from '@/contexts/UserDataContext';

interface RobustFileUploaderProps {
  onUploadComplete?: (fileData: any) => void;
  onUploadStart?: () => void;
  onUploadEnd?: () => void;
}

interface FileWithStatus extends File {
  id?: string;
  status?: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
  error?: string;
  progress?: number;
  thumbnail?: string;
  videoMetadata?: {
    duration: number;
    width: number;
    height: number;
  };
}

export function RobustFileUploader({ onUploadComplete, onUploadStart, onUploadEnd }: RobustFileUploaderProps) {
  const { user } = useUser();
  const { refreshUserData } = useUserData();
  const [files, setFiles] = useState<FileWithStatus[]>([]);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadAbortController, setUploadAbortController] = useState<AbortController | null>(null);
  const [autoSyncing, setAutoSyncing] = useState(false);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    console.log('📁 Files dropped:', acceptedFiles.length);

    // Validate files
    const validationResults = acceptedFiles.map(file => {
      console.log('🔍 Checking file:', {
        name: file?.name,
        type: file?.type,
        size: file?.size,
        sizeInMB: file?.size ? (file.size / (1024 * 1024)).toFixed(2) : 'unknown'
      });

      if (!file || !file.name || !file.type) {
        return { file, valid: false, error: 'Invalid file properties' };
      }

      const isImage = file.type.startsWith('image/') && !file.type.includes('gif');
      const isVideo = file.type.startsWith('video/') || file.type === 'image/gif';
      const isSupported = isImage || isVideo;

      if (!isSupported) {
        return { file, valid: false, error: 'Unsupported file type. Only images and videos are allowed.' };
      }

      // Check file size (50MB limit - Supabase Storage limit)
      const maxSize = 50 * 1024 * 1024; // 50MB
      if (file.size > maxSize) {
        const fileSizeMB = (file.size / (1024 * 1024)).toFixed(1);
        return {
          file,
          valid: false,
          error: `❌ Video too large (${fileSizeMB}MB). Supabase Storage limit is 50MB. Please compress your video or use a smaller file.`
        };
      }

      return { file, valid: true, error: null };
    });

    const supportedFiles = validationResults.filter(result => result.valid).map(result => result.file);
    const rejectedFiles = validationResults.filter(result => !result.valid);

    console.log('✅ Supported files:', supportedFiles.length, 'out of', acceptedFiles.length);

    if (rejectedFiles.length > 0) {
      if (rejectedFiles.length === 1) {
        setError(rejectedFiles[0]?.error || 'File rejected');
      } else {
        const sizeErrors = rejectedFiles.filter(r => r.error?.includes('too large'));
        const typeErrors = rejectedFiles.filter(r => r.error?.includes('Unsupported'));
        
        if (sizeErrors.length > 0) {
          setError(`❌ File "${sizeErrors.length}" is too large. \nPlease compress your video or choose a smaller file.\n`)
        } else if (typeErrors.length > 0) {
          setError(`❌ ${typeErrors.length} file(s) rejected. Only images and videos are supported.`);
        } else {
          setError(`❌ ${rejectedFiles.length} file(s) were rejected.`);
        }
      }
    } else {
      setError(null);
    }

    if (supportedFiles.length > 0) {
      // Add status to files and generate thumbnails immediately
      const filesWithStatus: FileWithStatus[] = await Promise.all(
        supportedFiles.map(async (file) => {
          const fileWithStatus = file as FileWithStatus;
          fileWithStatus.id = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
          fileWithStatus.status = 'pending';
          fileWithStatus.progress = 0;

          // Generate thumbnail immediately for videos
          if (file.type.startsWith('video/')) {
            try {
              console.log('🖼️ Generating thumbnail for:', file.name);
              console.log('✅ Thumbnail generated for:', file.name);
            } catch (error) {
              console.warn('⚠️ Failed to generate thumbnail for:', file.name, error);
            }
          } else if (file.type.startsWith('image/')) {
            // TEMPORARY: Skip thumbnail generation for images to test
            console.log('🖼️ Skipping thumbnail generation for image (testing):', file.name);
            // fileWithStatus.thumbnail = URL.createObjectURL(file); // Comment this out
          }

          return fileWithStatus;
        })
      );

      setFiles(prev => [...prev, ...filesWithStatus]);

      // Auto-upload files immediately
      console.log('🚀 Auto-uploading files:', filesWithStatus.length);
      console.log('🔍 Files to upload:', filesWithStatus.map(f => ({ name: f.name, id: f.id, type: f.type })));
      console.log('🔍 User info:', { user: !!user, userId: user?.id });

      // Start upload with a small delay to ensure state is updated
      setTimeout(() => {
        console.log('⏰ Starting auto-upload...');
        uploadFiles(filesWithStatus);
      }, 500);
    }
  }, [user]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    onDropRejected: (rejectedFiles) => {
      console.log('❌ Files rejected by dropzone:', rejectedFiles);

      const sizeRejected = rejectedFiles.filter(rejection =>
        rejection.errors.some(error => error.code === 'file-too-large')
      );

      const typeRejected = rejectedFiles.filter(rejection =>
        rejection.errors.some(error => error.code === 'file-invalid-type')
      );

      if (sizeRejected.length > 0) {
        const largestFile = sizeRejected[0]?.file;
        if (largestFile) {
          const fileSizeMB = (largestFile.size / (1024 * 1024)).toFixed(1);
          setError(`❌ File "${largestFile.name}" is too large (${fileSizeMB}MB, limit: 50MB).\nPlease compress your video or choose a smaller file.\n`);
        }
      } else if (typeRejected.length > 0) {
        setError(`❌ Unsupported file type. Only images (JPEG, PNG, GIF) and videos (MP4, MOV, AVI) are allowed.`);
      } else {
        setError(`❌ ${rejectedFiles.length} file(s) were rejected.`);
      }
    },
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif'],
      'video/*': ['.mp4', '.mov', '.avi']
    },
    maxSize: 50 * 1024 * 1024, // 50MB
  });

  const cancelUpload = () => {
    console.log('🛑 Canceling upload...');
    if (uploadAbortController) {
      uploadAbortController.abort();
    }
    setUploading(false);
    setUploadAbortController(null);
    setError(null);

    // Clean up object URLs before clearing files
    files.forEach(file => {
      if (file.thumbnail && file.thumbnail.startsWith('blob:')) {
        URL.revokeObjectURL(file.thumbnail);
      }
    });

    // Clear all files completely when canceling
    setFiles([]);

    console.log('✅ Upload canceled and files cleared');
  };

  const uploadFiles = async (filesToUpload: FileWithStatus[]) => {
    console.log('🔍 Upload function called with:', {
      user: !!user,
      userId: user?.id,
      filesToUpload: filesToUpload?.length || 0,
      fileNames: filesToUpload?.map(f => f.name) || []
    });

    if (!filesToUpload || filesToUpload.length === 0) {
      console.log('❌ Cannot upload: no files provided');
      setError('No files to upload');
      return;
    }

    // Force check user again at upload time
    if (!user || !user.id) {
      console.log('❌ Cannot upload: user not authenticated');
      setError('Please sign in to upload files');
      return;
    }

    console.log('🚀 Starting DIRECT upload with', filesToUpload.length, 'files');
    setUploading(true);
    setError(null);

    // Create abort controller for canceling uploads
    const abortController = new AbortController();
    setUploadAbortController(abortController);

    onUploadStart?.();

    const uploadPromises = filesToUpload.map(async (file) => {
      try {
        // Validate file object
        if (!file || !file.name || !file.type || !file.size) {
          console.error('❌ Invalid file object:', {
            hasFile: !!file,
            name: file?.name,
            type: file?.type,
            size: file?.size
          });
          throw new Error('Invalid file object');
        }

        console.log('📤 Starting DIRECT upload for file:', file.name, 'Size:', (file.size / (1024 * 1024)).toFixed(2) + 'MB');

        // Update file status to uploading
        setFiles(prev => prev.map(f =>
          f.id === file.id ? { ...f, status: 'uploading' as const, progress: 0 } : f
        ));

        // STEP 1: Get signed upload URL
        console.log('📋 Step 1: Getting signed URL for:', file.name);
        const urlResponse = await fetch('/api/upload-url', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            fileName: file.name,
            fileType: file.type,
            fileSize: file.size,
          }),
          signal: abortController.signal,
        });

        if (!urlResponse.ok) {
          const errorData = await urlResponse.json();
          throw new Error(errorData.error || 'Failed to get upload URL');
        }

        const { uploadUrl, filePath, fileName, fileType } = await urlResponse.json();
        console.log('✅ Got signed URL for:', fileName);

        // Update progress
        setFiles(prev => prev.map(f =>
          f.id === file.id ? { ...f, progress: 25 } : f
        ));

        // STEP 2: Upload directly to Supabase
        console.log('📤 Step 2: Uploading directly to Supabase...');
        const uploadResponse = await fetch(uploadUrl, {
          method: 'PUT',
          body: file,
          headers: {
            'Content-Type': file.type,
          },
          signal: abortController.signal,
        });

        console.log('📥 Direct upload response status:', uploadResponse.status);

        if (!uploadResponse.ok) {
          const responseText = await uploadResponse.text();
          console.error('❌ Direct upload failed:', responseText);
          throw new Error(`Upload failed: ${uploadResponse.status} ${uploadResponse.statusText}`);
        }

        console.log('✅ File uploaded directly to Supabase successfully');

        // Update progress
        setFiles(prev => prev.map(f =>
          f.id === file.id ? { ...f, progress: 75 } : f
        ));

        // STEP 3: Complete upload by saving to database with thumbnail and metadata
        console.log('💾 Step 3: Saving to database...');
        const completeResponse = await fetch('/api/upload-complete', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            filePath,
            originalFileName: file.name,
            fileType,
            fileSize: file.size,
            mimeType: file.type,
            // Don't send the actual thumbnail data - it can be very large
            // thumbnail: file.thumbnail, // ← Remove this line
            videoMetadata: file.videoMetadata, // Keep video metadata as it's small
          }),
          signal: abortController.signal,
        });

        if (!completeResponse.ok) {
          const errorData = await completeResponse.json();
          throw new Error(errorData.error || 'Failed to complete upload');
        }

        const result = await completeResponse.json();
        console.log('✅ Upload completed successfully for:', file.name, '- File ID:', result.file.id);

        // Set status to completed with 100% progress
        setFiles(prev => prev.map(f =>
          f.id === file.id ? { ...f, status: 'completed' as const, progress: 100 } : f
        ));

        return {
          success: true,
          fileId: result.file.id,
          filename: result.file.filename,
          url: result.file.url,
          thumbnail: file.thumbnail
        };

      } catch (error: any) {
        // Handle aborted requests (user canceled)
        if (error.name === 'AbortError') {
          console.log('🛑 Upload aborted for file:', file.name);
          return {
            success: false,
            filename: file.name,
            error: 'Upload canceled'
          };
        }

        console.error('❌ Upload error for file', file.name, ':', error);

        // Update file status to error
        setFiles(prev => prev.map(f =>
          f.id === file.id ? { ...f, status: 'error' as const, error: error.message } : f
        ));

        return {
          success: false,
          filename: file.name,
          error: error.message
        };
      }
    }); // End of uploadPromises.map

    try {
      const results = await Promise.all(uploadPromises);
      const successful = results.filter(r => r.success);
      const failed = results.filter(r => !r.success);

      console.log('📊 Upload results:', {
        total: results.length,
        successful: successful.length,
        failed: failed.length
      });

      if (successful.length > 0) {
        console.log('✅ Direct uploads completed successfully');
        
        // Clear successful files after a longer delay so user can see completion
        setTimeout(() => {
          setFiles(prev => {
            // Clean up object URLs before filtering
            prev.forEach(file => {
              if (file.status === 'completed' && file.thumbnail && file.thumbnail.startsWith('blob:')) {
                URL.revokeObjectURL(file.thumbnail);
              }
            });
            return prev.filter(f => f.status !== 'completed');
          });
        }, 5000);

        // Refresh user data to update credits
        refreshUserData();

        // 🔄 AUTO-SYNC: Run the same sync functionality as the sync button
        console.log('🔄 Auto-syncing storage after successful upload...');
        setAutoSyncing(true);
        try {
          const syncResponse = await fetch('/api/sync-storage', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
          });

          if (syncResponse.ok) {
            const syncResult = await syncResponse.json();
            console.log('✅ Auto-sync completed successfully:', syncResult.summary);
          } else {
            console.warn('⚠️ Auto-sync failed, but upload was successful');
          }
        } catch (syncError) {
          console.warn('⚠️ Auto-sync error (upload still successful):', syncError);
        } finally {
          setAutoSyncing(false);
        }

        // Call completion handler
        if (onUploadComplete) {
          onUploadComplete({
            success: true,
            count: successful.length,
            uploads: successful
          });
        }
      }

      if (failed.length > 0) {
        setError(`${failed.length} file(s) failed to upload`);
      }

    } catch (error: any) {
      console.error('❌ Upload process error:', error);
      setError(`Upload failed: ${error.message}`);
    } finally {
      setUploading(false);
      setUploadAbortController(null);
      onUploadEnd?.();
    }
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="w-full space-y-4">
      <div
        {...getRootProps()}
        className={`flex h-40 cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed ${
          isDragActive ? 'border-blue-500 bg-blue-500/10' : 'border-gray-600'
        } transition-colors`}
      >
        <input {...getInputProps()} />
        <div className="flex flex-col items-center justify-center space-y-2">
          <Upload className="h-6 w-6 text-gray-400" />
          <p className="text-center text-lg font-medium text-white">Drop photo or video files here</p>
          <p className="mt-1 text-center text-sm text-gray-400">
          Want to upload a whole bunch at once?
          Go Pro or Premium to unlock full power! 🚀
          </p>
        </div>
      </div>

      {error && (
        <div className={`flex items-center space-x-2 rounded-md p-4 text-white border-2 ${
          error.includes('too large') || error.includes('50MB')
            ? 'bg-red-900/70 border-red-500 shadow-lg shadow-red-500/20'
            : 'bg-red-900/50 border-red-700'
        }`}>
          <AlertCircle className="h-5 w-5 flex-shrink-0" />
          <div className="flex-1">
            <span className="font-medium">{error}</span>
            {(error.includes('too large') || error.includes('50MB')) && (
              <div className="mt-2 text-sm text-red-200">
                💡 <strong>Tip:</strong> Upgrade to Pro or Premium for larger uploads! 🚀
              </div>
            )}
          </div>
        </div>
      )}

      {autoSyncing && (
        <div className="flex items-center space-x-2 rounded-md p-3 text-white bg-blue-900/50 border border-blue-500">
          <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-300 border-t-transparent" />
          <span className="text-sm">🔄 Auto-syncing files with storage...</span>
        </div>
      )}

      {/* Enhanced upload status with thumbnails */}
      {uploading && files.length > 0 && (
        <div className="mt-4 space-y-2">
          <div className="text-center py-2 bg-gray-800/30 rounded-lg border border-gray-700">
            <div className="flex items-center justify-center space-x-4">
              <div className="inline-flex items-center space-x-2 text-blue-400 text-sm">
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-400"></div>
                <span>Uploading {files.length} {files.length === 1 ? 'file' : 'files'}. Hang tight ☕, this will not take long!</span>
              </div>

              {/* Cancel button */}
              <button
                onClick={cancelUpload}
                className="px-2 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded transition-colors"
                title="Cancel all uploads"
              >
                Cancel
              </button>
            </div>
          </div>

          {/* Individual file progress with thumbnails */}
          {files.map((file) => (
            <div key={file.id} className="bg-gray-800/20 rounded p-3 border border-gray-700">
              <div className="flex items-center space-x-3">
                {/* Thumbnail preview */}
                <div className="relative flex-shrink-0 w-16 h-16 bg-gray-700 rounded overflow-hidden">
                  {file.thumbnail && (
                    <>
                      <img 
                        src={file.thumbnail} 
                        alt={file.name}
                        className="w-full h-full object-cover"
                      />
                      {file.type.startsWith('video/') && (
                        <div className="absolute inset-0 flex items-center justify-center">
                          <Play className="w-4 h-4 text-white/80" fill="currentColor" />
                        </div>
                      )}
                    </>
                  )}
                </div>

                {/* File info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm text-gray-300 truncate">{file.name}</span>
                    <span className="text-xs text-gray-400">
                      {file.status === 'uploading' ? `${file.progress || 0}%` : 
                       file.status === 'completed' ? '✅ Done' :
                       file.status === 'error' ? '❌ Error' : 'Pending'}
                    </span>
                  </div>

                  {/* Video metadata */}
                  {file.videoMetadata && (
                    <div className="text-xs text-gray-500 mb-1">
                      {formatDuration(file.videoMetadata.duration)} • {file.videoMetadata.width}×{file.videoMetadata.height}
                    </div>
                  )}
                  
                  {file.status === 'uploading' && (
                    <div className="w-full bg-gray-700 rounded-full h-1.5">
                      <div 
                        className="bg-blue-500 h-1.5 rounded-full transition-all duration-300" 
                        style={{ width: `${file.progress || 0}%` }}
                      />
                    </div>
                  )}
                  
                  {file.status === 'error' && file.error && (
                    <div className="text-xs text-red-400 mt-1">{file.error}</div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}