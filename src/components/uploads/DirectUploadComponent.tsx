'use client';

import { useState, useCallback, useRef } from 'react';
import { useDropzone } from 'react-dropzone';
import { useUser } from '@clerk/nextjs';
import { Upload, X, FileImage, FileVideo, AlertCircle, CheckCircle, Loader2 } from 'lucide-react';
import { useUserData } from '@/contexts/UserDataContext';

interface DirectUploadComponentProps {
  onUploadComplete?: (result: any) => void;
  onUploadStart?: () => void;
  onUploadEnd?: () => void;
  maxSizeMB?: number;
  accept?: string;
  className?: string;
}

interface FileWithStatus extends File {
  id: string;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  progress: number;
  error?: string;
  result?: any;
}

export function DirectUploadComponent({
  onUploadComplete,
  onUploadStart,
  onUploadEnd,
  maxSizeMB = 50,
  accept = "image/*,video/*",
  className = ""
}: DirectUploadComponentProps) {
  const { user } = useUser();
  const { refreshUserData } = useUserData();
  const [files, setFiles] = useState<FileWithStatus[]>([]);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Validate file before upload
  const validateFile = (file: File): { isValid: boolean; error?: string } => {
    // Check file type
    const allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    const allowedVideoTypes = ['video/mp4', 'video/quicktime', 'video/avi', 'video/webm'];
    const allowedTypes = [...allowedImageTypes, ...allowedVideoTypes];

    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: `File type "${file.type}" not supported. Please use: JPEG, PNG, GIF, WebP, MP4, MOV, AVI, or WebM.`
      };
    }

    // Check file size
    const isVideo = allowedVideoTypes.includes(file.type);
    const maxSize = isVideo ? 50 * 1024 * 1024 : 10 * 1024 * 1024; // 50MB for videos, 10MB for images
    
    if (file.size > maxSize) {
      const fileSizeMB = (file.size / (1024 * 1024)).toFixed(1);
      const maxSizeMB = (maxSize / (1024 * 1024)).toFixed(0);
      return {
        isValid: false,
        error: `File too large: ${fileSizeMB}MB. Maximum size is ${maxSizeMB}MB for ${isVideo ? 'videos' : 'images'}.`
      };
    }

    return { isValid: true };
  };

  // Direct upload using signed URLs
  const uploadFileDirect = async (file: FileWithStatus): Promise<any> => {
    try {
      console.log('🚀 Starting direct upload for:', file.name);

      // Step 1: Get signed upload URL
      console.log('📝 Step 1: Requesting signed upload URL...');
      console.log('🔍 File details:', {
        name: file.name,
        type: file.type,
        size: file.size,
        lastModified: file.lastModified
      });

      const requestBody = {
        fileName: file.name,
        fileType: file.type,
        fileSize: file.size
      };
      console.log('📤 Sending request body:', requestBody);

      const urlResponse = await fetch('/api/upload-url', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      if (!urlResponse.ok) {
        const errorData = await urlResponse.json();
        throw new Error(errorData.error || 'Failed to get upload URL');
      }

      const { uploadUrl, filePath } = await urlResponse.json();
      console.log('✅ Got signed upload URL');

      // Update progress
      setFiles(prev => prev.map(f => 
        f.id === file.id ? { ...f, progress: 25 } : f
      ));

      // Step 2: Upload directly to Supabase Storage
      console.log('📤 Step 2: Uploading directly to Supabase...');
      const uploadResponse = await fetch(uploadUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type,
        },
        signal: abortControllerRef.current?.signal,
      });

      if (!uploadResponse.ok) {
        const responseText = await uploadResponse.text();
        console.error('❌ Direct upload failed:', responseText);
        throw new Error(`Upload failed: ${uploadResponse.status} ${uploadResponse.statusText}`);
      }

      console.log('✅ File uploaded directly to Supabase');

      // Update progress
      setFiles(prev => prev.map(f => 
        f.id === file.id ? { ...f, progress: 75 } : f
      ));

      // Step 3: Complete upload by saving to database
      console.log('💾 Step 3: Saving to database...');
      const completeResponse = await fetch('/api/upload-complete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          filePath,
          originalFileName: file.name,
          fileType: file.type.startsWith('video/') ? 'video' : 'image',
          fileSize: file.size,
          mimeType: file.type
        }),
        signal: abortControllerRef.current?.signal,
      });

      if (!completeResponse.ok) {
        const errorData = await completeResponse.json();
        throw new Error(errorData.error || 'Failed to complete upload');
      }

      const result = await completeResponse.json();
      console.log('✅ Upload completed successfully');

      // Update progress to 100%
      setFiles(prev => prev.map(f => 
        f.id === file.id ? { ...f, progress: 100, status: 'completed', result } : f
      ));

      return result;

    } catch (error: any) {
      console.error('❌ Direct upload error:', error);
      
      // Update file status to error
      setFiles(prev => prev.map(f => 
        f.id === file.id ? { ...f, status: 'error', error: error.message } : f
      ));
      
      throw error;
    }
  };

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (!user?.id) {
      setError('Please sign in to upload files');
      return;
    }

    if (acceptedFiles.length === 0) {
      setError('No valid files selected');
      return;
    }

    // Reset error state
    setError(null);

    // Validate files
    const validationResults = acceptedFiles.map(file => ({
      file,
      validation: validateFile(file)
    }));

    const validFiles = validationResults.filter(r => r.validation.isValid).map(r => r.file);
    const invalidFiles = validationResults.filter(r => !r.validation.isValid);

    if (invalidFiles.length > 0) {
      setError(invalidFiles[0].validation.error!);
      return;
    }

    // Add files to state with pending status
    // IMPORTANT: Cannot spread File objects, must use Object.assign or add properties directly
    const filesWithStatus: FileWithStatus[] = validFiles.map(file => {
      const fileWithStatus = file as FileWithStatus;
      fileWithStatus.id = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
      fileWithStatus.status = 'pending';
      fileWithStatus.progress = 0;
      return fileWithStatus;
    });

    setFiles(filesWithStatus);
    setUploading(true);
    onUploadStart?.();

    // Create abort controller for this upload session
    abortControllerRef.current = new AbortController();

    try {
      // Process files one by one
      for (const file of filesWithStatus) {
        // Update status to uploading
        setFiles(prev => prev.map(f => 
          f.id === file.id ? { ...f, status: 'uploading' } : f
        ));

        const result = await uploadFileDirect(file);
        
        if (result.success) {
          onUploadComplete?.(result.file);
        }
      }

      // Refresh user data to update credits/sync
      await refreshUserData();

    } catch (error: any) {
      console.error('❌ Upload error:', error);
      setError(error.message || 'Upload failed');
    } finally {
      setUploading(false);
      onUploadEnd?.();
      abortControllerRef.current = null;
    }

  }, [user, onUploadComplete, onUploadStart, onUploadEnd, refreshUserData]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
      'image/gif': ['.gif'],
      'image/webp': ['.webp'],
      'video/mp4': ['.mp4'],
      'video/quicktime': ['.mov'],
      'video/avi': ['.avi'],
      'video/webm': ['.webm']
    },
    maxSize: maxSizeMB * 1024 * 1024,
    multiple: false,
    disabled: uploading
  });

  const cancelUpload = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    setUploading(false);
    setFiles([]);
    setError(null);
    onUploadEnd?.();
  };

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
  };

  return (
    <div className={`w-full space-y-4 ${className}`}>
      {/* Upload Area */}
      <div
        {...getRootProps()}
        className={`flex h-40 cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed ${
          isDragActive ? 'border-blue-500 bg-blue-500/10' : 'border-gray-600'
        } transition-colors ${uploading ? 'pointer-events-none opacity-50' : ''}`}
      >
        <input {...getInputProps()} />
        <div className="flex flex-col items-center justify-center space-y-2">
          <Upload className="h-6 w-6 text-gray-400" />
          <p className="text-center text-lg font-medium text-white">
            {uploading ? 'Uploading...' : isDragActive ? 'Drop your file here!' : 'Drop photo or video files here'}
          </p>
          <p className="mt-1 text-center text-sm text-gray-400">
            Images up to 10MB, Videos up to 50MB
          </p>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="flex items-center space-x-2 rounded-lg bg-red-500/10 border border-red-500/20 p-3">
          <AlertCircle className="h-5 w-5 text-red-400 flex-shrink-0" />
          <p className="text-sm text-red-400">{error}</p>
        </div>
      )}

      {/* File List */}
      {files.length > 0 && (
        <div className="space-y-2">
          {files.map((file) => (
            <div key={file.id} className="flex items-center space-x-3 rounded-lg bg-gray-800/50 p-3">
              {file.type.startsWith('image/') ? (
                <FileImage className="h-5 w-5 text-blue-400 flex-shrink-0" />
              ) : (
                <FileVideo className="h-5 w-5 text-purple-400 flex-shrink-0" />
              )}
              
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">{file.name}</p>
                <p className="text-xs text-gray-400">
                  {(file.size / (1024 * 1024)).toFixed(1)} MB
                </p>
                
                {/* Progress Bar */}
                {file.status === 'uploading' && (
                  <div className="mt-2 w-full bg-gray-700 rounded-full h-1.5">
                    <div 
                      className="bg-blue-500 h-1.5 rounded-full transition-all duration-300"
                      style={{ width: `${file.progress}%` }}
                    />
                  </div>
                )}
              </div>

              {/* Status Icon */}
              <div className="flex-shrink-0">
                {file.status === 'uploading' && (
                  <Loader2 className="h-4 w-4 text-blue-400 animate-spin" />
                )}
                {file.status === 'completed' && (
                  <CheckCircle className="h-4 w-4 text-green-400" />
                )}
                {file.status === 'error' && (
                  <AlertCircle className="h-4 w-4 text-red-400" />
                )}
                {file.status === 'pending' && (
                  <button
                    onClick={() => removeFile(file.id)}
                    className="p-1 hover:bg-gray-700 rounded"
                  >
                    <X className="h-3 w-3 text-gray-400" />
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Cancel Button */}
      {uploading && (
        <button
          onClick={cancelUpload}
          className="w-full py-2 px-4 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
        >
          Cancel Upload
        </button>
      )}
    </div>
  );
}
