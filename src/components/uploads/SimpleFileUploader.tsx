'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { Upload, X, AlertCircle, CheckCircle } from 'lucide-react';

interface SimpleFileUploaderProps {
  onUploadComplete?: (result: any) => void;
}

export function SimpleFileUploader({ onUploadComplete }: SimpleFileUploaderProps) {
  const { user } = useUser();
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [currentFileName, setCurrentFileName] = useState<string>('');
  const [uploadSuccessful, setUploadSuccessful] = useState(false);

  // Add navigation tracking to detect unexpected page reloads
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (uploading) {
        console.log('⚠️ PAGE UNLOAD DETECTED DURING UPLOAD!');
        e.preventDefault();
        e.returnValue = 'Upload in progress. Are you sure you want to leave?';
        return 'Upload in progress. Are you sure you want to leave?';
      }
    };

    const handleVisibilityChange = () => {
      if (uploading) {
        console.log('👁️ PAGE VISIBILITY CHANGED DURING UPLOAD:', document.visibilityState);
      }
    };

    const handleFocus = () => {
      if (uploading) {
        console.log('🔍 WINDOW FOCUS DURING UPLOAD');
      }
    };

    const handleBlur = () => {
      if (uploading) {
        console.log('😴 WINDOW BLUR DURING UPLOAD');
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', handleBlur);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('blur', handleBlur);
    };
  }, [uploading]);

  const validateFile = (file: File): { isValid: boolean; error?: string } => {
    console.log('🔍 VALIDATION START - File:', file.name, 'Type:', file.type, 'Size:', file.size, 'bytes');
    
    // Check file type - STRICT validation
    const allowedImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    const allowedVideoTypes = ['video/mp4', 'video/quicktime', 'video/mov', 'video/avi', 'video/webm', 'video/x-msvideo', 'video/3gpp', 'video/x-ms-wmv'];
    const allAllowedTypes = [...allowedImageTypes, ...allowedVideoTypes];

    const fileType = file.type.toLowerCase();
    console.log('🔍 File type (lowercase):', fileType);
    console.log('🔍 Allowed types:', allAllowedTypes);

    if (!allAllowedTypes.includes(fileType)) {
      console.log('❌ VALIDATION FAILED - File type not allowed:', fileType);
      return {
        isValid: false,
        error: `"${file.name}" is not supported. File type: ${file.type}. Only images (JPEG, PNG, GIF, WebP) and videos (MP4, MOV, AVI, WebM) are allowed.`
      };
    }

    // Check if it's a video and apply video-specific size limit
    const isVideo = allowedVideoTypes.includes(fileType);
    const isImage = allowedImageTypes.includes(fileType);
    
    console.log('🔍 File classification - isVideo:', isVideo, 'isImage:', isImage);

    // Different size limits for different file types
    let maxSize;
    if (isVideo) {
      maxSize = 50 * 1024 * 1024; // 50MB for videos
    } else if (isImage) {
      maxSize = 10 * 1024 * 1024; // 10MB for images
    } else {
      console.log('❌ VALIDATION FAILED - Unknown file classification');
      return {
        isValid: false,
        error: `"${file.name}" could not be classified as image or video.`
      };
    }

    console.log('🔍 Size check - File size:', file.size, 'Max allowed:', maxSize);

    if (file.size > maxSize) {
      const fileSizeMB = (file.size / (1024 * 1024)).toFixed(1);
      const maxSizeMB = (maxSize / (1024 * 1024)).toFixed(0);
      console.log('❌ VALIDATION FAILED - File too large:', fileSizeMB, 'MB, max:', maxSizeMB, 'MB');
      return {
        isValid: false,
        error: `"${file.name}" is too large (${fileSizeMB}MB). Maximum file size is ${maxSizeMB}MB for ${isVideo ? 'videos' : 'images'}.`
      };
    }

    console.log('✅ VALIDATION PASSED - File is valid');
    return { isValid: true };
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    console.log('🎯 UPLOAD PROCESS START');
    console.log('🎯 Event target:', event.target);
    console.log('🎯 Files:', event.target.files);
    
    const file = event.target.files?.[0];
    if (!file) {
      console.log('❌ No file selected');
      return;
    }

    console.log('📁 FILE SELECTED:', {
      name: file.name,
      type: file.type,
      size: file.size,
      lastModified: file.lastModified
    });

    // Clear previous messages and reset state
    console.log('🧹 Clearing previous state...');
    setError(null);
    setSuccess(null);
    setUploadSuccessful(false);

    // Validate file
    console.log('🔍 Starting file validation...');
    const validation = validateFile(file);
    if (!validation.isValid) {
      console.log('❌ VALIDATION FAILED, setting error:', validation.error);
      setError(validation.error!);
      return;
    }
    console.log('✅ File validation passed');

    if (!user) {
      console.log('❌ No user found, authentication required');
      setError('Please log in to upload files');
      return;
    }
    console.log('✅ User authenticated:', user.id);

    try {
      console.log('🚀 STARTING UPLOAD PROCESS...');
      setUploading(true);
      setUploadProgress(0);
      setCurrentFileName(file.name);

      const formData = new FormData();
      formData.append('file', file);
      console.log('📦 FormData created with file');

      // Use XMLHttpRequest for upload progress tracking
      console.log('🌐 Creating XMLHttpRequest...');
      const result = await new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        console.log('🌐 XMLHttpRequest created');

        // Track upload progress
        xhr.upload.addEventListener('progress', (e) => {
          console.log('📊 PROGRESS EVENT:', {
            lengthComputable: e.lengthComputable,
            loaded: e.loaded,
            total: e.total,
            timestamp: new Date().toISOString()
          });
          
          if (e.lengthComputable) {
            const percentComplete = Math.round((e.loaded / e.total) * 100);
            setUploadProgress(percentComplete);
            console.log(`📊 Upload progress: ${percentComplete}% (${e.loaded}/${e.total} bytes)`);
          } else {
            console.log('📊 Upload progress: length not computable, setting to 50%');
            setUploadProgress(50);
          }
        });

        // Add comprehensive event listeners for debugging
        xhr.upload.addEventListener('loadstart', (e) => {
          console.log('📤 UPLOAD LOADSTART EVENT:', e);
          setUploadProgress(1);
        });

        xhr.upload.addEventListener('loadend', (e) => {
          console.log('📤 UPLOAD LOADEND EVENT:', e);
          setUploadProgress(99);
        });

        xhr.upload.addEventListener('abort', (e) => {
          console.log('🛑 UPLOAD ABORT EVENT:', e);
          reject(new Error('Upload was aborted'));
        });

        xhr.upload.addEventListener('error', (e) => {
          console.log('❌ UPLOAD ERROR EVENT:', e);
          reject(new Error('Upload error occurred'));
        });

        // Handle main request events
        xhr.addEventListener('readystatechange', () => {
          console.log('🔄 READYSTATE CHANGE:', {
            readyState: xhr.readyState,
            status: xhr.status,
            statusText: xhr.statusText,
            timestamp: new Date().toISOString()
          });
        });

        xhr.addEventListener('load', () => {
          console.log('✅ XHR LOAD EVENT:', {
            status: xhr.status,
            statusText: xhr.statusText,
            responseText: xhr.responseText?.substring(0, 200) + '...',
            timestamp: new Date().toISOString()
          });
          
          if (xhr.status >= 200 && xhr.status < 300) {
            try {
              const response = JSON.parse(xhr.responseText);
              console.log('✅ Response parsed successfully:', response);
              resolve(response);
            } catch (error) {
              console.log('❌ Failed to parse response:', error);
              reject(new Error('Invalid response format'));
            }
          } else {
            console.log('��� HTTP error status:', xhr.status);
            reject(new Error(`Upload failed: ${xhr.status} ${xhr.statusText}`));
          }
        });

        xhr.addEventListener('error', (e) => {
          console.log('❌ XHR ERROR EVENT:', e);
          reject(new Error('Upload failed: Network error'));
        });

        xhr.addEventListener('timeout', (e) => {
          console.log('⏰ XHR TIMEOUT EVENT:', e);
          reject(new Error('Upload failed: Timeout'));
        });

        xhr.addEventListener('abort', (e) => {
          console.log('🛑 XHR ABORT EVENT:', e);
          reject(new Error('Upload failed: Request aborted'));
        });

        // Start upload
        console.log('🚀 Opening XHR connection to /api/secure-upload');
        xhr.open('POST', '/api/secure-upload');
        xhr.timeout = 600000; // 10 minutes timeout for large video files
        console.log('📤 Sending FormData...');
        xhr.send(formData);
        console.log('📤 XHR request sent');
      });

      console.log('🎉 UPLOAD PROMISE RESOLVED:', result);

      if (result && result.success) {
        console.log('✅ Upload marked as successful');
        setUploadProgress(100);
        setUploadSuccessful(true);
        setSuccess(`✅ "${file.name}" uploaded successfully! Gallery updated.`);
        
        // Call onUploadComplete callback to refresh the gallery
        if (onUploadComplete) {
          console.log('📞 Calling onUploadComplete callback...');
          onUploadComplete(result);
        } else {
          console.log('⚠️ No onUploadComplete callback provided');
        }
        
        // Keep progress bar visible for 3 seconds to show completion
        console.log('⏰ Setting timeout to hide progress bar in 3 seconds...');
        setTimeout(() => {
          console.log('🧹 Hiding progress bar and resetting state');
          setUploading(false);
          setUploadProgress(0);
          setCurrentFileName('');
        }, 3000);
        
        // Clear success message after 5 seconds
        setTimeout(() => {
          console.log('🧹 Clearing success message');
          setSuccess(null);
        }, 5000);

        // Reset file input
        console.log('🧹 Resetting file input');
        event.target.value = '';
      } else {
        console.log('❌ Upload result indicates failure:', result);
        throw new Error(result?.error || 'Upload failed - no success flag');
      }
    } catch (error: any) {
      console.log('❌ UPLOAD PROCESS FAILED:', {
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      });
      
      setError(`Upload failed: ${error.message}`);
      
      // Reset upload state on error
      console.log('🧹 Resetting upload state due to error');
      setUploading(false);
      setUploadProgress(0);
      setCurrentFileName('');
    }
    
    console.log('🎯 UPLOAD PROCESS END');
  };

  return (
    <div className="w-full space-y-4">
      {/* Upload Area */}
      <div className="relative">
        <input
          type="file"
          accept="image/jpeg,image/png,image/gif,image/webp,video/mp4,video/quicktime,video/avi,video/webm,video/x-msvideo,video/3gpp,video/x-ms-wmv,.mp4,.mov,.avi,.webm,.wmv,.3gp"
          onChange={handleFileSelect}
          disabled={uploading}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer disabled:cursor-not-allowed"
          id="file-upload"
        />
        <label
          htmlFor="file-upload"
          className={`flex h-40 cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-600 transition-colors hover:border-blue-500 hover:bg-blue-500/10 ${
            uploading ? 'pointer-events-none opacity-50' : ''
          }`}
        >
          <div className="flex flex-col items-center justify-center space-y-2">
            <Upload className="h-6 w-6 text-gray-400" />
            <p className="text-center text-lg font-medium text-white">
              {uploading ? 'Uploading...' : 'Click to select photo or video'}
            </p>
            <p className="mt-1 text-center text-sm text-gray-400">
              {uploading ? 'Please wait...' : 'Images (10MB max) • Videos (50MB max)'}
            </p>
            <div className="mt-2 text-center text-xs text-gray-500">
              <div className="mb-1">
                <span className="text-green-400">✓</span> Images: JPEG, PNG, GIF, WebP (10MB max)
              </div>
              <div>
                <span className="text-purple-400">✓</span> Videos: MP4, MOV, AVI, WebM, WMV, 3GP (50MB max)
              </div>
            </div>
          </div>
        </label>
      </div>

      {/* Error Message */}
      {error && (
        <div className="rounded-lg border border-red-500/20 bg-red-900/20 p-4">
          <div className="flex items-start space-x-3">
            <AlertCircle className="h-5 w-5 text-red-400 flex-shrink-0 mt-0.5" />
            <div className="flex-1">
              <h3 className="text-sm font-medium text-red-300">Upload Error</h3>
              <p className="mt-1 text-sm text-red-200">{error}</p>
              <p className="mt-2 text-xs text-red-300/80">
                Click the X to dismiss this message.
              </p>
            </div>
            <button 
              onClick={() => setError(null)}
              className="flex-shrink-0 text-red-300 hover:text-red-100 transition-colors"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}

      {/* Success Message */}
      {success && (
        <div className="rounded-lg border border-green-500/20 bg-green-900/20 p-4">
          <div className="flex items-start space-x-3">
            <CheckCircle className="h-5 w-5 text-green-400 flex-shrink-0 mt-0.5" />
            <div className="flex-1">
              <h3 className="text-sm font-medium text-green-300">Upload Successful</h3>
              <p className="mt-1 text-sm text-green-200">{success}</p>
            </div>
            <button 
              onClick={() => setSuccess(null)}
              className="flex-shrink-0 text-green-300 hover:text-green-100 transition-colors"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}

      {/* Upload Progress */}
      {uploading && (
        <div className="rounded-lg bg-gray-800 p-4">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="animate-spin rounded-full h-5 w-5 border-2 border-blue-400 border-t-transparent" />
                <div>
                  <p className="text-sm font-medium text-white">Uploading "{currentFileName}"</p>
                  <p className="text-xs text-gray-400">Please don't close this page</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium text-blue-400">{uploadProgress}%</p>
                <p className="text-xs text-gray-400">
                  {uploadProgress === 0 ? 'Starting...' :
                   uploadProgress < 99 ? 'Uploading...' :
                   uploadProgress === 99 ? 'Processing...' :
                   uploadProgress === 100 ? 'Complete!' : 'Uploading...'}
                </p>
              </div>
            </div>
            
            {/* Progress Bar */}
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div 
                className="bg-blue-500 h-2 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${uploadProgress}%` }}
              />
            </div>
            
            {uploadProgress >= 99 && (
              <p className="text-xs text-yellow-400 text-center">
                {uploadProgress === 99 ? 'Upload complete, processing file on server...' : 
                 uploadProgress === 100 ? '✅ File processed successfully!' : ''}
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
}