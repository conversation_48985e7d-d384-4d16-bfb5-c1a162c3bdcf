'use client';

import { useEffect, useRef, useState } from 'react';
import { ProcessedDetection } from '@/services/detectionMetadata';

interface DetectionCanvasProps {
  imageUrl: string;
  detections: ProcessedDetection[];
  scoreThreshold: number;
  blurStrength: number;
  showBoundingBoxes: boolean;
  showLabels: boolean;
  blurDetections: boolean;
}

export function DetectionCanvas({
  imageUrl,
  detections,
  scoreThreshold,
  blurStrength,
  showBoundingBoxes,
  showLabels,
  blurDetections,
}: DetectionCanvasProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageElement, setImageElement] = useState<HTMLImageElement | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Load image
  useEffect(() => {
    const img = new Image();
    img.crossOrigin = 'anonymous'; // Enable CORS for Supabase images

    img.onload = () => {
      setImageElement(img);
      setImageLoaded(true);
      setError(null);
      console.log(`✅ Image loaded: ${img.width}x${img.height}`);
    };

    img.onerror = () => {
      setError('Failed to load image');
      console.error('Failed to load image from URL:', imageUrl);
    };

    img.src = imageUrl;

    return () => {
      img.src = '';
    };
  }, [imageUrl]);

  // Render detections on canvas
  useEffect(() => {
    if (!canvasRef.current || !imageElement || !imageLoaded) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size to match image
    canvas.width = imageElement.width;
    canvas.height = imageElement.height;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw original image
    ctx.drawImage(imageElement, 0, 0);

    // Filter detections by threshold
    const filteredDetections = detections.filter(d => d.score >= scoreThreshold);

    console.log(`🎨 Rendering ${filteredDetections.length} detections (threshold: ${scoreThreshold})`);

    // Apply blur to detected regions
    if (blurDetections && filteredDetections.length > 0) {
      applyBlurToDetections(ctx, canvas, imageElement, filteredDetections, blurStrength);
    }

    // Draw bounding boxes and labels
    if (showBoundingBoxes || showLabels) {
      drawBoundingBoxes(ctx, filteredDetections, showBoundingBoxes, showLabels);
    }

  }, [imageElement, imageLoaded, detections, scoreThreshold, blurStrength, showBoundingBoxes, showLabels, blurDetections]);

  // Apply blur effect to detected regions
  const applyBlurToDetections = (
    ctx: CanvasRenderingContext2D,
    canvas: HTMLCanvasElement,
    image: HTMLImageElement,
    detections: ProcessedDetection[],
    blurStrength: number
  ) => {
    console.log(`🔵 Applying blur to ${detections.length} detections with strength ${blurStrength}px`);

    detections.forEach((detection, index) => {
      const { x1, y1, width, height } = detection;

      // Ensure dimensions are positive and within bounds
      const safeX = Math.max(0, Math.min(x1, canvas.width));
      const safeY = Math.max(0, Math.min(y1, canvas.height));
      const safeWidth = Math.min(width, canvas.width - safeX);
      const safeHeight = Math.min(height, canvas.height - safeY);

      if (safeWidth <= 0 || safeHeight <= 0) return;

      // Two-canvas technique for proper blur application
      // Canvas 1: Source canvas with the region
      const sourceCanvas = document.createElement('canvas');
      sourceCanvas.width = safeWidth;
      sourceCanvas.height = safeHeight;
      const sourceCtx = sourceCanvas.getContext('2d');
      if (!sourceCtx) return;

      // Canvas 2: Destination canvas with filter applied
      const blurCanvas = document.createElement('canvas');
      blurCanvas.width = safeWidth;
      blurCanvas.height = safeHeight;
      const blurCtx = blurCanvas.getContext('2d');
      if (!blurCtx) return;

      // Step 1: Copy region to source canvas
      sourceCtx.drawImage(
        canvas,
        safeX, safeY, safeWidth, safeHeight,
        0, 0, safeWidth, safeHeight
      );

      // Step 2: Apply blur filter to destination context BEFORE drawing
      blurCtx.filter = `blur(${blurStrength}px)`;

      // Step 3: Draw FROM source canvas TO blur canvas (this applies the filter)
      blurCtx.drawImage(sourceCanvas, 0, 0);

      // Step 4: Draw blurred result back to main canvas
      ctx.drawImage(blurCanvas, 0, 0, safeWidth, safeHeight, safeX, safeY, safeWidth, safeHeight);

      console.log(`  ✅ Blurred region ${index + 1}: (${safeX}, ${safeY}) ${safeWidth}x${safeHeight}`);
    });
  };

  // Draw bounding boxes and labels
  const drawBoundingBoxes = (
    ctx: CanvasRenderingContext2D,
    detections: ProcessedDetection[],
    showBoxes: boolean,
    showLabels: boolean
  ) => {
    detections.forEach((detection, index) => {
      const { x1, y1, x2, y2, label, score } = detection;

      // Color based on detection index - using more subtle opacity
      const hue = (index * 137) % 360; // Golden angle for distinct colors
      const color = `hsl(${hue}, 70%, 50%)`;
      const subtleColor = `hsla(${hue}, 70%, 50%, 0.5)`; // 50% opacity for subtle appearance

      if (showBoxes) {
        const width = x2 - x1;
        const height = y2 - y1;
        const cornerLength = Math.min(20, width * 0.15, height * 0.15); // Corner markers 15% of box size, max 20px

        // Set subtle dashed line style
        ctx.strokeStyle = subtleColor;
        ctx.lineWidth = 1.5;
        ctx.setLineDash([5, 5]); // Dashed pattern: 5px dash, 5px gap

        // Draw subtle dashed rectangle
        ctx.strokeRect(x1, y1, width, height);

        // Draw corner markers for emphasis (solid lines)
        ctx.setLineDash([]); // Reset to solid
        ctx.strokeStyle = color; // Full opacity for corners
        ctx.lineWidth = 2;

        // Top-left corner
        ctx.beginPath();
        ctx.moveTo(x1, y1 + cornerLength);
        ctx.lineTo(x1, y1);
        ctx.lineTo(x1 + cornerLength, y1);
        ctx.stroke();

        // Top-right corner
        ctx.beginPath();
        ctx.moveTo(x2 - cornerLength, y1);
        ctx.lineTo(x2, y1);
        ctx.lineTo(x2, y1 + cornerLength);
        ctx.stroke();

        // Bottom-right corner
        ctx.beginPath();
        ctx.moveTo(x2, y2 - cornerLength);
        ctx.lineTo(x2, y2);
        ctx.lineTo(x2 - cornerLength, y2);
        ctx.stroke();

        // Bottom-left corner
        ctx.beginPath();
        ctx.moveTo(x1 + cornerLength, y2);
        ctx.lineTo(x1, y2);
        ctx.lineTo(x1, y2 - cornerLength);
        ctx.stroke();

        // Reset line dash
        ctx.setLineDash([]);
      }

      if (showLabels) {
        // Draw label with semi-transparent background
        const labelText = `${label} ${(score * 100).toFixed(0)}%`;
        ctx.font = '14px -apple-system, BlinkMacSystemFont, "Segoe UI", Arial, sans-serif';
        const textMetrics = ctx.measureText(labelText);
        const textWidth = textMetrics.width;
        const padding = 6;
        const textHeight = 18;

        // Semi-transparent background
        ctx.fillStyle = `hsla(${hue}, 70%, 50%, 0.85)`;
        ctx.fillRect(x1, y1 - textHeight - padding, textWidth + padding * 2, textHeight + padding);

        // Text with subtle shadow for readability
        ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
        ctx.shadowBlur = 2;
        ctx.shadowOffsetX = 1;
        ctx.shadowOffsetY = 1;
        ctx.fillStyle = 'white';
        ctx.fillText(labelText, x1 + padding, y1 - padding - 2);

        // Reset shadow
        ctx.shadowColor = 'transparent';
        ctx.shadowBlur = 0;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;
      }
    });
  };

  if (error) {
    return (
      <div className="flex items-center justify-center h-96 bg-navy-dark rounded-lg border border-red-400">
        <p className="text-red-400">{error}</p>
      </div>
    );
  }

  if (!imageLoaded) {
    return (
      <div className="flex items-center justify-center h-96 bg-navy-dark rounded-lg border border-navy-light">
        <div className="flex flex-col items-center gap-2">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400"></div>
          <p className="text-gray-400">Loading image...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full">
      <canvas
        ref={canvasRef}
        className="w-full h-auto rounded-lg border border-navy-light shadow-lg"
      />
    </div>
  );
}
