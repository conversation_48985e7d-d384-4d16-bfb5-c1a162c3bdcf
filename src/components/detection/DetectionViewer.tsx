'use client';

import { useState, useEffect } from 'react';
import { SlidersHorizontal, Eye, EyeOff, Tag, Droplets, RefreshCw } from 'lucide-react';
import { DetectionCanvas } from './DetectionCanvas';
import { fetchAndProcessDetections, ProcessedDetection, DetectionMetadata } from '@/services/detectionMetadata';

interface DetectionViewerProps {
  imageUrl: string;
  metadataPath: string;
  initialThreshold?: number;
  initialBlurStrength?: number;
}

export function DetectionViewer({
  imageUrl,
  metadataPath,
  initialThreshold = 0.3,
  initialBlurStrength = 35,
}: DetectionViewerProps) {
  const [metadata, setMetadata] = useState<DetectionMetadata | null>(null);
  const [allDetections, setAllDetections] = useState<ProcessedDetection[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // User-adjustable parameters
  const [scoreThreshold, setScoreThreshold] = useState(initialThreshold);
  const [blurStrength, setBlurStrength] = useState(initialBlurStrength);
  const [showBoundingBoxes, setShowBoundingBoxes] = useState(true);
  const [showLabels, setShowLabels] = useState(true);
  const [blurDetections, setBlurDetections] = useState(false);

  // Load detection metadata
  useEffect(() => {
    const loadDetections = async () => {
      setLoading(true);
      setError(null);

      try {
        console.log('📥 Fetching detection metadata from:', metadataPath);

        const { metadata: meta, detections } = await fetchAndProcessDetections(
          metadataPath,
          0.01 // Get all detections, we'll filter client-side
        );

        setMetadata(meta);
        setAllDetections(detections);
        console.log(`✅ Loaded ${detections.length} total detections`);
      } catch (err: any) {
        console.error('❌ Failed to load detections:', err);
        setError(err.message || 'Failed to load detection metadata');
      } finally {
        setLoading(false);
      }
    };

    loadDetections();
  }, [metadataPath]);

  // Calculate visible detection count
  const visibleDetectionCount = allDetections.filter(d => d.score >= scoreThreshold).length;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96 bg-navy-dark rounded-lg border border-navy-light">
        <div className="flex flex-col items-center gap-3">
          <RefreshCw className="h-8 w-8 text-green-400 animate-spin" />
          <p className="text-gray-400">Loading detections...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-400/10 border border-red-400 rounded-lg p-6">
        <h3 className="text-red-400 font-semibold mb-2">Error Loading Detections</h3>
        <p className="text-red-300 text-sm">{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Header */}
      <div className="bg-navy-dark rounded-lg border border-navy-light p-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-white">Detection Results</h3>
            <p className="text-sm text-gray-400">
              Showing {visibleDetectionCount} of {allDetections.length} detections
            </p>
          </div>
          <div className="text-right">
            <p className="text-2xl font-bold text-green-400">{visibleDetectionCount}</p>
            <p className="text-xs text-gray-400">Visible</p>
          </div>
        </div>
      </div>

      {/* Canvas with Detections */}
      <div className="bg-navy-dark rounded-lg border border-navy-light p-4">
        <DetectionCanvas
          imageUrl={imageUrl}
          detections={allDetections}
          scoreThreshold={scoreThreshold}
          blurStrength={blurStrength}
          showBoundingBoxes={showBoundingBoxes}
          showLabels={showLabels}
          blurDetections={blurDetections}
        />
      </div>

      {/* Controls */}
      <div className="bg-navy-dark rounded-lg border border-navy-light p-6 space-y-6">
        <h4 className="text-lg font-semibold text-white mb-4">Visualization Controls</h4>

        {/* Confidence Threshold Slider */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
              <SlidersHorizontal className="h-4 w-4 text-green-400" />
              Confidence Threshold
            </label>
            <span className="text-sm font-mono text-green-400">
              {(scoreThreshold * 100).toFixed(0)}%
            </span>
          </div>
          <input
            type="range"
            min="0"
            max="1"
            step="0.01"
            value={scoreThreshold}
            onChange={(e) => setScoreThreshold(parseFloat(e.target.value))}
            className="w-full h-2 bg-navy-light rounded-lg appearance-none cursor-pointer
                     [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:w-4
                     [&::-webkit-slider-thumb]:h-4 [&::-webkit-slider-thumb]:rounded-full
                     [&::-webkit-slider-thumb]:bg-green-400 [&::-webkit-slider-thumb]:cursor-pointer
                     [&::-moz-range-thumb]:w-4 [&::-moz-range-thumb]:h-4
                     [&::-moz-range-thumb]:rounded-full [&::-moz-range-thumb]:bg-green-400
                     [&::-moz-range-thumb]:border-0 [&::-moz-range-thumb]:cursor-pointer"
          />
          <p className="text-xs text-gray-400">
            Filter detections by minimum confidence level
          </p>
        </div>

        {/* Blur Strength Slider */}
        {blurDetections && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                <Droplets className="h-4 w-4 text-blue-400" />
                Blur Strength
              </label>
              <span className="text-sm font-mono text-blue-400">
                {blurStrength}px
              </span>
            </div>
            <input
              type="range"
              min="5"
              max="100"
              step="5"
              value={blurStrength}
              onChange={(e) => setBlurStrength(parseInt(e.target.value))}
              className="w-full h-2 bg-navy-light rounded-lg appearance-none cursor-pointer
                       [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:w-4
                       [&::-webkit-slider-thumb]:h-4 [&::-webkit-slider-thumb]:rounded-full
                       [&::-webkit-slider-thumb]:bg-blue-400 [&::-webkit-slider-thumb]:cursor-pointer
                       [&::-moz-range-thumb]:w-4 [&::-moz-range-thumb]:h-4
                       [&::-moz-range-thumb]:rounded-full [&::-moz-range-thumb]:bg-blue-400
                       [&::-moz-range-thumb]:border-0 [&::-moz-range-thumb]:cursor-pointer"
            />
            <p className="text-xs text-gray-400">
              Adjust the blur intensity for detected regions
            </p>
          </div>
        )}

        {/* Toggle Controls */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Bounding Boxes Toggle */}
          <button
            onClick={() => setShowBoundingBoxes(!showBoundingBoxes)}
            className={`flex items-center justify-between p-3 rounded-lg border transition-all ${
              showBoundingBoxes
                ? 'bg-green-400/10 border-green-400 text-green-400'
                : 'bg-navy-light border-navy-light text-gray-400 hover:border-gray-400'
            }`}
          >
            <div className="flex items-center gap-2">
              {showBoundingBoxes ? (
                <Eye className="h-4 w-4" />
              ) : (
                <EyeOff className="h-4 w-4" />
              )}
              <span className="text-sm font-medium">Boxes</span>
            </div>
          </button>

          {/* Labels Toggle */}
          <button
            onClick={() => setShowLabels(!showLabels)}
            className={`flex items-center justify-between p-3 rounded-lg border transition-all ${
              showLabels
                ? 'bg-green-400/10 border-green-400 text-green-400'
                : 'bg-navy-light border-navy-light text-gray-400 hover:border-gray-400'
            }`}
          >
            <div className="flex items-center gap-2">
              {showLabels ? (
                <Eye className="h-4 w-4" />
              ) : (
                <EyeOff className="h-4 w-4" />
              )}
              <span className="text-sm font-medium">Labels</span>
            </div>
          </button>

          {/* Blur Toggle */}
          <button
            onClick={() => setBlurDetections(!blurDetections)}
            className={`flex items-center justify-between p-3 rounded-lg border transition-all ${
              blurDetections
                ? 'bg-blue-400/10 border-blue-400 text-blue-400'
                : 'bg-navy-light border-navy-light text-gray-400 hover:border-gray-400'
            }`}
          >
            <div className="flex items-center gap-2">
              {blurDetections ? (
                <Eye className="h-4 w-4" />
              ) : (
                <EyeOff className="h-4 w-4" />
              )}
              <span className="text-sm font-medium">Blur</span>
            </div>
          </button>
        </div>
      </div>

      {/* Detection List */}
      {visibleDetectionCount > 0 && (
        <div className="bg-navy-dark rounded-lg border border-navy-light p-6">
          <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            <Tag className="h-5 w-5 text-green-400" />
            Detected Objects ({visibleDetectionCount})
          </h4>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {allDetections
              .filter(d => d.score >= scoreThreshold)
              .sort((a, b) => b.score - a.score)
              .map((detection, index) => (
                <div
                  key={detection.box_id}
                  className="flex items-center justify-between p-3 bg-navy-light rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{
                        backgroundColor: `hsl(${(index * 137) % 360}, 70%, 50%)`,
                      }}
                    />
                    <span className="text-white font-medium capitalize">
                      {detection.label}
                    </span>
                  </div>
                  <div className="flex items-center gap-4">
                    <span className="text-xs text-gray-400 font-mono">
                      {Math.round(detection.width)}×{Math.round(detection.height)}
                    </span>
                    <span className="text-sm font-semibold text-green-400">
                      {(detection.score * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
              ))}
          </div>
        </div>
      )}
    </div>
  );
}
