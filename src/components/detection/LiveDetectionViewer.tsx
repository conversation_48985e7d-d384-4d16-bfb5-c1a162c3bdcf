'use client';

import { useState, useMemo } from 'react';
import { SlidersHorizontal, Eye, EyeOff, Tag, Droplets } from 'lucide-react';
import { DetectionCanvas } from './DetectionCanvas';
import { processDetections, DetectionMetadata } from '@/services/detectionMetadata';

interface LiveDetectionViewerProps {
  imageUrl: string;
  detectionMetadata: DetectionMetadata;
  initialThreshold?: number;
  initialBlurStrength?: number;
}

/**
 * Live Detection Viewer - Renders detections from JSON metadata directly
 * No Supabase fetching required - metadata is passed in as a prop
 *
 * Usage:
 * ```tsx
 * const { detect, result } = useImageDetection();
 *
 * await detect(filePath, 'person', 0.1);
 *
 * <LiveDetectionViewer
 *   imageUrl={originalImageUrl}
 *   detectionMetadata={result.metadata}
 * />
 * ```
 */
export function LiveDetectionViewer({
  imageUrl,
  detectionMetadata,
  initialThreshold = 0.3,
  initialBlurStrength = 35,
}: LiveDetectionViewerProps) {
  // Process all detections with very low threshold to get everything
  const allDetections = useMemo(() => {
    return processDetections(detectionMetadata, 0.01);
  }, [detectionMetadata]);

  // User-adjustable parameters
  const [scoreThreshold, setScoreThreshold] = useState(initialThreshold);
  const [blurStrength, setBlurStrength] = useState(initialBlurStrength);
  const [showBoundingBoxes, setShowBoundingBoxes] = useState(true);
  const [showLabels, setShowLabels] = useState(true);
  const [blurDetections, setBlurDetections] = useState(false);

  // Calculate visible detection count
  const visibleDetectionCount = allDetections.filter(d => d.score >= scoreThreshold).length;

  return (
    <div className="space-y-6">
      {/* Stats Header */}
      <div className="bg-navy-dark rounded-lg border border-navy-light p-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-white">Detection Results</h3>
            <p className="text-sm text-gray-400">
              Showing {visibleDetectionCount} of {allDetections.length} detections
            </p>
          </div>
          <div className="text-right">
            <p className="text-2xl font-bold text-green-400">{visibleDetectionCount}</p>
            <p className="text-xs text-gray-400">Visible</p>
          </div>
        </div>
      </div>

      {/* Canvas with Detections */}
      <div className="bg-navy-dark rounded-lg border border-navy-light p-4">
        <DetectionCanvas
          imageUrl={imageUrl}
          detections={allDetections}
          scoreThreshold={scoreThreshold}
          blurStrength={blurStrength}
          showBoundingBoxes={showBoundingBoxes}
          showLabels={showLabels}
          blurDetections={blurDetections}
        />
      </div>

      {/* Controls */}
      <div className="bg-navy-dark rounded-lg border border-navy-light p-6 space-y-6">
        <h4 className="text-lg font-semibold text-white mb-4">Visualization Controls</h4>

        {/* Confidence Threshold Slider */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
              <SlidersHorizontal className="h-4 w-4 text-green-400" />
              Confidence Threshold
            </label>
            <span className="text-sm font-mono text-green-400">
              {(scoreThreshold * 100).toFixed(0)}%
            </span>
          </div>
          <input
            type="range"
            min="0"
            max="1"
            step="0.01"
            value={scoreThreshold}
            onChange={(e) => setScoreThreshold(parseFloat(e.target.value))}
            className="w-full h-2 bg-navy-light rounded-lg appearance-none cursor-pointer
                     [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:w-4
                     [&::-webkit-slider-thumb]:h-4 [&::-webkit-slider-thumb]:rounded-full
                     [&::-webkit-slider-thumb]:bg-green-400 [&::-webkit-slider-thumb]:cursor-pointer
                     [&::-moz-range-thumb]:w-4 [&::-moz-range-thumb]:h-4
                     [&::-moz-range-thumb]:rounded-full [&::-moz-range-thumb]:bg-green-400
                     [&::-moz-range-thumb]:border-0 [&::-moz-range-thumb]:cursor-pointer"
          />
          <p className="text-xs text-gray-400">
            Filter detections by minimum confidence level
          </p>
        </div>

        {/* Blur Strength Slider */}
        {blurDetections && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-300 flex items-center gap-2">
                <Droplets className="h-4 w-4 text-blue-400" />
                Blur Strength
              </label>
              <span className="text-sm font-mono text-blue-400">
                {blurStrength}px
              </span>
            </div>
            <input
              type="range"
              min="5"
              max="100"
              step="5"
              value={blurStrength}
              onChange={(e) => setBlurStrength(parseInt(e.target.value))}
              className="w-full h-2 bg-navy-light rounded-lg appearance-none cursor-pointer
                       [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:w-4
                       [&::-webkit-slider-thumb]:h-4 [&::-webkit-slider-thumb]:rounded-full
                       [&::-webkit-slider-thumb]:bg-blue-400 [&::-webkit-slider-thumb]:cursor-pointer
                       [&::-moz-range-thumb]:w-4 [&::-moz-range-thumb]:h-4
                       [&::-moz-range-thumb]:rounded-full [&::-moz-range-thumb]:bg-blue-400
                       [&::-moz-range-thumb]:border-0 [&::-moz-range-thumb]:cursor-pointer"
            />
            <p className="text-xs text-gray-400">
              Adjust the blur intensity for detected regions
            </p>
          </div>
        )}

        {/* Toggle Controls */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Bounding Boxes Toggle */}
          <button
            onClick={() => setShowBoundingBoxes(!showBoundingBoxes)}
            className={`flex items-center justify-between p-3 rounded-lg border transition-all ${
              showBoundingBoxes
                ? 'bg-green-400/10 border-green-400 text-green-400'
                : 'bg-navy-light border-navy-light text-gray-400 hover:border-gray-400'
            }`}
          >
            <div className="flex items-center gap-2">
              {showBoundingBoxes ? (
                <Eye className="h-4 w-4" />
              ) : (
                <EyeOff className="h-4 w-4" />
              )}
              <span className="text-sm font-medium">Boxes</span>
            </div>
          </button>

          {/* Labels Toggle */}
          <button
            onClick={() => setShowLabels(!showLabels)}
            className={`flex items-center justify-between p-3 rounded-lg border transition-all ${
              showLabels
                ? 'bg-green-400/10 border-green-400 text-green-400'
                : 'bg-navy-light border-navy-light text-gray-400 hover:border-gray-400'
            }`}
          >
            <div className="flex items-center gap-2">
              {showLabels ? (
                <Eye className="h-4 w-4" />
              ) : (
                <EyeOff className="h-4 w-4" />
              )}
              <span className="text-sm font-medium">Labels</span>
            </div>
          </button>

          {/* Blur Toggle */}
          <button
            onClick={() => setBlurDetections(!blurDetections)}
            className={`flex items-center justify-between p-3 rounded-lg border transition-all ${
              blurDetections
                ? 'bg-blue-400/10 border-blue-400 text-blue-400'
                : 'bg-navy-light border-navy-light text-gray-400 hover:border-gray-400'
            }`}
          >
            <div className="flex items-center gap-2">
              {blurDetections ? (
                <Eye className="h-4 w-4" />
              ) : (
                <EyeOff className="h-4 w-4" />
              )}
              <span className="text-sm font-medium">Blur</span>
            </div>
          </button>
        </div>
      </div>

      {/* Detection List */}
      {visibleDetectionCount > 0 && (
        <div className="bg-navy-dark rounded-lg border border-navy-light p-6">
          <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            <Tag className="h-5 w-5 text-green-400" />
            Detected Objects ({visibleDetectionCount})
          </h4>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {allDetections
              .filter(d => d.score >= scoreThreshold)
              .sort((a, b) => b.score - a.score)
              .map((detection, index) => (
                <div
                  key={detection.box_id}
                  className="flex items-center justify-between p-3 bg-navy-light rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{
                        backgroundColor: `hsl(${(index * 137) % 360}, 70%, 50%)`,
                      }}
                    />
                    <span className="text-white font-medium capitalize">
                      {detection.label}
                    </span>
                  </div>
                  <div className="flex items-center gap-4">
                    <span className="text-xs text-gray-400 font-mono">
                      {Math.round(detection.width)}×{Math.round(detection.height)}
                    </span>
                    <span className="text-sm font-semibold text-green-400">
                      {(detection.score * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
              ))}
          </div>
        </div>
      )}
    </div>
  );
}
