'use client';

import { MessageSquare, Send, X, AlertTriangle } from 'lucide-react';
import { useState, useEffect } from 'react';
import FingerprintJS from '@fingerprintjs/fingerprintjs';

export function FeedbackWidget() {
  const [isOpen, setIsOpen] = useState(false);
  const [email, setEmail] = useState('');
  const [feedback, setFeedback] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [error, setError] = useState('');
  const [fingerprint, setFingerprint] = useState<string | null>(null);
  const [submissionCount, setSubmissionCount] = useState(0);
  const [remainingSubmissions, setRemainingSubmissions] = useState(2);
  const [isBlocked, setIsBlocked] = useState(false);
  const [isShaking, setIsShaking] = useState(false);
  const [isCheckingLimit, setIsCheckingLimit] = useState(true);

  // Initialize fingerprinting on component mount
  useEffect(() => {
    const initFingerprint = async () => {
      try {
        const fp = await FingerprintJS.load();
        const result = await fp.get();
        setFingerprint(result.visitorId);
        console.log('🔒 Browser fingerprint initialized:', result.visitorId.substring(0, 8) + '...');
      } catch (err) {
        console.error('Failed to initialize fingerprint:', err);
        setFingerprint('fallback-' + Math.random().toString(36).substring(7));
      }
    };

    initFingerprint();
  }, []);

  // Check rate limit when fingerprint is available
  useEffect(() => {
    if (!fingerprint) return;

    const checkRateLimit = async () => {
      try {
        const response = await fetch('/api/feedback/check-limit', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ fingerprint }),
        });

        if (response.ok) {
          const data = await response.json();
          setSubmissionCount(data.submissionCount);
          setRemainingSubmissions(data.remainingSubmissions);
          setIsBlocked(data.isBlocked);
          console.log(`📊 Rate limit status: ${data.submissionCount}/2 submissions, ${data.remainingSubmissions} remaining`);
        }
      } catch (err) {
        console.error('Failed to check rate limit:', err);
      } finally {
        setIsCheckingLimit(false);
      }
    };

    checkRateLimit();
  }, [fingerprint]);

  const triggerShakeAnimation = () => {
    setIsShaking(true);
    setTimeout(() => setIsShaking(false), 500);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!feedback.trim() || !email.trim()) return;

    // Check if blocked before attempting submission
    if (isBlocked) {
      setError('You have reached the maximum number of feedback submissions (2). Thank you for your feedback!');
      triggerShakeAnimation();
      return;
    }

    // Validate email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address');
      triggerShakeAnimation();
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      // Send feedback to API endpoint with fingerprint
      const response = await fetch('/api/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          feedback,
          timestamp: new Date().toISOString(),
          fingerprint,
        }),
      });

      const data = await response.json();

      // Handle rate limit error (429)
      if (response.status === 429) {
        setError(data.message || 'You have reached the maximum number of feedback submissions (2).');
        setIsBlocked(true);
        setRemainingSubmissions(0);
        setSubmissionCount(data.submissionCount || 2);
        triggerShakeAnimation();
        return;
      }

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send feedback');
      }

      setSubmitted(true);

      // Update submission count
      setSubmissionCount(prev => prev + 1);
      setRemainingSubmissions(prev => Math.max(0, prev - 1));

      // Reset after 2 seconds
      setTimeout(() => {
        setEmail('');
        setFeedback('');
        setSubmitted(false);
        setIsOpen(false);
      }, 2000);
    } catch (err: any) {
      setError(err.message || 'Failed to send feedback. Please try again or email us <NAME_EMAIL>');
      triggerShakeAnimation();
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      {/* Feedback Button */}
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-6 right-6 z-40 flex items-center gap-2 rounded-full bg-green-400 px-6 py-3 font-semibold text-navy transition-all duration-300 hover:bg-green-500 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-offset-2 focus:ring-offset-navy"
        aria-label="Open feedback form"
      >
        <MessageSquare className="size-5" />
        <span className="hidden sm:inline">Feedback</span>
      </button>

      {/* Feedback Modal */}
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <div
            className="absolute inset-0 bg-black/60 backdrop-blur-sm"
            onClick={() => !isSubmitting && setIsOpen(false)}
            aria-hidden="true"
          />

          {/* Modal Content */}
          <div className={`relative w-full max-w-lg rounded-2xl border border-navy-light bg-navy p-6 shadow-2xl ${isShaking ? 'animate-shake' : ''}`}>
            {/* Header */}
            <div className="mb-4 flex items-center justify-between">
              <h3 className="text-2xl font-bold text-white">Share Your Feedback</h3>
              <button
                onClick={() => setIsOpen(false)}
                className="rounded-lg p-2 text-gray-400 transition-colors hover:bg-navy-light hover:text-white focus:outline-none focus:ring-2 focus:ring-green-400"
                aria-label="Close feedback form"
                disabled={isSubmitting}
              >
                <X className="size-5" />
              </button>
            </div>

            {/* Rate Limit Info Banner */}
            {!isCheckingLimit && !isBlocked && remainingSubmissions < 2 && (
              <div className="mb-4 rounded-lg border border-yellow-400/30 bg-yellow-400/10 px-4 py-3">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="size-5 text-yellow-400 mt-0.5 flex-shrink-0" />
                  <div className="text-sm">
                    <p className="font-semibold text-yellow-400">Rate Limit Notice</p>
                    <p className="text-yellow-300">
                      You have {remainingSubmissions} feedback submission{remainingSubmissions !== 1 ? 's' : ''} remaining out of 2 total allowed.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Blocked Banner */}
            {isBlocked && (
              <div className="mb-4 rounded-lg border border-red-400/30 bg-red-400/10 px-4 py-3">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="size-5 text-red-400 mt-0.5 flex-shrink-0" />
                  <div className="text-sm">
                    <p className="font-semibold text-red-400">Submission Limit Reached</p>
                    <p className="text-red-300">
                      You have reached the maximum number of feedback submissions (2). Thank you for your feedback!
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Success Message */}
            {submitted ? (
              <div className="flex flex-col items-center justify-center py-8">
                <div className="mb-4 flex size-16 items-center justify-center rounded-full bg-green-400/20">
                  <svg
                    className="size-8 text-green-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
                <p className="text-lg font-semibold text-white">Thank you for your feedback!</p>
                <p className="mt-2 text-sm text-gray-300">We appreciate your input.</p>
                {remainingSubmissions > 0 && (
                  <p className="mt-3 text-xs text-gray-400">
                    You have {remainingSubmissions} feedback submission{remainingSubmissions !== 1 ? 's' : ''} remaining.
                  </p>
                )}
              </div>
            ) : (
              <form onSubmit={handleSubmit}>
                {/* Description */}
                <p className="mb-4 text-gray-300">
                  We'd love to hear your thoughts on Guardiavision. Your feedback helps us improve!
                </p>

                {/* Error Message */}
                {error && (
                  <div className="mb-4 rounded-lg border border-red-400 bg-red-400/10 px-4 py-3 text-sm text-red-400">
                    {error}
                  </div>
                )}

                {/* Email Input */}
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Your email address"
                  className="mb-4 w-full rounded-lg border border-navy-light bg-navy-dark px-4 py-3 text-white placeholder-gray-400 transition-colors focus:border-green-400 focus:outline-none focus:ring-2 focus:ring-green-400/50 disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={isSubmitting || isBlocked}
                  required
                  aria-label="Email address"
                />

                {/* Textarea */}
                <textarea
                  value={feedback}
                  onChange={(e) => setFeedback(e.target.value)}
                  placeholder={isBlocked ? "Submission limit reached" : "Tell us what you think... What features would you like to see? What can we improve?"}
                  className="mb-4 w-full resize-none rounded-lg border border-navy-light bg-navy-dark px-4 py-3 text-white placeholder-gray-400 transition-colors focus:border-green-400 focus:outline-none focus:ring-2 focus:ring-green-400/50 disabled:opacity-50 disabled:cursor-not-allowed"
                  rows={5}
                  disabled={isSubmitting || isBlocked}
                  required
                  maxLength={500}
                  aria-label="Feedback message"
                />

                {/* Footer */}
                <div className="flex items-center justify-between">
                  <p className="text-xs text-gray-400">
                    {feedback.length}/500 characters
                  </p>
                  <button
                    type="submit"
                    disabled={isSubmitting || isBlocked || !feedback.trim() || !email.trim()}
                    className="inline-flex items-center gap-2 rounded-lg bg-green-400 px-6 py-3 font-semibold text-navy transition-all duration-300 hover:bg-green-500 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-offset-2 focus:ring-offset-navy disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    {isSubmitting ? (
                      <>
                        <svg className="size-5 animate-spin" viewBox="0 0 24 24">
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                            fill="none"
                          />
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          />
                        </svg>
                        Sending...
                      </>
                    ) : (
                      <>
                        <Send className="size-5" />
                        Send Feedback
                      </>
                    )}
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      )}
    </>
  );
}
