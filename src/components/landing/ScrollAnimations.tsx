'use client';

import { useEffect } from 'react';

export function ScrollAnimations() {
  useEffect(() => {
    // Add smooth scrolling to the entire page
    document.documentElement.style.scrollBehavior = 'smooth';

    const animateOnScroll = () => {
      // Handle individual scroll-animate elements
      const elements = document.querySelectorAll('.scroll-animate');
      elements.forEach((element, index) => {
        const elementPosition = element.getBoundingClientRect().top;
        const windowHeight = window.innerHeight;

        // Trigger animation when element is 80% of the way up the screen
        if (elementPosition < windowHeight * 0.8) {
          const hasCustomDelay = element.classList.toString().includes('delay-');
          if (!hasCustomDelay) {
            setTimeout(() => {
              element.classList.add('animate-in');
            }, index * 50);
          } else {
            element.classList.add('animate-in');
          }
        }
      });

      // Handle grouped scroll-animate elements (appear together)
      const groups = document.querySelectorAll('.scroll-animate-group');
      groups.forEach((group) => {
        const groupPosition = group.getBoundingClientRect().top;
        const windowHeight = window.innerHeight;

        // Trigger animation when group is 75% of the way up the screen
        if (groupPosition < windowHeight * 0.75) {
          const groupElements = group.querySelectorAll('.scroll-animate, [class*="scroll-animate"]');
          groupElements.forEach((element, index) => {
            // All elements in a group appear together with minimal stagger
            setTimeout(() => {
              element.classList.add('animate-in');
            }, index * 100); // Slightly longer stagger for grouped elements
          });
        }
      });
    };

    // Run once on initial load with a slight delay to ensure proper rendering
    setTimeout(animateOnScroll, 100);

    // Add scroll event listener with throttling for better performance
    let scrollTimeout: NodeJS.Timeout | null = null;
    const handleScroll = () => {
      if (!scrollTimeout) {
        scrollTimeout = setTimeout(() => {
          animateOnScroll();
          scrollTimeout = null;
        }, 5); // Reduced throttle for smoother animations
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    // Add resize listener to recalculate positions
    window.addEventListener('resize', animateOnScroll, { passive: true });

    // Clean up
    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', animateOnScroll);
      if (scrollTimeout) clearTimeout(scrollTimeout);
    };
  }, []);

  return null; // This component doesn't render anything
}
