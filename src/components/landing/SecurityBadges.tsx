import { <PERSON>, Shield, ShieldCheck, Server } from 'lucide-react';

export function SecurityBadges() {
  const badges = [
    {
      icon: <Lock className="size-5 text-green-400" />,
      title: 'End-to-End Encryption',
      description: 'AES-256 encryption for all uploads',
    },
    {
      icon: <Shield className="size-5 text-blue-400" />,
      title: 'GDPR Compliant',
      description: 'Full compliance with EU data protection',
    },
    {
      icon: <ShieldCheck className="size-5 text-green-400" />,
      title: 'Zero Data Retention',
      description: 'Files deleted after processing',
    },
    {
      icon: <Server className="size-5 text-blue-400" />,
      title: 'SOC 2 Type II',
      description: 'Certified secure infrastructure',
    },
  ];

  return (
    <section className="section-padding bg-navy-dark" id="security">
      <div className="container mx-auto container-padding">
        <div className="mx-auto max-w-3xl text-center scroll-animate-group">
          <div className="mb-4 inline-flex items-center gap-2 rounded-full border border-green-400/30 bg-green-400/10 px-4 py-2 text-sm font-semibold text-green-400">
            <ShieldCheck className="size-4" />
            <span>Enterprise-Grade Security</span>
          </div>
          <h2 className="text-heading-xl mb-4 text-white">
            Your Privacy is Our Priority
          </h2>
          <p className="text-body-lg text-secondary">
            We implement industry-leading security measures to protect your sensitive content. Your data is encrypted, processed securely, and never used to train our AI models.
          </p>
        </div>

        <div className="mt-12 grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {badges.map((badge, index) => (
            <div
              key={index}
              className="card-interactive card-padding group"
            >
              <div className="mb-4 inline-flex size-12 items-center justify-center rounded-lg bg-navy transition-colors group-hover:bg-navy-light">
                {badge.icon}
              </div>
              <h3 className="text-heading-sm mb-2 text-white">{badge.title}</h3>
              <p className="text-body-sm text-muted">{badge.description}</p>
            </div>
          ))}
        </div>

        {/* Additional Security Details */}
        <div className="mt-12 rounded-2xl border border-navy-light bg-navy p-6 md:p-8">
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            <div className="space-y-2">
              <h4 className="font-semibold text-white">Data Processing</h4>
              <p className="text-sm text-gray-300">
                All processing happens on secure, isolated servers. Your files are automatically deleted within 24 hours.
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold text-white">Privacy Policy</h4>
              <p className="text-sm text-gray-300">
                We never share, sell, or use your data for training. Read our{' '}
                <a href="/privacy-policy" className="text-green-400 hover:underline">
                  privacy policy
                </a>
                .
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold text-white">Regular Audits</h4>
              <p className="text-sm text-gray-300">
                Our systems undergo regular security audits and penetration testing by third-party experts.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
