'use client';

import { useState, useRef } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';

interface ImageSet {
  id: string;
  name: string;
  originalImage: string;
  processedImage: string;
  prompt: string;
  description: string;
}

const imageSets: ImageSet[] = [
  {
    id: 'people',
    name: 'People',
    originalImage: '/images/people.jpg',
    processedImage: '/images/people_processed.jpg',
    prompt: 'blur all peoples faces',
    description: 'Faces automatically blurred',
  },
  {
    id: 'car',
    name: 'Vehicle',
    originalImage: '/images/car.jpg',
    processedImage: '/images/car_processed.jpg',
    prompt: 'pixelate license plate',
    description: 'License plate pixelated',
  },
  {
    id: 'computer',
    name: 'Computer',
    originalImage: '/images/computer.jpg',
    processedImage: '/images/computer_processed.jpg',
    prompt: 'blur sensitive data on the screen',
    description: 'Sensitive screen data blurred',
  },
];

export function BeforeAfterShowcase() {
  const [activeImageSet, setActiveImageSet] = useState('people');
  const [sliderPosition, setSliderPosition] = useState(50);
  const [dragging, setDragging] = useState(false);
  const imageContainerRef = useRef<HTMLDivElement>(null);

  const currentImageSet =
    imageSets.find((set) => set.id === activeImageSet) || imageSets[0];

  const handleSliderChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSliderPosition(Number(e.target.value));
  };

  const updateSliderFromPosition = (clientX: number) => {
    const rect = imageContainerRef.current?.getBoundingClientRect();
    if (!rect) return;
    const newPos = ((clientX - rect.left) / rect.width) * 100;
    setSliderPosition(Math.min(100, Math.max(0, newPos)));
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    setDragging(true);
    updateSliderFromPosition(e.clientX);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (dragging) {
      updateSliderFromPosition(e.clientX);
    }
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    updateSliderFromPosition(e.touches[0].clientX);
  };

  const handleMouseUp = () => setDragging(false);

  return (
    <div className="scroll-animate-group select-none">
      {/* Image set buttons */}
      <div className="mb-8 flex justify-center">
        <div className="inline-flex rounded-lg bg-navy-light/30 p-1">
          {imageSets.map((imageSet) => (
            <button
              key={imageSet.id}
              onClick={() => {
                setActiveImageSet(imageSet.id);
                setSliderPosition(50);
              }}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-300 ${
                activeImageSet === imageSet.id
                  ? 'bg-green-400 text-navy shadow-lg'
                  : 'text-gray-300 hover:text-white hover:bg-navy-light/50'
              }`}
            >
              {imageSet.name}
            </button>
          ))}
        </div>
      </div>

      {/* Before/After Comparison */}
      <div className="mx-auto max-w-4xl">
        <div
          className="relative overflow-hidden rounded-xl border border-navy-light bg-navy-dark shadow-2xl"
          ref={imageContainerRef}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseLeave={handleMouseUp}
          onMouseUp={handleMouseUp}
          onTouchStart={(e) => {
            setDragging(true);
            updateSliderFromPosition(e.touches[0].clientX);
          }}
          onTouchMove={handleTouchMove}
          onTouchEnd={() => setDragging(false)}
        >
          <div className="relative aspect-[16/10] w-full">
            {/* AI-Processed Image (now on the LEFT as full background) */}
            <AnimatePresence mode="wait">
              <motion.div
                key={currentImageSet.processedImage}
                className="absolute inset-0 z-0"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.4 }}
              >
                <Image
                  src={currentImageSet.processedImage}
                  alt="AI Processed"
                  fill
                  className="object-cover"
                  priority
                  draggable={false}
                />
              </motion.div>
            </AnimatePresence>

            {/* Original Image (on the RIGHT, revealed by slider) */}
            <motion.div
              className="absolute inset-0 z-10 overflow-hidden"
              animate={{
                clipPath: `inset(0 0 0 ${sliderPosition}%)`,
              }}
              transition={{ type: 'spring', stiffness: 100, damping: 20 }}
            >
              <Image
                src={currentImageSet.originalImage}
                alt="Original"
                fill
                className="object-cover"
                draggable={false}
              />
            </motion.div>

            {/* Slider Bar & Handle */}
            <motion.div
              className="absolute top-0 bottom-0 z-20"
              style={{ left: `${sliderPosition}%` }}
            >
              {/* Line */}
              <div className="w-1 h-full bg-gradient-to-b from-green-400 to-blue-400 shadow-lg"></div>

              {/* Handle */}
              <motion.div
                className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-gradient-to-br from-green-400 to-blue-500 border-4 border-white shadow-2xl hover:scale-105 transition-transform duration-200"
              >
                <div className="w-full h-full flex items-center justify-center">
                  <div className="w-1 h-5 bg-white rounded-full mx-0.5"></div>
                  <div className="w-1 h-5 bg-white rounded-full mx-0.5"></div>
                </div>
              </motion.div>
            </motion.div>

            {/* Labels */}
            <div className="absolute top-4 left-4 z-30 bg-green-500 text-navy px-3 py-1 rounded-md text-xs md:text-sm font-semibold shadow-md">
              AI-Processed
            </div>
            <div className="absolute top-4 right-4 z-30 bg-black/70 text-white px-3 py-1 rounded-md text-xs md:text-sm font-semibold shadow-md">
              Original
            </div>
          </div>

          {/* Slider Control (fallback input range) */}
          <div className="p-6 bg-navy-dark border-t border-navy-light">
            <input
              type="range"
              min="0"
              max="100"
              value={sliderPosition}
              onChange={handleSliderChange}
              className="w-full h-2 bg-navy-light rounded-lg appearance-none cursor-pointer slider-thumb"
            />
            <div className="mt-4 text-center">
              <p className="text-gray-300 mb-2">
                <span className="font-semibold text-green-400">AI Prompt:</span>{' '}
                "{currentImageSet.prompt}"
              </p>
              <p className="text-sm text-gray-400">
                {currentImageSet.description}
              </p>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .slider-thumb::-webkit-slider-thumb {
          appearance: none;
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #10b981;
          cursor: pointer;
          border: 2px solid #ffffff;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        }

        .slider-thumb::-moz-range-thumb {
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #10b981;
          cursor: pointer;
          border: 2px solid #ffffff;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        }
      `}</style>
    </div>
  );
}
