'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

export function DynamicStats() {
  const [filesProcessed, setFilesProcessed] = useState(51980);
  const [activeCountries, setActiveCountries] = useState(47);

  useEffect(() => {
    const launchDate = new Date('2025-10-26');
    const now = new Date();
    const daysSinceLaunch = Math.floor((now.getTime() - launchDate.getTime()) / (1000 * 60 * 60 * 24));

    const baseFiles = 5198 + (daysSinceLaunch * 100);
    const baseCountries = 10 + (daysSinceLaunch * 1);

    setFilesProcessed(baseFiles);
    setActiveCountries(Math.min(baseCountries, 195));

    const interval = setInterval(() => {
      setFilesProcessed(prev => prev + Math.floor(Math.random() * 3) + 1); // increment by 1-3
      if (Math.random() < 0.05 && activeCountries < 195) {
        setActiveCountries(prev => prev + 1);
      }
    }, 2000); // update every 2 seconds

    return () => clearInterval(interval);
  }, [activeCountries]);

  const RollingDigit = ({ digit, position }: { digit: string; position: number }) => {
    const prevDigitRef = useRef(digit);
    const [shouldAnimate, setShouldAnimate] = useState(false);

    useEffect(() => {
      if (prevDigitRef.current !== digit) {
        setShouldAnimate(true);
        prevDigitRef.current = digit;
      }
    }, [digit]);

    return (
      <div className="relative w-[0.6em] h-[1.2em] overflow-hidden inline-block font-mono text-center">
        {shouldAnimate ? (
          <AnimatePresence mode="wait" onExitComplete={() => setShouldAnimate(false)}>
            <motion.span
              key={`${position}-${digit}`}
              initial={{ y: '100%' }}
              animate={{ y: '0%' }}
              exit={{ y: '-100%' }}
              transition={{ 
                duration: 0.4,
                ease: [0.25, 0.1, 0.25, 1],
                delay: position * 0.05 // stagger effect
              }}
              className="absolute inset-0 flex items-center justify-center"
            >
              {digit}
            </motion.span>
          </AnimatePresence>
        ) : (
          <span className="absolute inset-0 flex items-center justify-center">
            {digit}
          </span>
        )}
      </div>
    );
  };

  const renderRollingNumber = (num: number, color: string = 'text-green-400') => {
    const numStr = num.toString();
    const digits = numStr.split('');
    
    return (
      <div className="flex items-baseline justify-center">
        <div className={`flex items-baseline font-mono text-5xl font-bold ${color}`}>
          {digits.map((digit, index) => (
            <RollingDigit 
              key={index} 
              digit={digit} 
              position={index}
            />
          ))}
          <span className={`${color} font-mono text-5xl font-bold leading-none`}>+</span>
        </div>
      </div>
    );
  };

  return (
    <div className="py-16 bg-navy-dark/50 rounded-2xl">
      <div className="max-w-5xl mx-auto px-6">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-white mb-12">
            Trusted by users around the world
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <motion.div
              className="bg-navy-light/30 rounded-xl p-8 flex flex-col items-center"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              {renderRollingNumber(filesProcessed, 'text-green-400')}
              <div className="text-gray-300 mt-2">Files processed</div>
              <div className="text-xs text-gray-500 mt-1">Growing daily</div>
            </motion.div>

            <motion.div
              className="bg-navy-light/30 rounded-xl p-8 flex flex-col items-center"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              {renderRollingNumber(activeCountries, 'text-blue-400')}
              <div className="text-gray-300 mt-2">Daily active countries</div>
              <div className="text-xs text-gray-500 mt-1">Expanding globally</div>
            </motion.div>
          </div>
        </div>

        <div className="text-center">
          <div className="inline-flex items-center text-sm text-gray-400">
            <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
            Live statistics • Updated in real-time
          </div>
        </div>
      </div>
    </div>
  );
}