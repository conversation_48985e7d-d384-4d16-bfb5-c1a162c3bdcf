'use client';

import { Menu, X } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';

interface InteractiveNavbarProps {
  locale: string;
}

export function InteractiveNavbar({ locale }: InteractiveNavbarProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <nav className="sticky top-0 z-50 border-b border-navy-light/20 bg-navy/80 backdrop-blur-lg" aria-label="Main navigation">
      <div className="mx-auto max-w-7xl px-6 sm:px-8 lg:px-12">
        <div className="flex h-32 items-center justify-between">
          <div className="flex items-center">
            <Link href="/" className="group flex items-center transition-transform duration-300 hover:scale-105">
              <div className="relative overflow-hidden rounded-full">
                <div className="absolute -inset-1 rounded-full bg-gradient-to-r from-blue-400 to-green-400 opacity-70 blur-lg transition-opacity duration-300 group-hover:opacity-100"></div>
                <div className="relative">
                  <Image src="/photo-de-profil-linkedin.png" alt="Guardiavision Logo" width={80} height={80} className="object-cover" style={{ width: '80px', height: '80px' }} />
                </div>
              </div>
              <span className="ml-4 text-2xl font-bold text-white transition-colors duration-300">Guardia<span className="vision-text">Vision</span></span>
            </Link>
          </div>

          <div className="hidden md:block">
            <div className="ml-10 flex items-center gap-8">
              <Link
                href="#features"
                className="text-lg font-medium text-gray-200 transition-colors duration-300 hover:text-green-400 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-offset-2 focus:ring-offset-navy rounded-sm px-2 py-1"
              >
                Features
              </Link>
              <Link
                href="#demo"
                className="text-lg font-medium text-gray-200 transition-colors duration-300 hover:text-green-400 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-offset-2 focus:ring-offset-navy rounded-sm px-2 py-1"
              >
                Demo
              </Link>
              <Link
                href="#pricing"
                className="text-lg font-medium text-gray-200 transition-colors duration-300 hover:text-green-400 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-offset-2 focus:ring-offset-navy rounded-sm px-2 py-1"
              >
                Pricing
              </Link>
              <Link
                href={`/${locale}/sign-in`}
                className="blue-button-glow inline-block rounded-full border-2 border-blue-400 px-8 py-3.5 text-lg font-semibold text-blue-400 transition-all duration-300 hover:border-transparent hover:bg-blue-500 hover:text-white focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-navy"
              >
                Log In
              </Link>
              <div className="group relative">
                <div className="rainbow-border rounded-full"></div>
                <Link
                  href={`/${locale}/sign-up`}
                  className="relative inline-flex items-center justify-center rounded-full bg-green-400 px-8 py-3.5 text-lg font-semibold text-navy transition-all duration-300 hover:bg-green-500 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-offset-2 focus:ring-offset-navy"
                >
                  Start Free Trial
                </Link>
              </div>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              type="button"
              className="relative inline-flex items-center justify-center rounded-full p-2.5 text-gray-200 transition-all duration-300 hover:bg-navy-light hover:text-green-400 focus:outline-none focus:ring-2 focus:ring-green-400"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              <span className="absolute -inset-0.5"></span>
              <span className="sr-only">Open main menu</span>
              {isMenuOpen
                ? (
                    <X className="block size-7" aria-hidden="true" />
                  )
                : (
                    <Menu className="block size-7" aria-hidden="true" />
                  )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMenuOpen && (
        <div className="md:hidden" role="menu">
          <div className="space-y-2 bg-navy-dark/90 px-6 pb-8 pt-4 backdrop-blur-lg">
            <Link
              href="#features"
              className="block rounded-lg px-4 py-3.5 text-lg font-medium text-gray-200 transition-all duration-300 hover:bg-navy-light hover:text-green-400 focus:outline-none focus:ring-2 focus:ring-green-400"
              onClick={() => setIsMenuOpen(false)}
              role="menuitem"
            >
              Features
            </Link>
            <Link
              href="#demo"
              className="block rounded-lg px-4 py-3.5 text-lg font-medium text-gray-200 transition-all duration-300 hover:bg-navy-light hover:text-green-400 focus:outline-none focus:ring-2 focus:ring-green-400"
              onClick={() => setIsMenuOpen(false)}
              role="menuitem"
            >
              Demo
            </Link>
            <Link
              href="#pricing"
              className="block rounded-lg px-4 py-3.5 text-lg font-medium text-gray-200 transition-all duration-300 hover:bg-navy-light hover:text-green-400 focus:outline-none focus:ring-2 focus:ring-green-400"
              onClick={() => setIsMenuOpen(false)}
              role="menuitem"
            >
              Pricing
            </Link>
            <div className="mt-10 space-y-4 px-3">
              <Link
                href={`/${locale}/sign-in`}
                className="btn-base btn-secondary btn-md block w-full text-center rounded-lg"
                onClick={() => setIsMenuOpen(false)}
                role="menuitem"
              >
                Log In
              </Link>
              <Link
                href={`/${locale}/sign-up`}
                className="btn-base btn-primary btn-md glow-green block w-full text-center rounded-lg"
                onClick={() => setIsMenuOpen(false)}
                role="menuitem"
              >
                Start Free Trial
              </Link>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
}
