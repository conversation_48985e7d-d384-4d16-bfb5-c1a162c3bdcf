'use client';

import { useState } from 'react';
import { useUser } from '@clerk/nextjs';

interface Plan {
  name: string;
  credits: number;
  subscriptionType: string;
}

const plans: Plan[] = [
  { name: 'Free', credits: 5, subscriptionType: 'Free' },
  { name: 'Standard', credits: 700, subscriptionType: 'Standard' },
  { name: 'Pro', credits: 999999, subscriptionType: 'Pro' },
  { name: 'Premium', credits: 999999, subscriptionType: 'Premium' },
];

export function CreditUpdater() {
  const [loading, setLoading] = useState(false);
  const [fixLoading, setFixLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [currentUser, setCurrentUser] = useState<any>(null);
  const { user } = useUser();

  const fetchCurrentUser = async () => {
    try {
      const response = await fetch('/api/admin/update-credits');
      const data = await response.json();

      if (data.success) {
        setCurrentUser(data.user);
        setMessage(`Current: ${data.user.subscriptionType} plan with ${data.user.credits} credits`);
      } else {
        setMessage(`Error: ${data.error}`);
      }
    } catch (error) {
      setMessage(`Error fetching user data: ${error}`);
    }
  };

  const quickFixToStandard = async () => {
    setFixLoading(true);
    setMessage('');

    try {
      const response = await fetch('/api/admin/fix-my-account', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (data.success) {
        setMessage(`✅ ${data.message}`);
        // Refresh current user data
        await fetchCurrentUser();
      } else {
        setMessage(`❌ Error: ${data.error}`);
      }
    } catch (error) {
      setMessage(`❌ Error: ${error instanceof Error ? error.message : 'Something went wrong'}`);
    } finally {
      setFixLoading(false);
    }
  };

  const updateCredits = async (plan: Plan) => {
    setLoading(true);
    setMessage('');

    try {
      const response = await fetch('/api/admin/update-credits', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          planType: plan.name.toLowerCase(),
          credits: plan.credits,
          subscriptionType: plan.subscriptionType,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setMessage(`✅ ${data.message}`);
        // Refresh current user data
        await fetchCurrentUser();
      } else {
        setMessage(`❌ Error: ${data.error}`);
      }
    } catch (error) {
      setMessage(`❌ Error: ${error instanceof Error ? error.message : 'Something went wrong'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-navy-light rounded-lg p-6 border border-navy-light">
      <h3 className="text-xl font-bold text-white mb-4">Manual Credit Update</h3>
      <p className="text-gray-300 mb-4">
        Use this to manually update your subscription after payment (for testing purposes).
      </p>

      {/* Quick Fix for Standard Plan */}
      <div className="mb-6 p-4 bg-green-500/10 border border-green-500 rounded-lg">
        <h4 className="text-green-400 font-semibold mb-2">🚀 Quick Fix - Already Paid for Standard?</h4>
        <p className="text-green-300 text-sm mb-3">
          If you already paid for the Standard plan but didn't get your 700 credits, click here to fix it instantly:
        </p>
        <button
          onClick={quickFixToStandard}
          disabled={fixLoading}
          className={`bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors ${
            fixLoading ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          {fixLoading ? 'Fixing Account...' : '✨ Fix My Standard Plan Now'}
        </button>
      </div>

      {/* Current Status */}
      <div className="mb-6">
        <button
          onClick={fetchCurrentUser}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors mb-3"
        >
          Check Current Status
        </button>
        {currentUser && (
          <div className="bg-navy rounded-lg p-4">
            <h4 className="text-white font-semibold mb-2">Current Status:</h4>
            <p className="text-gray-300">Plan: <span className="text-green-400">{currentUser.subscriptionType}</span></p>
            <p className="text-gray-300">Credits: <span className="text-green-400">{currentUser.credits.toLocaleString()}</span></p>
            <p className="text-gray-300">Status: <span className="text-green-400">{currentUser.subscriptionStatus}</span></p>
            <p className="text-gray-300 text-sm">Last Updated: {new Date(currentUser.lastUpdated).toLocaleString()}</p>
          </div>
        )}
      </div>

      {/* Plan Update Buttons */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        {plans.map((plan) => (
          <button
            key={plan.name}
            onClick={() => updateCredits(plan)}
            disabled={loading}
            className={`p-4 rounded-lg border transition-all duration-300 ${
              loading ? 'opacity-50 cursor-not-allowed' : 'hover:border-green-400'
            } ${
              plan.name === 'Free' ? 'border-blue-400 text-blue-400' :
              plan.name === 'Standard' ? 'border-teal-400 text-teal-400' :
              plan.name === 'Pro' ? 'border-green-400 text-green-400' :
              'border-purple-400 text-purple-400'
            }`}
          >
            <div className="text-center">
              <h4 className="font-semibold mb-1">{plan.name}</h4>
              <p className="text-sm text-gray-400">
                {plan.credits === 999999 ? 'Unlimited' : plan.credits.toLocaleString()} credits
              </p>
            </div>
          </button>
        ))}
      </div>

      {/* Message Display */}
      {message && (
        <div className={`p-4 rounded-lg ${
          message.includes('✅') ? 'bg-green-500/10 border border-green-500 text-green-400' :
          message.includes('❌') ? 'bg-red-500/10 border border-red-500 text-red-400' :
          'bg-blue-500/10 border border-blue-500 text-blue-400'
        }`}>
          <p className="text-sm">{message}</p>
        </div>
      )}

      {/* Instructions */}
      <div className="mt-6 p-4 bg-navy rounded-lg">
        <h4 className="text-white font-semibold mb-2">Instructions:</h4>
        <ol className="text-gray-300 text-sm space-y-1">
          <li>1. Complete payment through Clerk's PricingTable</li>
          <li>2. Click the corresponding plan button above to update your credits</li>
          <li>3. Refresh your dashboard to see the updated credits</li>
          <li>4. This is temporary until Stripe webhook integration is configured</li>
        </ol>

        <div className="mt-4 p-3 bg-blue-500/10 border border-blue-500 rounded-lg">
          <h5 className="text-blue-400 font-semibold mb-1">For Automatic Credit Updates:</h5>
          <p className="text-blue-300 text-xs">
            Configure Stripe webhook URL: <code className="bg-navy px-1 rounded">https://yourdomain.com/api/webhooks/stripe</code>
            <br />
            Events: checkout.session.completed, invoice.payment_succeeded, customer.subscription.*
          </p>
        </div>
      </div>
    </div>
  );
}
