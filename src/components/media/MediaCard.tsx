'use client';

import { useState } from 'react';
import { Eye, Trash2, Play, Download, Calendar, HardDrive } from 'lucide-react';

interface MediaFile {
  id: string;
  original_filename: string;
  file_type: 'image' | 'video';
  file_size: number;
  created_at: string;
  url?: string; // Direct URL from API
  metadata?: {
    originalUrl?: string;
    duration?: number;
  };
}

interface MediaCardProps {
  file: MediaFile;
  onPreview: (file: MediaFile) => void;
  onDelete: (fileId: string, fileName: string) => void;
  onOpenStudio: (fileId: string) => void;
  isDeleting?: boolean;
}

export function MediaCard({ file, onPreview, onDelete, onOpenStudio, isDeleting = false }: MediaCardProps) {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [videoLoaded, setVideoLoaded] = useState(false);

  const handleOpenStudio = async (fileId: string) => {
    try {
      await fetch('/api/user/files/update-access', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fileId,
          lastAccessedDate: new Date().toISOString()
        })
      });
    } catch (error) {
      console.error('Failed to update last accessed date:', error);
    }
    onOpenStudio(fileId);
  };

  const formatFileSize = (bytes: number) => {
    const mb = bytes / (1024 * 1024);
    return mb < 1 ? `${(bytes / 1024).toFixed(0)}KB` : `${mb.toFixed(1)}MB`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const truncateFilename = (filename: string, maxLength: number = 25) => {
    if (filename.length <= maxLength) return filename;
    const extension = filename.split('.').pop();
    const nameWithoutExt = filename.substring(0, filename.lastIndexOf('.'));
    const truncatedName = nameWithoutExt.substring(0, maxLength - extension!.length - 4);
    return `${truncatedName}...${extension}`;
  };

  const isVideo = file.file_type === 'video';
  const fileUrl = file.url || file.metadata?.originalUrl;

  return (
    <div
      className={`group relative bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 hover:scale-[1.02] cursor-pointer ${isDeleting ? 'opacity-50' : ''}`}
      onDoubleClick={() => handleOpenStudio(file.id)}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Media Preview */}
      <div className="relative aspect-[4/3] bg-gray-900 overflow-hidden">
        {fileUrl ? (
          <>
            {isVideo ? (
              <div className="relative w-full h-full">
                <video
                  src={fileUrl}
                  className="w-full h-full object-cover transition-all duration-300"
                  muted
                  playsInline
                  loop
                  preload="auto"
                  onLoadedMetadata={(e) => {
                    const video = e.target as HTMLVideoElement;
                    // Seek to 10% or 1 second, whichever is smaller
                    video.currentTime = Math.min(1, video.duration * 0.1);
                  }}
                  onSeeked={() => {
                    // Frame is now rendered, show it
                    setVideoLoaded(true);
                  }}
                  onMouseEnter={(e) => {
                    if (videoLoaded && isHovered) {
                      const video = e.target as HTMLVideoElement;
                      video.currentTime = 0;
                      video.play().catch(() => {});
                    }
                  }}
                  onMouseLeave={(e) => {
                    const video = e.target as HTMLVideoElement;
                    video.pause();
                    video.currentTime = Math.min(1, video.duration * 0.1);
                  }}
                />

                <div className={`absolute inset-0 flex items-center justify-center bg-gradient-to-br from-black/40 to-purple-900/40 backdrop-blur-sm transition-opacity duration-300 ${isHovered && videoLoaded ? 'opacity-0' : 'opacity-100'}`}>
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg hover:scale-110 transition-transform duration-200">
                    <Play className="w-8 h-8 text-white ml-1" />
                  </div>
                </div>

                {file.metadata?.duration && (
                  <div className="absolute bottom-2 right-2 bg-black/80 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-md border border-white/10">
                    {Math.round(file.metadata.duration)}s
                  </div>
                )}

                {isHovered && videoLoaded && (
                  <div className="absolute top-2 left-2 bg-green-600/90 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-full animate-pulse">
                    Preview Playing
                  </div>
                )}
              </div>
            ) : (
              <div className="relative w-full h-full">
                {!imageLoaded && !imageError && (
                  <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-gray-800 to-gray-900">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-12 w-12 border-4 border-purple-600/30 border-t-purple-600 mx-auto mb-3"></div>
                      <p className="text-gray-400 text-sm">Loading image...</p>
                    </div>
                  </div>
                )}

                {imageError ? (
                  <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-800 to-gray-700">
                    <div className="text-center">
                      <div className="w-16 h-16 bg-red-600/20 rounded-full flex items-center justify-center mb-3 mx-auto">
                        <svg className="w-8 h-8 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"/>
                        </svg>
                      </div>
                      <p className="text-red-400 text-sm font-medium">Preview Failed</p>
                      <p className="text-gray-500 text-xs mt-1">Image could not be loaded</p>
                    </div>
                  </div>
                ) : (
                  <img
                    src={fileUrl}
                    alt={file.original_filename}
                    className={`w-full h-full object-cover transition-all duration-500 ${imageLoaded ? 'opacity-100 scale-100' : 'opacity-0 scale-105'}`}
                    onLoad={() => setImageLoaded(true)}
                    onError={() => setImageError(true)}
                  />
                )}
              </div>
            )}

            {/* Action Icons - Top Right */}
            <div className="absolute top-2 right-2 z-10 flex space-x-1 opacity-0 group-hover:opacity-100 transition-all duration-200 transform group-hover:scale-100 scale-95">
              <button
                onClick={(e) => { e.stopPropagation(); onPreview(file); }}
                className="p-2 bg-blue-600/90 hover:bg-blue-700 text-white rounded-lg transition-all duration-200 hover:scale-110 shadow-lg backdrop-blur-sm"
                title="Preview"
              >
                <Eye className="w-4 h-4" />
              </button>

              <button
                onClick={(e) => { e.stopPropagation(); handleOpenStudio(file.id); }}
                className="p-2 bg-green-600/90 hover:bg-green-700 text-white rounded-lg transition-all duration-200 hover:scale-110 shadow-lg backdrop-blur-sm"
                title="Open in Studio"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
                </svg>
              </button>

              <button
                onClick={(e) => { e.stopPropagation(); onDelete(file.id, file.original_filename); }}
                disabled={isDeleting}
                className="p-2 bg-red-600/90 hover:bg-red-700 text-white rounded-lg transition-all duration-200 hover:scale-110 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg backdrop-blur-sm"
                title="Delete"
              >
                {isDeleting ? (
                  <div className="w-4 h-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                ) : (
                  <Trash2 className="w-4 h-4" />
                )}
              </button>
            </div>

            {/* File Type Badge - Moved to Bottom Right */}
            <div className="absolute bottom-3 right-3 transition-transform duration-200 group-hover:scale-105">
              <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium backdrop-blur-sm border ${
                isVideo
                  ? 'bg-purple-600/90 text-purple-100 border-purple-400/30'
                  : 'bg-blue-600/90 text-blue-100 border-blue-400/30'
              }`}>
                {isVideo ? (
                  <>
                    <Play className="w-3 h-3 mr-1" />
                    Video
                  </>
                ) : (
                  <>
                    <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"/>

                      </svg>
                    Image
                  </>
                )}
              </span>
            </div>
          </>
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-800 to-gray-900">
            <div className="text-center p-6">
              <div className="w-20 h-20 bg-gradient-to-br from-gray-700 to-gray-600 rounded-2xl flex items-center justify-center mb-4 mx-auto">
                <svg className="w-10 h-10 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"/>
                </svg>
              </div>
              <p className="text-gray-400 text-sm font-medium">No Preview Available</p>
              <p className="text-gray-500 text-xs mt-1">File processing or URL missing</p>
            </div>
          </div>
        )}
      </div>

      {/* File Information */}
      <div className="p-4 bg-gradient-to-br from-gray-800 to-gray-900 border-t border-white/5">
        <div className="mb-2">
          <h3
            className="text-white font-medium text-sm leading-tight group-hover:text-purple-300 transition-colors duration-200"
            title={file.original_filename}
          >
            {truncateFilename(file.original_filename)}
          </h3>
        </div>

        <div className="flex items-center justify-between text-xs text-gray-400">
          <div className="flex items-center space-x-3">
            <div className="flex items-center">
              <HardDrive className="w-3 h-3 mr-1" />
              {formatFileSize(file.file_size)}
            </div>
            <div className="flex items-center">
              <Calendar className="w-3 h-3 mr-1" />
              {formatDate(file.created_at)}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
