'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { X, ExternalLink, Download } from 'lucide-react';

interface MediaFile {
  id: string;
  original_filename: string;
  file_type: 'image' | 'video';
  file_size: number;
  created_at: string;
  metadata?: {
    originalUrl?: string;
    duration?: number;
  };
}

interface PreviewModalProps {
  file: MediaFile | null;
  isOpen: boolean;
  onClose: () => void;
  onOpenStudio: (fileId: string) => void;
}

export function PreviewModal({ file, isOpen, onClose, onOpenStudio }: PreviewModalProps) {
  const router = useRouter();
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen || !file) return null;

  const formatFileSize = (bytes: number) => {
    const mb = bytes / (1024 * 1024);
    return mb < 1 ? `${(bytes / 1024).toFixed(0)}KB` : `${mb.toFixed(1)}MB`;
  };

  const handleDownload = () => {
    if (file.metadata?.originalUrl) {
      const link = document.createElement('a');
      link.href = file.metadata.originalUrl;
      link.download = file.original_filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleOpenStudio = () => {
    onOpenStudio(file.id);
    onClose();
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/80 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative z-10 max-w-4xl max-h-[90vh] w-full bg-gray-900 rounded-xl shadow-2xl overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700 bg-gray-800">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${
              file.file_type === 'video' 
                ? 'bg-purple-500/20 text-purple-400' 
                : 'bg-blue-500/20 text-blue-400'
            }`}>
              {file.file_type === 'video' ? (
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M8 5v10l7-5-7-5z"/>
                </svg>
              ) : (
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"/>
                </svg>
              )}
            </div>
            <div>
              <h2 className="text-lg font-semibold text-white truncate max-w-md">
                {file.original_filename}
              </h2>
              <p className="text-sm text-gray-400">
                {file.file_type === 'video' ? 'Video' : 'Image'} • {formatFileSize(file.file_size)}
                {file.metadata?.duration && ` • ${Math.round(file.metadata.duration)}s`}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Download button */}
            <button
              onClick={handleDownload}
              className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
              title="Download"
            >
              <Download className="w-5 h-5" />
            </button>
            
            {/* Open in studio button */}
            <button
              onClick={handleOpenStudio}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center space-x-2"
            >
              <ExternalLink className="w-4 h-4" />
              <span>Open in Studio</span>
            </button>
            
            {/* Close button */}
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex items-center justify-center bg-black min-h-[400px] max-h-[70vh]">
          {file.file_type === 'image' ? (
            <div className="relative w-full h-full flex items-center justify-center p-4">
              <img
                src={file.metadata?.originalUrl}
                alt={file.original_filename}
                className="max-w-full max-h-full object-contain rounded-lg"
                style={{ maxHeight: 'calc(70vh - 2rem)' }}
              />
            </div>
          ) : (
            <div className="relative w-full h-full flex items-center justify-center p-4">
              <video
                src={file.metadata?.originalUrl}
                controls
                className="max-w-full max-h-full rounded-lg"
                style={{ maxHeight: 'calc(70vh - 2rem)' }}
                preload="metadata"
              >
                Your browser does not support the video tag.
              </video>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
