'use client';

import { useState, useEffect, useCallback } from 'react';
import { useUser } from '@clerk/nextjs';

interface MediaFile {
  id: string;
  filename: string;
  type: 'image' | 'video';
  size: number;
  url: string;
  created_at: string;
  processing_status?: string;
}

interface MediaGridProps {
  onFileClick?: (file: MediaFile) => void;
  refreshTrigger?: number;
}

// Cache configuration
const CACHE_KEY = 'guardiavision_media_files';
const CACHE_DURATION = 2 * 60 * 1000; // 2 minutes

export function MediaGrid({ onFileClick, refreshTrigger }: MediaGridProps) {
  const { user } = useUser();
  const [files, setFiles] = useState<MediaFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'image' | 'video'>('all');

  // Cache utilities
  const getCachedFiles = useCallback(() => {
    if (typeof window === 'undefined') return null;
    
    try {
      const cached = localStorage.getItem(CACHE_KEY);
      if (!cached) return null;
      
      const data = JSON.parse(cached);
      const now = Date.now();
      
      if (now - data.timestamp < CACHE_DURATION) {
        console.log('📦 Using cached media files');
        return data.files;
      } else {
        localStorage.removeItem(CACHE_KEY);
        return null;
      }
    } catch (error) {
      console.error('❌ Cache read error:', error);
      localStorage.removeItem(CACHE_KEY);
      return null;
    }
  }, []);

  const setCachedFiles = useCallback((files: MediaFile[]) => {
    if (typeof window === 'undefined') return;
    
    try {
      const cacheData = {
        files,
        timestamp: Date.now(),
        userId: user?.id
      };
      localStorage.setItem(CACHE_KEY, JSON.stringify(cacheData));
      console.log('💾 Cached media files');
    } catch (error) {
      console.error('❌ Cache write error:', error);
    }
  }, [user?.id]);

  const fetchFiles = useCallback(async (useCache = true) => {
    if (!user) {
      setLoading(false);
      return;
    }

    // Try cache first
    if (useCache) {
      const cachedFiles = getCachedFiles();
      if (cachedFiles) {
        setFiles(cachedFiles);
        setLoading(false);
        // Still fetch fresh data in background
        fetchFiles(false);
        return;
      }
    }

    try {
      setError(null);
      if (useCache) setLoading(true);

      console.log('🔄 Fetching media files...');

      const response = await fetch('/api/user/files', {
        cache: 'no-store'
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        console.log(`✅ Loaded ${data.files.length} files`);
        setFiles(data.files);
        setCachedFiles(data.files);
      } else {
        throw new Error(data.error || 'Failed to load files');
      }

    } catch (error: any) {
      console.error('❌ Error fetching files:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  }, [user, getCachedFiles, setCachedFiles]);

  // Initial load
  useEffect(() => {
    fetchFiles();
  }, [fetchFiles]);

  // Refresh when trigger changes (e.g., after upload)
  useEffect(() => {
    if (refreshTrigger && refreshTrigger > 0) {
      console.log('🔄 Refresh triggered');
      fetchFiles(false); // Skip cache on manual refresh
    }
  }, [refreshTrigger, fetchFiles]);

  // Filter files
  const filteredFiles = files.filter(file => {
    if (filter === 'all') return true;
    return file.type === filter;
  });

  const formatFileSize = (bytes: number) => {
    const mb = bytes / (1024 * 1024);
    return mb < 1 ? `${(bytes / 1024).toFixed(0)}KB` : `${mb.toFixed(1)}MB`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!user) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-400">Please sign in to view your media</p>
      </div>
    );
  }

  if (loading && files.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <p className="text-gray-400">Loading your media...</p>
      </div>
    );
  }

  if (error && files.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-red-400 mb-4">Error loading media: {error}</p>
        <button
          onClick={() => fetchFiles(false)}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filter tabs */}
      <div className="flex space-x-1 bg-gray-800 rounded-lg p-1">
        {[
          { key: 'all', label: 'All', count: files.length },
          { key: 'image', label: 'Images', count: files.filter(f => f.type === 'image').length },
          { key: 'video', label: 'Videos', count: files.filter(f => f.type === 'video').length }
        ].map(({ key, label, count }) => (
          <button
            key={key}
            onClick={() => setFilter(key as any)}
            className={`
              flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors
              ${filter === key 
                ? 'bg-blue-600 text-white' 
                : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }
            `}
          >
            {label} ({count})
          </button>
        ))}
      </div>

      {/* Files grid */}
      {filteredFiles.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-400">
            {filter === 'all' ? 'No media files yet' : `No ${filter}s found`}
          </p>
          <p className="text-gray-500 text-sm mt-2">
            Upload your first file to get started
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {filteredFiles.map((file) => (
            <div
              key={file.id}
              onClick={() => onFileClick?.(file)}
              className="group relative bg-gray-800 rounded-lg overflow-hidden cursor-pointer hover:ring-2 hover:ring-blue-500 transition-all duration-200"
            >
              {/* Media preview */}
              <div className="aspect-square relative bg-gray-700">
                {file.type === 'image' ? (
                  <img
                    src={file.url}
                    alt={file.filename}
                    className="absolute inset-0 w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <svg className="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>
                )}
                
                {/* Overlay */}
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors" />
              </div>

              {/* File info */}
              <div className="p-3">
                <p className="text-sm font-medium text-white truncate" title={file.filename}>
                  {file.filename}
                </p>
                <div className="flex justify-between items-center mt-1">
                  <span className="text-xs text-gray-400">
                    {formatFileSize(file.size)}
                  </span>
                  <span className="text-xs text-gray-500">
                    {formatDate(file.created_at)}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Refresh indicator */}
      {loading && files.length > 0 && (
        <div className="text-center py-2">
          <span className="text-sm text-gray-400">Refreshing...</span>
        </div>
      )}
    </div>
  );
}
