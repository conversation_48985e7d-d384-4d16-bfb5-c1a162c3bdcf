'use client';

import { useCreditsCheck } from '@/hooks/useCreditsCheck';
import { LowCreditsWarning } from './LowCreditsWarning';

interface CreditsCheckerProps {
  threshold?: number; // Custom threshold for low credits warning
  showWarning?: boolean; // Whether to show the warning component
}

export function CreditsChecker({ threshold = 20, showWarning = true }: CreditsCheckerProps) {
  const { creditsData, isLoading } = useCreditsCheck();

  if (isLoading || !creditsData) {
    return null;
  }

  const isLowCredits = creditsData.credits < threshold;

  if (!showWarning || !isLowCredits) {
    return null;
  }

  return (
    <LowCreditsWarning
      credits={creditsData.credits}
      subscriptionType={creditsData.subscriptionType}
    />
  );
}

// Export the hook for direct use
export { useCreditsCheck };
