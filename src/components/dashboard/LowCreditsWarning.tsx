'use client';

import { useState } from 'react';
import { AlertTriangle, ArrowR<PERSON>, X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface LowCreditsWarningProps {
  credits: number;
  subscriptionType: string;
  onClose?: () => void;
}

export function LowCreditsWarning({ credits, subscriptionType, onClose }: LowCreditsWarningProps) {
  const [isVisible, setIsVisible] = useState(true);

  // Only show warning if credits are below 20
  if (credits >= 20 || !isVisible) {
    return null;
  }

  const handleClose = () => {
    setIsVisible(false);
    if (onClose) onClose();
  };

  const handleUpgrade = () => {
    window.location.href = '/dashboard/billing';
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="fixed top-20 right-6 z-50 w-80"
        >
          <div className="bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden">
            {/* Header */}
            <div className="flex items-center justify-between p-4 bg-amber-50 border-b border-amber-100">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <AlertTriangle className="h-5 w-5 text-amber-600" />
                </div>
                <div>
                  <h3 className="text-sm font-semibold text-gray-900">Low Credits</h3>
                  <p className="text-xs text-gray-600">
                    {credits} credits remaining
                  </p>
                </div>
              </div>
              <button
                onClick={handleClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-4 w-4" />
              </button>
            </div>

            {/* Content */}
            <div className="p-4">
              <p className="text-sm text-gray-700 mb-4">
                You're running low on credits. Upgrade your plan to continue processing without interruption.
              </p>

              {/* Upgrade Button */}
              <button
                onClick={handleUpgrade}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-4 rounded-md transition-colors duration-200 flex items-center justify-center group"
              >
                View Plans
                <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-0.5 transition-transform" />
              </button>

              {/* Plan Info */}
              <div className="mt-3 pt-3 border-t border-gray-100">
                <div className="flex justify-between text-xs text-gray-500">
                  <span>Current: {subscriptionType}</span>
                  <span>{credits} credits left</span>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
