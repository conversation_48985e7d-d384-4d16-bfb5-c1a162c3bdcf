'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { OptimizedFileUploader as FileUploader } from '@/components/uploads/OptimizedFileUploader';
import { MediaCard } from '@/components/media/MediaCard';
import { PreviewModal } from '@/components/media/PreviewModal';
import { RefreshCw, Trash2, Grid3x3, Grid2x2, Rows3, Sparkles, Image as ImageIcon, Video as VideoIcon, LayoutGrid } from 'lucide-react';

type MediaType = 'all' | 'video' | 'photo';
type ViewMode = 'grid-large' | 'grid-small' | 'list';

interface MediaItem {
  id: string;
  original_filename: string;
  file_type: 'image' | 'video';
  file_size: number;
  created_at: string;
  upload_date?: string;
  last_accessed_date?: string;
  processed_image_url?: string;
  completion_date?: string;
  download_count?: number;
  url?: string;
  metadata?: {
    originalUrl?: string;
    duration?: number;
  };
}

interface CategorizedMedia {
  last24Hours: MediaItem[];
  thisWeek: MediaItem[];
  thisMonth: MediaItem[];
  older: MediaItem[];
  draft: MediaItem[];
  completed: MediaItem[];
}

export function MediaDashboard() {
  const { user } = useUser();
  const router = useRouter();
  const [mediaType, setMediaType] = useState<MediaType>('all');
  const [viewMode, setViewMode] = useState<ViewMode>('grid-large');
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [categorizedMedia, setCategorizedMedia] = useState<CategorizedMedia>({
    last24Hours: [],
    thisWeek: [],
    thisMonth: [],
    older: [],
    draft: [],
    completed: []
  });
  const [loading, setLoading] = useState(false);
  const [deletingFiles, setDeletingFiles] = useState<Set<string>>(new Set());
  const [error, setError] = useState<string | null>(null);
  const [deletingAll, setDeletingAll] = useState(false);
  const [uploadInProgress, setUploadInProgress] = useState(false);
  const [syncingStorage, setSyncingStorage] = useState(false);
  const [previewFile, setPreviewFile] = useState<MediaItem | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  // Time-based categorization helper functions
  const categorizeMediaByTime = (items: MediaItem[]): CategorizedMedia => {
    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const categorized: CategorizedMedia = {
      last24Hours: [],
      thisWeek: [],
      thisMonth: [],
      older: [],
      draft: [],
      completed: []
    };

    items.forEach(item => {
      const uploadDate = new Date(item.upload_date || item.created_at);

      // Time-based categorization
      if (uploadDate >= oneDayAgo) {
        categorized.last24Hours.push(item);
      } else if (uploadDate >= oneWeekAgo) {
        categorized.thisWeek.push(item);
      } else if (uploadDate >= oneMonthAgo) {
        categorized.thisMonth.push(item);
      } else {
        categorized.older.push(item);
      }

      // Status-based categorization
      if (item.processed_image_url && !item.completion_date) {
        categorized.draft.push(item);
      } else if (item.completion_date) {
        categorized.completed.push(item);
      }
    });

    return categorized;
  };

  // Format relative time display
  const formatRelativeTime = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours} hours ago`;

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays} days ago`;

    const diffInWeeks = Math.floor(diffInDays / 7);
    if (diffInWeeks < 4) return `${diffInWeeks} weeks ago`;

    const diffInMonths = Math.floor(diffInDays / 30);
    return `${diffInMonths} months ago`;
  };

  // Update last accessed date when file is opened
  const updateLastAccessedDate = async (fileId: string) => {
    try {
      await fetch('/api/user/files/update-access', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ fileId, lastAccessedDate: new Date().toISOString() })
      });
    } catch (error) {
      console.error('Failed to update last accessed date:', error);
    }
  };

  // Function to fetch media data
  const fetchMediaData = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      console.log('🔍 Fetching media files from API...');
      const response = await fetch('/api/user/files', {
        cache: 'no-store'
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('📦 API Response:', result);

      if (result.success && Array.isArray(result.files)) {
        console.log(`✅ Loaded ${result.files.length} files`);
        setMediaItems(result.files);
        // Categorize media by time and status
        const categorized = categorizeMediaByTime(result.files);
        setCategorizedMedia(categorized);
      } else {
        console.error('❌ Invalid API response:', result);
        setError('Failed to load media files');
        setMediaItems([]);
        setCategorizedMedia({
          last24Hours: [],
          thisWeek: [],
          thisMonth: [],
          older: [],
          draft: [],
          completed: []
        });
      }
    } catch (error: any) {
      console.error('❌ Error fetching media:', error);
      setError(`Failed to load media files: ${error.message}`);
      setMediaItems([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMediaData();
  }, [user]);

  const handleUploadComplete = async (uploadResults: any) => {
    console.log('📤 Upload complete, refreshing media list...', uploadResults);
    if (uploadResults && uploadResults.success) {
      setError(null);
      await fetchMediaData();
    } else {
      // Don't set error here as the uploader component handles it
      await fetchMediaData(); // Still refresh to show any successful uploads
    }
  };

  const handleDeleteFile = async (fileId: string, fileName: string) => {
    if (!confirm(`Are you sure you want to delete "${fileName}"?`)) return;

    setDeletingFiles(prev => new Set(prev).add(fileId));

    try {
      const response = await fetch(`/api/user/files/${fileId}`, { method: 'DELETE' });
      const result = await response.json();

      if (result.success) {
        await fetchMediaData();
      } else {
        alert(`Failed to delete file: ${result.error}`);
      }
    } catch (error: any) {
      alert(`Error deleting file: ${error.message}`);
    } finally {
      setDeletingFiles(prev => {
        const newSet = new Set(prev);
        newSet.delete(fileId);
        return newSet;
      });
    }
  };

  const handleDeleteAll = async () => {
    if (mediaItems.length === 0) {
      alert('No files to delete.');
      return;
    }

    if (!confirm(`Delete ALL ${mediaItems.length} files?`)) return;

    setDeletingAll(true);
    try {
      const deletePromises = mediaItems.map(async (item) => {
        const response = await fetch(`/api/user/files/${item.id}`, { method: 'DELETE' });
        const result = await response.json();
        return result.success;
      });

      await Promise.all(deletePromises);
      await fetchMediaData();
      alert('All files deleted successfully!');
    } catch (error: any) {
      alert(`Error during bulk delete: ${error.message}`);
    } finally {
      setDeletingAll(false);
    }
  };

  const handleSyncStorage = async () => {
    setSyncingStorage(true);
    try {
      const response = await fetch('/api/sync-storage', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      });

      if (response.ok) {
        await fetchMediaData();
      } else {
        throw new Error('Storage sync failed');
      }
    } catch (error: any) {
      setError(`Storage sync failed: ${error.message}`);
    } finally {
      setSyncingStorage(false);
    }
  };

  const filteredItems = mediaItems.filter(item => {
    if (mediaType === 'video') return item.file_type === 'video';
    if (mediaType === 'photo') return item.file_type === 'image';
    return true;
  });

  const getGridClass = () => {
    switch (viewMode) {
      case 'grid-large':
        return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6';
      case 'grid-small':
        return 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4';
      case 'list':
        return 'flex flex-col gap-3';
      default:
        return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-950 via-black to-gray-950 text-white">
      <div className="max-w-[1800px] mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Header with glassmorphism effect */}
        <div className="mb-8 relative">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 via-blue-600/10 to-purple-600/10 rounded-2xl blur-2xl" />
          <div className="relative backdrop-blur-xl bg-white/5 rounded-2xl border border-white/10 p-6 shadow-2xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-gradient-to-br from-purple-500 to-blue-600 rounded-xl shadow-lg shadow-purple-500/50">
                  <Sparkles className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-white via-gray-200 to-gray-400 bg-clip-text text-transparent">
                    Media Dashboard
                  </h1>
                  <p className="text-gray-400 text-sm mt-1">
                    Manage your privacy-protected media files
                  </p>
                </div>
              </div>

              {/* View mode toggle */}
              <div className="hidden md:flex items-center space-x-2 bg-white/5 rounded-lg p-1 border border-white/10">
                <button
                  onClick={() => setViewMode('grid-large')}
                  className={`p-2 rounded transition-all duration-200 ${
                    viewMode === 'grid-large'
                      ? 'bg-purple-600 text-white shadow-lg shadow-purple-600/50'
                      : 'text-gray-400 hover:text-white hover:bg-white/10'
                  }`}
                  title="Large Grid"
                >
                  <Grid3x3 className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewMode('grid-small')}
                  className={`p-2 rounded transition-all duration-200 ${
                    viewMode === 'grid-small'
                      ? 'bg-purple-600 text-white shadow-lg shadow-purple-600/50'
                      : 'text-gray-400 hover:text-white hover:bg-white/10'
                  }`}
                  title="Small Grid"
                >
                  <Grid2x2 className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded transition-all duration-200 ${
                    viewMode === 'list'
                      ? 'bg-purple-600 text-white shadow-lg shadow-purple-600/50'
                      : 'text-gray-400 hover:text-white hover:bg-white/10'
                  }`}
                  title="List View"
                >
                  <Rows3 className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Error Display with modern styling */}
        {error && (
          <div className="mb-6 relative">
            <div className="absolute inset-0 bg-red-600/20 rounded-xl blur-xl" />
            <div className="relative backdrop-blur-xl bg-red-950/40 border border-red-500/30 rounded-xl p-4 shadow-xl">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 w-2 h-2 bg-red-500 rounded-full animate-pulse" />
                  <div>
                    <h3 className="text-sm font-medium text-red-300">Error</h3>
                    <p className="mt-1 text-sm text-red-200">{error}</p>
                  </div>
                </div>
                <button
                  onClick={() => setError(null)}
                  className="text-red-300 hover:text-red-100 transition-colors duration-200"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Controls Bar */}
        <div className="mb-6 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <h2 className="text-xl font-semibold text-gray-200">
            {mediaType === 'all' ? 'All Media' : mediaType === 'video' ? 'Videos' : 'Photos'}
            <span className="ml-2 px-3 py-1 text-sm bg-purple-600/20 text-purple-300 rounded-full border border-purple-500/30">
              {filteredItems.length}
            </span>
          </h2>

          <div className="flex items-center space-x-2">
            {mediaItems.length > 0 && (
              <button
                onClick={handleDeleteAll}
                disabled={deletingAll || loading}
                className="group flex items-center px-4 py-2 text-sm text-red-300 bg-red-950/30 border border-red-500/30 rounded-lg hover:bg-red-950/50 hover:border-red-500/50 disabled:opacity-50 transition-all duration-200 hover:shadow-lg hover:shadow-red-500/20"
              >
                {deletingAll ? (
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-red-300 border-t-transparent" />
                ) : (
                  <Trash2 className="mr-2 h-4 w-4 group-hover:scale-110 transition-transform duration-200" />
                )}
                {deletingAll ? 'Deleting...' : 'Delete All'}
              </button>
            )}

            <button
              onClick={fetchMediaData}
              disabled={loading}
              className="group flex items-center px-4 py-2 text-sm text-blue-300 bg-blue-950/30 border border-blue-500/30 rounded-lg hover:bg-blue-950/50 hover:border-blue-500/50 disabled:opacity-50 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20"
            >
              {loading ? (
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-blue-300 border-t-transparent" />
              ) : (
                <RefreshCw className="mr-2 h-4 w-4 group-hover:rotate-180 transition-transform duration-500" />
              )}
              {loading ? 'Loading...' : 'Refresh'}
            </button>
          </div>
        </div>

        {/* Upload area with modern design */}
        <div className="mb-8">
          <FileUploader
            onUploadComplete={handleUploadComplete}
          />
        </div>

        {/* Media type tabs with modern design and icons */}
        <div className="mb-6 flex items-center space-x-1 bg-white/5 rounded-lg p-1 border border-white/10 backdrop-blur-xl w-fit">
          {([
            { type: 'all' as const, label: 'All Media', icon: LayoutGrid },
            { type: 'video' as const, label: 'Videos', icon: VideoIcon },
            { type: 'photo' as const, label: 'Photos', icon: ImageIcon }
          ]).map(({ type, label, icon: Icon }) => (
            <button
              key={type}
              className={`flex items-center space-x-2 px-6 py-2.5 text-sm font-medium rounded-md transition-all duration-200 ${
                mediaType === type
                  ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-lg shadow-purple-600/50'
                  : 'text-gray-400 hover:text-white hover:bg-white/10'
              }`}
              onClick={() => setMediaType(type)}
            >
              <Icon className="w-4 h-4" />
              <span>{label}</span>
            </button>
          ))}
        </div>

        {/* Media Grid */}
        {loading ? (
          <div className="flex items-center justify-center py-20">
            <div className="text-center">
              <div className="relative mx-auto h-16 w-16 mb-6">
                <div className="absolute inset-0 rounded-full border-4 border-purple-600/20"></div>
                <div className="absolute inset-0 rounded-full border-4 border-purple-600 border-t-transparent animate-spin"></div>
              </div>
              <p className="text-gray-400 text-lg">Loading your media...</p>
            </div>
          </div>
        ) : filteredItems.length === 0 ? (
          <div className="text-center py-20">
            <div className="relative mx-auto h-32 w-32 mb-6">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-600/20 to-blue-600/20 rounded-2xl blur-2xl"></div>
              <div className="relative flex items-center justify-center h-full">
                <svg className="w-20 h-20 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"/>
                </svg>
              </div>
            </div>
            <h3 className="text-xl font-medium text-gray-300 mb-2">No media files yet</h3>
            <p className="text-gray-500">Upload your first image or video to get started</p>
          </div>
        ) : (
          <div className={getGridClass()}>
            {filteredItems
              .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
              .map((item) => (
                <MediaCard
                  key={item.id}
                  file={item}
                  onPreview={(file) => {
                    setPreviewFile(file);
                    setShowPreview(true);
                  }}
                  onDelete={handleDeleteFile}
                  onOpenStudio={(fileId) => {
                    console.log('🎬 Opening studio for file:', fileId);
                    router.push(`/studio?file=${fileId}`);
                  }}
                  isDeleting={deletingFiles.has(item.id)}
                />
              ))}
          </div>
        )}

        {/* Preview Modal */}
        <PreviewModal
          file={previewFile}
          isOpen={showPreview}
          onClose={() => {
            setShowPreview(false);
            setPreviewFile(null);
          }}
          onOpenStudio={(fileId) => {
            console.log('🎬 Opening studio from preview for file:', fileId);
            router.push(`/studio?file=${fileId}`);
          }}
        />
      </div>
    </div>
  );
}
