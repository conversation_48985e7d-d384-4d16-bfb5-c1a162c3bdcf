'use client';

import { useState } from 'react';
import { AlertTriangle, ArrowR<PERSON>, X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface InlineLowCreditsWarningProps {
  credits: number;
  subscriptionType: string;
  onClose?: () => void;
}

export function InlineLowCreditsWarning({ credits, subscriptionType, onClose }: InlineLowCreditsWarningProps) {
  const [isVisible, setIsVisible] = useState(true);

  // Only show warning if credits are below 20
  if (credits >= 20 || !isVisible) {
    return null;
  }

  const handleClose = () => {
    setIsVisible(false);
    if (onClose) onClose();
  };

  const handleUpgrade = () => {
    window.location.href = '/dashboard/billing';
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="mb-6"
        >
          <div className="bg-gradient-to-r from-gray-800 to-gray-900 border border-amber-500/30 rounded-lg p-4">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-0.5">
                  <AlertTriangle className="h-5 w-5 text-amber-400" />
                </div>
                <div className="flex-1">
                  <h3 className="text-sm font-semibold text-white mb-1">
                    Low Credits Warning
                  </h3>
                  <p className="text-sm text-gray-300 mb-3">
                    You have <span className="font-semibold text-amber-400">{credits} credits</span> remaining.
                    Upgrade your plan to continue processing without interruption.
                  </p>
                  <button
                    onClick={handleUpgrade}
                    className="inline-flex items-center px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors duration-200 group"
                  >
                    View Plans
                    <ArrowRight className="h-3 w-3 ml-1 group-hover:translate-x-0.5 transition-transform" />
                  </button>
                </div>
              </div>
              <button
                onClick={handleClose}
                className="flex-shrink-0 text-gray-400 hover:text-gray-300 transition-colors ml-4"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
