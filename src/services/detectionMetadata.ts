import { createServiceRoleSupabaseClient } from '@/utils/supabase/server';
import { supabase } from '@/lib/supabase';

export interface DetectionBox {
  box_id: string;
  label: string;
  box: [number, number, number, number]; // [x1, y1, x2, y2]
  score: number;
}

export interface DetectionMetadata {
  filename: string;
  detection_all: {
    box_ids: string[];
    labels: string[];
    boxes: number[][];
    scores: number[];
  };
}

export interface ProcessedDetection {
  box_id: string;
  label: string;
  x1: number;
  y1: number;
  x2: number;
  y2: number;
  width: number;
  height: number;
  score: number;
}

/**
 * Fetches detection metadata JSON from Supabase results bucket
 * @param metadataPath - Path to the JSON file in results bucket (e.g., "user_123/detections/jobid_metadata.json")
 * @returns Detection metadata object
 */
export async function fetchDetectionMetadata(metadataPath: string): Promise<DetectionMetadata> {
  try {
    // Download JSON file from Supabase storage
    const { data, error } = await supabase
      .storage
      .from('results')
      .download(metadataPath);

    if (error) {
      console.error('Error downloading detection metadata:', error);
      throw new Error(`Failed to fetch detection metadata: ${error.message}`);
    }

    // Parse JSON
    const text = await data.text();
    const metadata: DetectionMetadata = JSON.parse(text);

    console.log(`✅ Fetched detection metadata: ${metadata.detection_all.box_ids.length} detections`);
    return metadata;
  } catch (error) {
    console.error('Error fetching detection metadata:', error);
    throw error;
  }
}

/**
 * Processes raw detection metadata into a more usable format
 * @param metadata - Raw detection metadata from JSON
 * @param scoreThreshold - Minimum confidence score (0-1)
 * @returns Array of processed detections above threshold
 */
export function processDetections(
  metadata: DetectionMetadata,
  scoreThreshold: number = 0.1
): ProcessedDetection[] {
  const { box_ids, labels, boxes, scores } = metadata.detection_all;

  const detections: ProcessedDetection[] = [];

  for (let i = 0; i < box_ids.length; i++) {
    const score = scores[i];

    // Filter by confidence threshold
    if (score < scoreThreshold) {
      continue;
    }

    const [x1, y1, x2, y2] = boxes[i];

    detections.push({
      box_id: box_ids[i],
      label: labels[i],
      x1,
      y1,
      x2,
      y2,
      width: x2 - x1,
      height: y2 - y1,
      score,
    });
  }

  return detections;
}

/**
 * Fetches and processes detection metadata in one call
 * @param metadataPath - Path to the JSON file in results bucket
 * @param scoreThreshold - Minimum confidence score (0-1)
 * @returns Array of processed detections above threshold
 */
export async function fetchAndProcessDetections(
  metadataPath: string,
  scoreThreshold: number = 0.1
): Promise<{ metadata: DetectionMetadata; detections: ProcessedDetection[] }> {
  const metadata = await fetchDetectionMetadata(metadataPath);
  const detections = processDetections(metadata, scoreThreshold);

  return { metadata, detections };
}

/**
 * Gets count of detections above a given threshold
 * @param metadata - Detection metadata
 * @param scoreThreshold - Minimum confidence score (0-1)
 * @returns Count of detections above threshold
 */
export function getDetectionCount(
  metadata: DetectionMetadata,
  scoreThreshold: number = 0.1
): number {
  return metadata.detection_all.scores.filter(score => score >= scoreThreshold).length;
}
