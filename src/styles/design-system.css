/**
 * Design System Configuration
 * Standardized typography, colors, and spacing for Guardiavision
 */

/* ===========================
   TYPOGRAPHY SYSTEM
   =========================== */

/* Heading hierarchy with consistent sizing */
.text-display-xl {
  @apply text-5xl font-extrabold tracking-tight sm:text-6xl lg:text-7xl;
}

.text-display-lg {
  @apply text-4xl font-extrabold tracking-tight sm:text-5xl lg:text-6xl;
}

.text-display-md {
  @apply text-3xl font-bold tracking-tight sm:text-4xl lg:text-5xl;
}

.text-heading-xl {
  @apply text-3xl font-bold tracking-tight sm:text-4xl;
}

.text-heading-lg {
  @apply text-2xl font-bold tracking-tight sm:text-3xl;
}

.text-heading-md {
  @apply text-xl font-semibold tracking-tight sm:text-2xl;
}

.text-heading-sm {
  @apply text-lg font-semibold tracking-tight sm:text-xl;
}

/* Body text variants */
.text-body-xl {
  @apply text-xl leading-relaxed;
}

.text-body-lg {
  @apply text-lg leading-relaxed;
}

.text-body-md {
  @apply text-base leading-relaxed;
}

.text-body-sm {
  @apply text-sm leading-relaxed;
}

/* Specialized text classes */
.text-caption {
  @apply text-sm text-gray-400;
}

.text-overline {
  @apply text-xs font-semibold uppercase tracking-wider;
}

/* ===========================
   COLOR SYSTEM (WCAG AA Compliant)
   =========================== */

/* Primary text colors with proper contrast */
.text-primary-light {
  @apply text-white;
  /* Contrast ratio: 21:1 on navy background */
}

.text-primary-default {
  @apply text-gray-100;
  /* Contrast ratio: 18:1 on navy background */
}

.text-secondary {
  @apply text-gray-300;
  /* Contrast ratio: 12:1 on navy background */
}

.text-muted {
  @apply text-gray-400;
  /* Contrast ratio: 7:1 on navy background (WCAG AA compliant) */
}

/* Accent colors with sufficient contrast */
.text-accent-green {
  @apply text-green-400;
  /* Updated for better visibility */
}

.text-accent-blue {
  @apply text-blue-400;
}

/* Background colors */
.bg-primary {
  @apply bg-navy;
}

.bg-primary-dark {
  @apply bg-navy-dark;
}

.bg-primary-light {
  @apply bg-navy-light;
}

/* ===========================
   SPACING SYSTEM
   =========================== */

/* Consistent section spacing */
.section-padding {
  @apply py-16 sm:py-20 lg:py-24;
}

.section-padding-sm {
  @apply py-12 sm:py-14 lg:py-16;
}

.section-padding-lg {
  @apply py-20 sm:py-24 lg:py-32;
}

/* Container spacing */
.container-padding {
  @apply px-4 sm:px-6 lg:px-8;
}

.container-padding-lg {
  @apply px-6 sm:px-8 lg:px-12;
}

/* Content spacing */
.content-gap {
  @apply space-y-4;
}

.content-gap-lg {
  @apply space-y-6;
}

.content-gap-xl {
  @apply space-y-8;
}

/* Grid gaps */
.grid-gap {
  @apply gap-6;
}

.grid-gap-lg {
  @apply gap-8;
}

.grid-gap-xl {
  @apply gap-10;
}

/* ===========================
   BUTTON SYSTEM
   =========================== */

/* Base button styles */
.btn-base {
  @apply inline-flex items-center justify-center rounded-lg font-semibold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-navy;
}

/* Button sizes */
.btn-sm {
  @apply px-4 py-2 text-sm;
}

.btn-md {
  @apply px-6 py-3 text-base;
}

.btn-lg {
  @apply px-8 py-4 text-lg;
}

.btn-xl {
  @apply px-10 py-5 text-xl;
}

/* Primary button (green CTA) */
.btn-primary {
  @apply btn-base bg-green-400 text-navy hover:bg-green-500 focus:ring-green-400;
}

/* Secondary button (blue outline) */
.btn-secondary {
  @apply btn-base border-2 border-blue-400 text-blue-400 hover:bg-blue-500 hover:border-transparent hover:text-white focus:ring-blue-400;
}

/* Ghost button (transparent) */
.btn-ghost {
  @apply btn-base border border-gray-300 text-gray-300 hover:bg-navy-light hover:border-green-400 hover:text-green-400 focus:ring-gray-300;
}

/* ===========================
   CARD SYSTEM
   =========================== */

.card-base {
  @apply rounded-lg border bg-navy-dark transition-all duration-300;
}

.card-interactive {
  @apply card-base border-navy-light hover:border-green-400/50 hover:shadow-lg hover:shadow-green-400/10;
}

.card-padding {
  @apply p-6;
}

.card-padding-lg {
  @apply p-8;
}

/* ===========================
   ACCESSIBILITY UTILITIES
   =========================== */

/* Focus visible states */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-offset-2 focus:ring-offset-navy;
}

/* Skip to content link for keyboard navigation */
.skip-to-content {
  @apply absolute -top-40 left-0 z-[100] bg-green-400 px-6 py-3 text-navy font-bold focus:top-4 focus:left-4 transition-all;
}

/* Screen reader only */
.sr-only-focusable:not(:focus):not(:focus-within) {
  @apply sr-only;
}

/* ===========================
   GRADIENT UTILITIES
   =========================== */

.gradient-text {
  @apply bg-gradient-to-r from-blue-400 to-green-400 bg-clip-text text-transparent;
}

.gradient-text-alt {
  @apply bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent;
}

/* Enhanced glow effects with better performance */
.glow-green {
  @apply shadow-lg shadow-green-400/20 hover:shadow-xl hover:shadow-green-400/40 transition-shadow duration-300;
}

.glow-blue {
  @apply shadow-lg shadow-blue-400/20 hover:shadow-xl hover:shadow-blue-400/40 transition-shadow duration-300;
}

/* ===========================
   RESPONSIVE UTILITIES
   =========================== */

/* Mobile-first spacing utilities */
@media (max-width: 640px) {
  .section-padding {
    @apply py-12;
  }

  .container-padding-lg {
    @apply px-4;
  }
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
