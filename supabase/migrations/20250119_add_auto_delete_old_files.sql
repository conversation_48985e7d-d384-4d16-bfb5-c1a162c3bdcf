-- Function to automatically delete files older than 30 days
-- This keeps storage usage under control and removes stale files

CREATE OR REPLACE FUNCTION delete_old_files()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    deleted_count INTEGER;
    thirty_days_ago TIMESTAMP WITH TIME ZONE;
BEGIN
    thirty_days_ago := NOW() - INTERVAL '30 days';

    -- Log the operation start
    RAISE NOTICE 'Starting automatic deletion of files older than 30 days...';

    -- Delete old files from user_files table
    WITH deleted AS (
        DELETE FROM user_files
        WHERE created_at < thirty_days_ago
        RETURNING id
    )
    SELECT COUNT(*) INTO deleted_count FROM deleted;

    -- Log the result
    RAISE NOTICE 'Deleted % old files from user_files', deleted_count;

    -- Also clean up orphaned studio_states
    DELETE FROM studio_states
    WHERE file_id NOT IN (SELECT id FROM user_files);

    -- Clean up orphaned processed_files
    DELETE FROM processed_files
    WHERE original_file_id NOT IN (SELECT id FROM user_files);

    RAISE NOTICE 'Cleaned up orphaned studio_states and processed_files';
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION delete_old_files() TO service_role;

-- Add comment
COMMENT ON FUNCTION delete_old_files() IS 'Deletes files older than 30 days and their related records';

-- Note: To set up the actual cron job, you need to use Supabase's pg_cron extension
-- This can be done via the Supabase dashboard or by running:
--
-- SELECT cron.schedule(
--     'delete-old-files-daily',
--     '0 2 * * *', -- Run at 2 AM every day
--     $$SELECT delete_old_files()$$
-- );
--
-- Or manually call this function via an API route with proper authentication
