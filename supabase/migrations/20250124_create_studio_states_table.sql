-- Create studio_states table for saving user's studio session state
-- This table stores the current state of the GuardiaVision Studio for each user and file

CREATE TABLE IF NOT EXISTS studio_states (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id TEXT NOT NULL,
    file_id TEXT NOT NULL,
    state_data JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one state per user per file
    UNIQUE(user_id, file_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_studio_states_user_id ON studio_states(user_id);
CREATE INDEX IF NOT EXISTS idx_studio_states_file_id ON studio_states(file_id);
CREATE INDEX IF NOT EXISTS idx_studio_states_user_file ON studio_states(user_id, file_id);
CREATE INDEX IF NOT EXISTS idx_studio_states_updated_at ON studio_states(updated_at);

-- <PERSON><PERSON> function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_studio_states_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at
DROP TRIGGER IF EXISTS trigger_update_studio_states_updated_at ON studio_states;
CREATE TRIGGER trigger_update_studio_states_updated_at
    BEFORE UPDATE ON studio_states
    FOR EACH ROW
    EXECUTE FUNCTION update_studio_states_updated_at();

-- Enable Row Level Security
ALTER TABLE studio_states ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only access their own studio states
CREATE POLICY "Users can view their own studio states" ON studio_states
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own studio states" ON studio_states
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own studio states" ON studio_states
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own studio states" ON studio_states
    FOR DELETE USING (auth.uid()::text = user_id);

-- Grant necessary permissions
GRANT ALL ON studio_states TO authenticated;
GRANT ALL ON studio_states TO service_role;

-- Add helpful comments
COMMENT ON TABLE studio_states IS 'Stores GuardiaVision Studio session state for each user and file';
COMMENT ON COLUMN studio_states.user_id IS 'Clerk user ID';
COMMENT ON COLUMN studio_states.file_id IS 'Reference to user_files.id';
COMMENT ON COLUMN studio_states.state_data IS 'JSON object containing studio state (detected objects, custom areas, settings, etc.)';
COMMENT ON COLUMN studio_states.created_at IS 'When the studio state was first created';
COMMENT ON COLUMN studio_states.updated_at IS 'When the studio state was last updated';
