-- Add workflow status and file type tracking to user_files table
-- This enables proper draft/completed status tracking and better file organization

-- Add workflow_status column (null = uploaded but not opened, 'draft' = opened in studio, 'completed' = saved from studio)
ALTER TABLE user_files
ADD COLUMN IF NOT EXISTS workflow_status TEXT CHECK (workflow_status IN ('draft', 'completed')) DEFAULT NULL;

-- Add file_type column for easier filtering (image/video)
ALTER TABLE user_files
ADD COLUMN IF NOT EXISTS file_type TEXT;

-- Add original_filename column to preserve the original name
ALTER TABLE user_files
ADD COLUMN IF NOT EXISTS original_filename TEXT;

-- Add url column for storing Supabase storage URLs
ALTER TABLE user_files
ADD COLUMN IF NOT EXISTS url TEXT;

-- Add last_accessed_at to track when file was last opened in studio
ALTER TABLE user_files
ADD COLUMN IF NOT EXISTS last_accessed_at TIMESTAMP WITH TIME ZONE;

-- Create index for workflow_status for efficient querying
CREATE INDEX IF NOT EXISTS idx_user_files_workflow_status ON user_files(workflow_status);

-- Create index for file_type for efficient filtering
CREATE INDEX IF NOT EXISTS idx_user_files_file_type ON user_files(file_type);

-- Create composite index for user + workflow status queries
CREATE INDEX IF NOT EXISTS idx_user_files_user_workflow ON user_files(user_id, workflow_status);

-- Create index for last_accessed_at to enable file cleanup
CREATE INDEX IF NOT EXISTS idx_user_files_last_accessed ON user_files(last_accessed_at);

-- Update existing records to set file_type based on mime_type
UPDATE user_files
SET file_type = CASE
    WHEN mime_type LIKE 'image/%' THEN 'image'
    WHEN mime_type LIKE 'video/%' THEN 'video'
    ELSE 'unknown'
END
WHERE file_type IS NULL;

-- Update existing records to set original_filename from filename if not set
UPDATE user_files
SET original_filename = filename
WHERE original_filename IS NULL;

-- Add comment to explain workflow_status
COMMENT ON COLUMN user_files.workflow_status IS 'Workflow status: NULL (just uploaded), draft (opened in studio), completed (saved from studio)';
COMMENT ON COLUMN user_files.file_type IS 'File type: image or video';
COMMENT ON COLUMN user_files.last_accessed_at IS 'Last time file was opened in studio';

-- Grant necessary permissions
GRANT ALL ON user_files TO authenticated;
GRANT ALL ON user_files TO service_role;
