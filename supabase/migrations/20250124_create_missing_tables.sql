-- Create missing tables that are causing refresh errors
-- This migration ensures all required tables exist with proper RLS policies

-- Create processed_files table if it doesn't exist
CREATE TABLE IF NOT EXISTS processed_files (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id TEXT NOT NULL,
    original_file_id TEXT NOT NULL,
    processed_image_url TEXT,
    processing_settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for processed_files
CREATE INDEX IF NOT EXISTS idx_processed_files_user_id ON processed_files(user_id);
CREATE INDEX IF NOT EXISTS idx_processed_files_original_file_id ON processed_files(original_file_id);
CREATE INDEX IF NOT EXISTS idx_processed_files_user_file ON processed_files(user_id, original_file_id);
CREATE INDEX IF NOT EXISTS idx_processed_files_created_at ON processed_files(created_at);

-- Enable RLS on processed_files
ALTER TABLE processed_files ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for processed_files
CREATE POLICY "Users can view their own processed files" ON processed_files
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own processed files" ON processed_files
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own processed files" ON processed_files
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own processed files" ON processed_files
    FOR DELETE USING (auth.uid()::text = user_id);

-- Create user_files table if it doesn't exist (for file access)
CREATE TABLE IF NOT EXISTS user_files (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    filename TEXT NOT NULL,
    file_path TEXT,
    file_size BIGINT,
    mime_type TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for user_files
CREATE INDEX IF NOT EXISTS idx_user_files_user_id ON user_files(user_id);
CREATE INDEX IF NOT EXISTS idx_user_files_created_at ON user_files(created_at);

-- Enable RLS on user_files
ALTER TABLE user_files ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for user_files
CREATE POLICY "Users can view their own files" ON user_files
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own files" ON user_files
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own files" ON user_files
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own files" ON user_files
    FOR DELETE USING (auth.uid()::text = user_id);

-- Grant necessary permissions
GRANT ALL ON processed_files TO authenticated;
GRANT ALL ON processed_files TO service_role;
GRANT ALL ON user_files TO authenticated;
GRANT ALL ON user_files TO service_role;

-- Add helpful comments
COMMENT ON TABLE processed_files IS 'Stores processed image results and settings for GuardiaVision Studio';
COMMENT ON TABLE user_files IS 'Stores user uploaded files metadata';

-- Create function to handle file access validation
CREATE OR REPLACE FUNCTION validate_file_access(file_id TEXT, requesting_user_id TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    -- Check if user has access to this file
    RETURN EXISTS (
        SELECT 1 FROM user_files 
        WHERE id = file_id AND user_id = requesting_user_id
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION validate_file_access(TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION validate_file_access(TEXT, TEXT) TO service_role;
