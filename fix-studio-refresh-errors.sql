-- ============================================================================
-- GUARDIAVISION STUDIO REFRESH ERRORS FIX
-- Run this script in Supabase SQL Editor to fix "Access Denied" and "File Not Found" errors
-- ============================================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ============================================================================
-- 1. CREATE USER_FILES TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS user_files (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    filename TEXT NOT NULL,
    file_path TEXT,
    file_size BIGINT,
    mime_type TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for user_files
CREATE INDEX IF NOT EXISTS idx_user_files_user_id ON user_files(user_id);
CREATE INDEX IF NOT EXISTS idx_user_files_created_at ON user_files(created_at);

-- Enable RLS on user_files
ALTER TABLE user_files ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Users can view their own files" ON user_files;
DROP POLICY IF EXISTS "Users can insert their own files" ON user_files;
DROP POLICY IF EXISTS "Users can update their own files" ON user_files;
DROP POLICY IF EXISTS "Users can delete their own files" ON user_files;

-- Create RLS policies for user_files
CREATE POLICY "Users can view their own files" ON user_files
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own files" ON user_files
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own files" ON user_files
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own files" ON user_files
    FOR DELETE USING (auth.uid()::text = user_id);

-- ============================================================================
-- 2. CREATE PROCESSED_FILES TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS processed_files (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id TEXT NOT NULL,
    original_file_id TEXT NOT NULL,
    processed_image_url TEXT,
    processing_settings JSONB DEFAULT '{}',
    file_name TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for processed_files
CREATE INDEX IF NOT EXISTS idx_processed_files_user_id ON processed_files(user_id);
CREATE INDEX IF NOT EXISTS idx_processed_files_original_file_id ON processed_files(original_file_id);
CREATE INDEX IF NOT EXISTS idx_processed_files_user_file ON processed_files(user_id, original_file_id);
CREATE INDEX IF NOT EXISTS idx_processed_files_created_at ON processed_files(created_at);

-- Enable RLS on processed_files
ALTER TABLE processed_files ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Users can view their own processed files" ON processed_files;
DROP POLICY IF EXISTS "Users can insert their own processed files" ON processed_files;
DROP POLICY IF EXISTS "Users can update their own processed files" ON processed_files;
DROP POLICY IF EXISTS "Users can delete their own processed files" ON processed_files;

-- Create RLS policies for processed_files
CREATE POLICY "Users can view their own processed files" ON processed_files
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own processed files" ON processed_files
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own processed files" ON processed_files
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own processed files" ON processed_files
    FOR DELETE USING (auth.uid()::text = user_id);

-- ============================================================================
-- 3. CREATE STUDIO_STATES TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS studio_states (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id TEXT NOT NULL,
    file_id TEXT NOT NULL,
    state_data JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one state per user per file
    UNIQUE(user_id, file_id)
);

-- Create indexes for studio_states
CREATE INDEX IF NOT EXISTS idx_studio_states_user_id ON studio_states(user_id);
CREATE INDEX IF NOT EXISTS idx_studio_states_file_id ON studio_states(file_id);
CREATE INDEX IF NOT EXISTS idx_studio_states_user_file ON studio_states(user_id, file_id);
CREATE INDEX IF NOT EXISTS idx_studio_states_updated_at ON studio_states(updated_at);

-- Enable RLS on studio_states
ALTER TABLE studio_states ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Users can view their own studio states" ON studio_states;
DROP POLICY IF EXISTS "Users can insert their own studio states" ON studio_states;
DROP POLICY IF EXISTS "Users can update their own studio states" ON studio_states;
DROP POLICY IF EXISTS "Users can delete their own studio states" ON studio_states;

-- Create RLS policies for studio_states
CREATE POLICY "Users can view their own studio states" ON studio_states
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own studio states" ON studio_states
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own studio states" ON studio_states
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own studio states" ON studio_states
    FOR DELETE USING (auth.uid()::text = user_id);

-- ============================================================================
-- 4. CREATE UPDATE TRIGGERS
-- ============================================================================

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
DROP TRIGGER IF EXISTS update_user_files_updated_at ON user_files;
CREATE TRIGGER update_user_files_updated_at
    BEFORE UPDATE ON user_files
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_processed_files_updated_at ON processed_files;
CREATE TRIGGER update_processed_files_updated_at
    BEFORE UPDATE ON processed_files
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_studio_states_updated_at ON studio_states;
CREATE TRIGGER update_studio_states_updated_at
    BEFORE UPDATE ON studio_states
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- 5. GRANT PERMISSIONS
-- ============================================================================

-- Grant all necessary permissions
GRANT ALL ON user_files TO authenticated;
GRANT ALL ON user_files TO service_role;
GRANT ALL ON processed_files TO authenticated;
GRANT ALL ON processed_files TO service_role;
GRANT ALL ON studio_states TO authenticated;
GRANT ALL ON studio_states TO service_role;

-- Grant sequence permissions
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO service_role;

-- ============================================================================
-- 6. VERIFICATION QUERIES
-- ============================================================================

-- Verify tables were created
SELECT 
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('user_files', 'processed_files', 'studio_states')
ORDER BY table_name;

-- Verify RLS is enabled
SELECT 
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('user_files', 'processed_files', 'studio_states')
ORDER BY tablename;

-- Verify policies exist
SELECT 
    schemaname,
    tablename,
    policyname,
    cmd
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename IN ('user_files', 'processed_files', 'studio_states')
ORDER BY tablename, policyname;

-- ============================================================================
-- SUCCESS MESSAGE
-- ============================================================================
DO $$
BEGIN
    RAISE NOTICE '✅ GuardiaVision Studio database setup completed successfully!';
    RAISE NOTICE '✅ Tables created: user_files, processed_files, studio_states';
    RAISE NOTICE '✅ RLS policies configured for security';
    RAISE NOTICE '✅ Indexes created for performance';
    RAISE NOTICE '✅ Studio refresh errors should now be fixed!';
END $$;
