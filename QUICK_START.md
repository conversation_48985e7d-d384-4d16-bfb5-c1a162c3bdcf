# Quick Start - Media Management Fixes

All fixes have been applied! Follow these steps to deploy:

## 1️⃣ Run Database Migrations (REQUIRED)

Open your Supabase SQL Editor and run these two files:

### Migration 1: Workflow Tracking
```bash
# File: supabase/migrations/20250119_add_workflow_status_to_user_files.sql
```
This adds the workflow status columns to track draft/completed files.

### Migration 2: Auto-Delete Function
```bash
# File: supabase/migrations/20250119_add_auto_delete_old_files.sql
```
This creates the function to delete files older than 30 days.

**How to run**:
1. Copy the SQL from each file
2. Paste into Supabase Dashboard → SQL Editor
3. Click "Run"

## 2️⃣ Deploy Your Code

```bash
# Test locally first
npm run build
npm run dev

# Check these URLs:
# - http://localhost:3000/dashboard/week
# - http://localhost:3000/dashboard/recent
# - http://localhost:3000/dashboard/draft
# - http://localhost:3000/dashboard/completed

# Then deploy to production
git add .
git commit -m "Fix media management issues"
git push
```

## 3️⃣ Setup Auto-Delete (Optional)

In Supabase SQL Editor:

```sql
-- Enable pg_cron extension (if not already enabled)
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Schedule daily cleanup at 2 AM
SELECT cron.schedule(
    'delete-old-files-daily',
    '0 2 * * *',
    $$SELECT delete_old_files()$$
);
```

## ✅ What's Fixed

### Issue 1: File Preview
- ✅ All files now have working preview images
- ✅ Older files can be viewed

### Issue 2: Date Filtering
- ✅ `/dashboard/week` shows last 7 days
- ✅ `/dashboard/recent` shows last 24 hours

### Issue 3: Workflow Status
- ✅ Opening file in studio → adds to Draft
- ✅ Saving from studio → moves to Completed

## 🧪 Quick Test

1. **Upload a file** → Should appear in dashboard
2. **Open in studio** → Should appear in `/dashboard/draft`
3. **Save from studio** → Should appear in `/dashboard/completed`
4. **Check week/recent** → Files should show based on upload date

## 📁 Files Changed

**Backend:**
- `/src/app/api/user/files/route.ts` - URL generation
- `/src/app/api/user/files/update-status/route.ts` - NEW API endpoint

**Frontend:**
- `/src/app/[locale]/(auth)/dashboard/week/page.tsx` - Rewritten
- `/src/app/[locale]/(auth)/dashboard/recent/page.tsx` - Rewritten
- `/src/app/[locale]/(auth)/dashboard/draft/page.tsx` - Rewritten
- `/src/app/[locale]/(auth)/dashboard/completed/page.tsx` - Rewritten
- `/src/components/studio/GuardiaVisionStudio.tsx` - Added status tracking

**Database:**
- 2 new migrations in `/supabase/migrations/`

## 🆘 If Something Breaks

**Files not showing in dashboard:**
- Check browser console for errors
- Verify migrations ran successfully
- Check Supabase logs

**Status not updating:**
- Open browser console
- Look for "File marked as draft" or "File marked as completed" messages
- Check `/api/user/files/update-status` endpoint exists

**Need help:**
- See `IMPLEMENTATION_COMPLETE.md` for full details
- See `MEDIA_MANAGEMENT_FIXES.md` for technical details

## 🎉 That's It!

Your media management system is now fully functional with:
- ✅ Working file previews
- ✅ Date-based filtering
- ✅ Draft/Completed workflow tracking
- ✅ Auto-delete old files (when cron is setup)

Ready to use! 🚀
